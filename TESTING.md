# WSSTun Testing Guide

## 🧪 Running Tests

All test-related files are located in the `tests/` directory.

### Quick Start

```bash
# 1. Enter test directory
cd tests

# 2. Run tests (automatically installs dependencies and compiles)
# Windows:
run_tests.bat
# Linux/macOS:
./run_tests.sh
```

### Detailed Instructions

Please see `tests/TEST_README.md` for complete test documentation and usage instructions.

## 📋 Test Coverage

- ✅ Server mode
- ✅ Client Forward mode (regular + Mux)
- ✅ Client Proxy mode (regular + Mux + SOCKS5 + HTTP)
- ✅ Hub Service mode
- ✅ Comprehensive scenarios and error handling

## 💡 Important Note

**All test commands must be run in the `tests/` directory.** 