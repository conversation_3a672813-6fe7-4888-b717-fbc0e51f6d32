# WSSTun 应用层心跳机制设计

## 📊 概述

本文档描述了WSSTun中Hub模式下的应用层心跳机制设计。该机制旨在解决负载均衡器环境下WebSocket连接保活问题。

## 1. 心跳机制核心原理

### 1.1 设计目标

**目的**：解决负载均衡器环境下WebSocket连接保活问题  
**范围**：仅在Hub模式下实现，Client-Server直连使用WebSocket原生ping/pong  
**方向**：Hub Service → Hub Server 单向心跳  
**协议**：应用层JSON消息，避免被LB拦截  

### 1.2 与现有机制的关系

```
三层保活机制：
1. TCP Keep-Alive        - 操作系统层面（默认2小时）
2. WebSocket Ping/Pong   - 协议层面（自动，可能被LB拦截）  
3. Application Heartbeat - 应用层面（30秒，穿透LB）
```

### 1.3 架构设计

```
Hub Service ──────单向心跳（30s）────→ Hub Server
     ↓                                        ↓
[发送心跳]                                [接收心跳]
[程序内重连]                              [超时检测]
[指数退避]                                [状态监控]
```

**关键特性**：
- **Hub Service端**：心跳失败时程序内自动重连，支持三阶段指数退避算法
- **Hub Server端**：被动接收心跳，超时检测，不主动发送心跳

## 2. 消息协议

### 2.1 心跳消息类型

```rust
// Hub Service → Hub Server (唯一的心跳消息)
#[serde(rename = "hubservice_heartbeat")]
HubServiceHeartbeat {
    service_id: String,
    timestamp: u64,        // UNIX timestamp
},
```

### 2.2 配置参数

```bash
# Hub Service 命令行参数
--heartbeat-interval <SECONDS>   # 心跳间隔，默认30秒
--heartbeat-timeout <SECONDS>    # 超时时间，默认90秒

# Hub Server 命令行参数
--heartbeat-timeout <SECONDS>    # 超时时间，默认90秒
```

## 3. 当前实现状态

### ✅ 已完成的功能

#### 3.1 Hub Service端自动重连机制
- ✅ **程序内重连**：心跳失败时不再 `std::process::exit(1)`，而是程序内自动重连
- ✅ **三阶段指数退避**：
  - 阶段1：快速重连（5次，指数退避：1s→2s→4s→8s→16s）
  - 阶段2：慢速重连（10次，固定30秒间隔）
  - 阶段3：持续重连（无限次，固定300秒间隔）
- ✅ **信号驱动架构**：使用watch通道进行重连信号通信，避免繁忙等待
- ✅ **连接成功重置**：重新连接成功后重置重连计数器

#### 3.2 配置管理
- ✅ **CLI参数支持**：所有心跳参数可通过命令行配置
- ✅ **配置传递**：配置正确传递到各个组件

#### 3.3 心跳发送
- ✅ **Hub Service心跳发送**：定期发送应用层心跳到Server
- ✅ **心跳接收**：Server端能接收和记录心跳消息

## 4. 🎯 待完成任务

### 4.1 Server端心跳监控增强 (优先级：P1)

#### 任务4.1.1：Hub Service状态跟踪
**目标**：为每个Hub Service维护独立心跳状态

```rust
// src/server_hub.rs
#[derive(Debug, Clone)]
struct HeartbeatStatus {
    last_heartbeat_received: Option<Instant>,
    heartbeat_receive_count: u64,
    consecutive_timeout_count: u32,
    is_healthy: bool,
    last_timeout_log_time: Option<Instant>, // 避免频繁日志
}

struct HubServiceState {
    sink: Arc<Mutex<SplitSink<WebSocket, Message>>>,
    clients: HashMap<String, Arc<Mutex<SplitSink<WebSocket, Message>>>>,
    heartbeat_status: HeartbeatStatus,  // 新增
}
```

#### 任务4.1.2：主动心跳健康检查
**目标**：Server端定期检查所有Hub Service心跳健康

```rust
fn start_heartbeat_health_checker(
    service_hub: Arc<RwLock<ServiceHub>>,
    check_interval: u64,        // 默认30秒
    heartbeat_timeout: u64,     // 默认90秒
) -> JoinHandle<()> {
    tokio::spawn(async move {
        let mut interval = tokio::time::interval(Duration::from_secs(check_interval));
        
        loop {
            interval.tick().await;
            let mut hub = service_hub.write().await;
            let now = Instant::now();
            
            let mut services_to_disconnect = Vec::new();
            
            for (service_id, hub_service) in &mut hub.hub_services {
                if let Some(last_heartbeat) = hub_service.heartbeat_status.last_heartbeat_received {
                    let elapsed = now.duration_since(last_heartbeat);
                    
                    if elapsed > Duration::from_secs(heartbeat_timeout) {
                        hub_service.heartbeat_status.consecutive_timeout_count += 1;
                        hub_service.heartbeat_status.is_healthy = false;
                        
                        // 连续超时3次后主动断开连接
                        if hub_service.heartbeat_status.consecutive_timeout_count >= 3 {
                            services_to_disconnect.push(service_id.clone());
                        }
                    }
                }
            }
            
            // 断开不健康的服务
            for service_id in services_to_disconnect {
                warn!("强制断开心跳超时的Hub Service: {}", service_id);
                hub.handle_hubservice_disconnect(&service_id).await;
            }
        }
    })
}
```

#### 任务4.1.3：心跳接收状态更新
**目标**：优化心跳接收时的状态更新

```rust
// 在handle_hubservice_messages中优化心跳处理
HubMessage::HubServiceHeartbeat { service_id, timestamp } => {
    let now = Instant::now();
    
    // 更新心跳状态
    if let Some(hub_service) = service_hub.write().await.hub_services.get_mut(&service_id) {
        hub_service.heartbeat_status.last_heartbeat_received = Some(now);
        hub_service.heartbeat_status.heartbeat_receive_count += 1;
        hub_service.heartbeat_status.consecutive_timeout_count = 0;
        hub_service.heartbeat_status.is_healthy = true;
    }
    
    debug!("收到Hub Service心跳: {} (时间戳: {})", service_id, timestamp);
}
```

### 4.2 监控和可观测性 (优先级：P2)

#### 任务4.2.1：HTTP监控端点
**目标**：提供RESTful API查询心跳状态

```rust
// src/server.rs
#[derive(Serialize)]
struct HeartbeatMetrics {
    total_services: usize,
    healthy_services: usize,
    unhealthy_services: usize,
    services: Vec<ServiceHeartbeatInfo>,
}

#[derive(Serialize)]
struct ServiceHeartbeatInfo {
    service_id: String,
    is_healthy: bool,
    last_heartbeat_ago_seconds: Option<u64>,
    heartbeat_receive_count: u64,
    consecutive_timeout_count: u32,
}

async fn get_heartbeat_metrics(
    Extension(service_hub): Extension<Arc<RwLock<ServiceHub>>>,
) -> Json<HeartbeatMetrics> {
    // 实现心跳状态查询
}
```

**API端点**：
```bash
GET /heartbeat
{
  "total_services": 5,
  "healthy_services": 4,
  "unhealthy_services": 1,
  "services": [
    {
      "service_id": "my-service",
      "is_healthy": true,
      "last_heartbeat_ago_seconds": 15,
      "heartbeat_receive_count": 120,
      "consecutive_timeout_count": 0
    }
  ]
}
```

#### 任务4.2.2：结构化日志优化
**目标**：统一心跳日志格式，便于监控

```rust
// 心跳相关的结构化日志宏
macro_rules! heartbeat_log {
    (received, $service_id:expr, $timestamp:expr) => {
        debug!(
            "[Heartbeat] action=received service_id={} timestamp={} status=ok", 
            $service_id, $timestamp
        );
    };
    (timeout, $service_id:expr, $elapsed:expr, $count:expr) => {
        warn!(
            "[Heartbeat] action=timeout service_id={} elapsed_sec={} consecutive_count={}", 
            $service_id, $elapsed, $count
        );
    };
}
```

### 4.3 性能优化 (优先级：P3)

#### 任务4.3.1：心跳批量处理
**目标**：优化多Hub Service的心跳处理性能

- 批量更新心跳状态，减少锁竞争
- 异步健康检查，避免阻塞主消息处理

#### 任务4.3.2：内存优化
**目标**：优化心跳状态存储

- 使用更紧凑的数据结构
- 定期清理过期的心跳记录

## 5. 负载均衡器兼容性

### 5.1 LB穿透原理
- 使用JSON文本消息而非WebSocket控制帧
- LB将心跳视为正常业务数据进行转发
- 避免LB对ping/pong帧的拦截或修改
- 定期数据流防止LB关闭空闲连接

### 5.2 推荐LB配置
```nginx
# Nginx配置示例
location /hub {
    proxy_pass http://wsstun_backend;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_read_timeout 300s;  # 大于心跳超时时间
    proxy_send_timeout 300s;
}
```

## 6. 实施计划

### 6.1 时间安排
- **阶段1**：Server端心跳监控增强（预计4-6小时）
- **阶段2**：监控端点和日志优化（预计2-3小时）
- **阶段3**：性能优化和测试（预计2-3小时）

### 6.2 验收标准
- ❌ Server端能够正确跟踪每个Hub Service的心跳状态
- ❌ 超时和连续失败时能够主动断开连接
- ❌ 提供HTTP API查询心跳状态
- ❌ 统一的结构化日志格式
- ❌ 性能测试通过（支持100+并发Hub Service）

## 7. 技术特点总结

### 7.1 单向心跳设计
- **简化架构**：只有Hub Service主动发送心跳，Server被动接收
- **减少网络开销**：避免双向心跳带来的额外流量
- **提高可靠性**：集中在Hub Service端进行连接管理

### 7.2 智能重连策略
- **三阶段退避**：快速恢复→慢速重试→长期保持
- **程序内重连**：避免进程重启带来的额外开销
- **状态保持**：重连期间保持服务状态，快速恢复

### 7.3 负载均衡器友好
- **应用层协议**：JSON消息穿透LB过滤层
- **可配置间隔**：根据LB配置调整心跳频率
- **容错设计**：适应网络抖动和临时中断

## 8. 成功指标

- **连接稳定性**：在LB环境下连接保持时间延长200%+
- **故障检测时间**：心跳超时检测时间在90秒内
- **假故障率**：减少因LB拦截ping/pong导致的误判90%+
- **可观测性**：心跳状态可通过HTTP API实时查询 