//! Handle /hub/forward WebSocket connections
//! Parse target addresses sent by clients and distribute target instructions to corresponding hub-services

use crate::common::{HubMessage, Result, TunnelError, ConnectionId};
use crate::server_hub::ServiceHub;
use crate::server_hub_dynamic::DynamicHubState;
use axum::{
    extract::{
        ws::{Message, WebSocket, WebSocketUpgrade}, Extension,
        Query,
    },
    http::StatusCode,
    response::IntoResponse,
};
use futures_util::{SinkExt, StreamExt};
use log::{debug, error, info, warn};

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;

// Handle WebSocket connection requests for Hub forwarding functionality
pub async fn ws_handler(
    ws: WebSocketUpgrade,
    _headers: axum::http::HeaderMap,
    Query(params): Query<HashMap<String, String>>,
    Extension(service_hub): Extension<Arc<RwLock<ServiceHub>>>,
    Extension(dynamic_hub_state): Extension<Arc<tokio::sync::Mutex<DynamicHubState>>>,
) -> impl IntoResponse {
    info!("Handling new WebSocket connection on /hub/forward route");

    // Get service_id and target from query parameters
    let service_id = params.get("service_id").cloned();
    let target_addr = params.get("target").cloned();

    if let (Some(service_id), Some(target_addr)) = (service_id, target_addr) {
        info!(
            "/hub/forward - Service ID: {}, Target: {}",
            service_id, target_addr
        );

        // Check if service is available - first check dynamic hub state, then check traditional hub state
        let is_available = {
            let dynamic_state = dynamic_hub_state.lock().await;
            if dynamic_state.is_service_available(&service_id) {
                true
            } else {
                let hub = service_hub.read().await;
                hub.is_service_available(&service_id)
            }
        };

        if !is_available {
            warn!("Service {} is not available", service_id);
            return StatusCode::SERVICE_UNAVAILABLE.into_response();
        }

        ws.on_upgrade(move |socket| {
            handle_hub_client_socket(
                socket,
                service_id,
                service_hub,
                dynamic_hub_state,
                HubClientType::Forward { target_addr },
            )
        })
    } else {
        warn!("Missing service_id or target parameter");
        StatusCode::BAD_REQUEST.into_response()
    }
}

/// Hub client type
#[derive(Debug, Clone)]
enum HubClientType {
    Forward { target_addr: String },
}

/// Unified Hub client handler
async fn handle_hub_client_socket(
    ws: WebSocket,
    service_id: String,
    service_hub: Arc<RwLock<ServiceHub>>,
    dynamic_hub_state: Arc<tokio::sync::Mutex<DynamicHubState>>,
    client_type: HubClientType,
) {
    let type_name = match &client_type {
        HubClientType::Forward { .. } => "Forward",
    };

    info!("Establishing Hub {} WebSocket connection, Service ID: {}", type_name, service_id);

    // Generate connection ID
    let connection_id = {
        let mut hub = service_hub.write().await;
        hub.generate_connection_id()
    };

    // Handle initialization logic based on type
    let ws = match client_type {
        HubClientType::Forward { target_addr } => {
            // Send forward instruction
            if let Err(e) = send_service_instruction(
                &service_hub,
                &dynamic_hub_state,
                &service_id,
                connection_id,
                "target",
                &target_addr,
            )
            .await
            {
                error!("Failed to send forward instruction: {}", e);
                return;
            }
            ws
        }
    };

    info!(
        "Sent {} instruction to service {}, Connection ID: {}",
        type_name, service_id, connection_id
    );

    // Register client connection
    let (ws_sink, ws_stream) = ws.split();
    let ws_sink = Arc::new(tokio::sync::Mutex::new(ws_sink));

    {
        let mut hub = service_hub.write().await;
        hub.register_client(
            connection_id.clone(),
            service_id.clone(),
            Arc::clone(&ws_sink),
        );
    }

    // Also register client-to-service mapping and client connection in dynamic hub state
    {
        let mut dynamic_state = dynamic_hub_state.lock().await;
        let client_id = format!("legacy_{}", connection_id);
        dynamic_state.register_client_to_service(client_id.clone(), service_id.clone());
        dynamic_state.register_client_connection(client_id, Arc::clone(&ws_sink));
    }

    info!("Registered client connection, Connection ID: {}", connection_id);

    // Start message handling
    if let Err(e) =
        handle_client_messages(ws_stream, connection_id, service_hub.clone(), dynamic_hub_state.clone()).await
    {
        error!("Failed to handle client messages: {}", e);
    }

    // Clean up connection
    {
        let mut hub = service_hub.write().await;
        hub.handle_client_disconnect(connection_id).await;
    }

    // Also clean up mappings in dynamic hub state
    {
        let mut dynamic_state = dynamic_hub_state.lock().await;
        let client_id = format!("legacy_{}", connection_id);
        dynamic_state.cleanup_client(&client_id);
    }

    info!("Hub {} connection disconnected, Connection ID: {}", type_name, connection_id);
}

/// Send service instruction to hub-service (shared function)
async fn send_service_instruction(
    service_hub: &Arc<RwLock<ServiceHub>>,
    dynamic_hub_state: &Arc<tokio::sync::Mutex<DynamicHubState>>,
    service_id: &str,
    connection_id: ConnectionId,
    instruction_type: &str,
    target_addr: &str,
) -> Result<()> {
    // First try to get connection from dynamic hub state
    let dynamic_hub_service_sink = {
        let dynamic_state = dynamic_hub_state.lock().await;
        dynamic_state.get_control_channel(service_id)
    };

    if let Some(sink) = dynamic_hub_service_sink {
        // For dynamic hub state, we send HubMessage::ServiceForwardInstruction
        let instruction = HubMessage::ServiceForwardInstruction {
            connection_id,
            instruction_type: instruction_type.to_string(),
            target_addr: target_addr.to_string(),
        };

        let json = serde_json::to_string(&instruction).map_err(|e| {
            error!("Failed to serialize instruction: {}", e);
            TunnelError::Other(format!("JSON serialization error: {}", e))
        })?;

        sink.lock()
            .await
            .send(Message::Text(json.into()))
            .await
            .map_err(|e| {
                error!("Failed to send instruction: {}", e);
                TunnelError::Other(format!("WebSocket send error: {}", e))
            })?;

        info!(
            "Sent {} instruction to dynamic hub service {}, Connection ID: {}",
            instruction_type, service_id, connection_id
        );
        return Ok(());
    }

    // If not found in dynamic hub state, try to get from traditional ServiceHub
    let hub_service_sink = {
        let hub = service_hub.read().await;
        hub.get_hubservice_sink(service_id)
    };

    if let Some(sink) = hub_service_sink {
        let instruction = HubMessage::ServiceForwardInstruction {
            connection_id,
            instruction_type: instruction_type.to_string(),
            target_addr: target_addr.to_string(),
        };

        let json = serde_json::to_string(&instruction).map_err(|e| {
            error!("Failed to serialize instruction: {}", e);
            TunnelError::Other(format!("JSON serialization error: {}", e))
        })?;

        sink.lock()
            .await
            .send(Message::Text(json.into()))
            .await
            .map_err(|e| {
                error!("Failed to send instruction: {}", e);
                TunnelError::Other(format!("WebSocket send error: {}", e))
            })?;

        info!(
            "Sent {} instruction to traditional hub service {}, Connection ID: {}",
            instruction_type, service_id, connection_id
        );
        Ok(())
    } else {
        error!("Service not found: {}", service_id);
        Err(TunnelError::ServiceUnavailable(format!(
            "Service '{}' is not available",
            service_id
        )))
    }
}

/// Handle client WebSocket messages (shared function)
async fn handle_client_messages(
    mut ws_stream: futures_util::stream::SplitStream<WebSocket>,
    connection_id: ConnectionId,
    service_hub: Arc<RwLock<ServiceHub>>,
    dynamic_hub_state: Arc<tokio::sync::Mutex<DynamicHubState>>,
) -> Result<()> {
    // Wait for data tunnel establishment
    let client_id = format!("legacy_{}", connection_id);
    
    // Give data tunnel some time to establish connection
    for attempt in 1..=50 { // Wait up to 5 seconds (50 * 100ms)
        {
            let dynamic_state = dynamic_hub_state.lock().await;
            if dynamic_state.is_tunnel_active(&client_id) {
                debug!("Data tunnel established, Client ID: {}", client_id);
                break;
            }
        }
        
        if attempt == 50 {
            warn!("Data tunnel establishment timeout, Client ID: {}", client_id);
            return Err(TunnelError::Other("Data tunnel establishment timeout".to_string()));
        }
        
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
    }
    
    while let Some(message) = ws_stream.next().await {
        let message = match message {
            Ok(msg) => msg,
            Err(e) => {
                error!("Error receiving WebSocket message: {}", e);
                return Err(TunnelError::Other(format!("WebSocket receive error: {}", e)));
            }
        };

        match message {
            Message::Binary(data) => {
                debug!(
                    "Received {} bytes of binary data from client {}",
                    data.len(),
                    connection_id
                );

                // Prioritize forwarding data through dynamic hub state
                let dynamic_forwarded = {
                    let dynamic_state = dynamic_hub_state.lock().await;
                    dynamic_state.forward_client_to_tunnel(client_id.clone(), data.to_vec()).await
                };

                if let Err(e) = dynamic_forwarded {
                    debug!("Dynamic hub forwarding failed: {}, trying traditional forwarding", e);
                    // If dynamic hub state forwarding fails, try traditional forwarding method
                    let hub = service_hub.read().await;
                    if let Err(e) = hub
                        .forward_client_to_hubservice(connection_id, data.to_vec())
                        .await
                    {
                        error!("Failed to forward data: {}", e);
                        return Err(e);
                    }
                } else {
                    debug!("Successfully forwarded data through dynamic hub state");
                }
            }
            Message::Text(text) => {
                debug!("Received text message from client {}: {}", connection_id, text);
                // Currently no special text message handling needed
            }
            Message::Close(_) => {
                info!("Client {} closed WebSocket connection", connection_id);
                break;
            }
            Message::Ping(_) => debug!("Received ping"),
            Message::Pong(_) => debug!("Received pong"),
        }
    }

    info!("Client message handling loop ended, Connection ID: {}", connection_id);
    Ok(())
}
