//! The main entry point of the application.
mod client;
mod client_mux;
mod common;
mod hub_service;
mod hub_service_dynamic;
mod hub_service_forward;
mod hub_service_proxy;
mod proxy_server;
mod server;
mod server_forward;
mod server_hub;
mod server_hub_dynamic;
mod server_mux;
mod server_proxy;
mod streamlit_client;
mod reconnect;
mod heartbeat;

use clap::{Parser, Subcommand};
use log::{error, info, LevelFilter};
use std::process;

#[global_allocator]
static GLOBAL: MiMalloc = MiMalloc;

use mimalloc::MiMalloc;

#[derive(Parser)]
#[command(name = "wsstun")]
#[command(about = "WebSocket Tunnel - A tool for creating secure tunnels over WebSocket connections")]
#[command(version = concat!(env!("CARGO_PKG_VERSION"), " (built on ", env!("BUILD_TIMESTAMP_CST"), ")"))]
struct Cli {
    #[command(subcommand)]
    command: Commands,

    /// Set log level
    #[arg(long, default_value = "info", global = true)]
    log_level: String,

    /// Enable mux mode
    #[arg(long, global = true)]
    use_mux: bool,
}

#[derive(clap::Args, Debug, Clone)]
struct ClientConnectionArgs {
    /// Server URL (e.g., ws://localhost:8080)
    #[arg(short, long)]
    server: String,

    /// Authentication username (optional)
    #[arg(long)]
    username: Option<String>,

    /// Authentication password (optional)
    #[arg(long)]
    password: Option<String>,
}


#[derive(Subcommand)]
enum Commands {
    /// Run in forward mode (port forwarding)
    Forward {
        #[clap(flatten)]
        conn_args: ClientConnectionArgs,

        /// Local address to listen on
        #[arg(short, long, default_value = "127.0.0.1:8080")]
        listen: String,
        /// Target address to forward to
        #[arg(short, long)]
        target: String,
        /// Service ID for hub mode (optional)
        #[arg(long)]
        service_id: Option<String>,
    },
    /// Run in proxy mode (SOCKS5/HTTP proxy)
    Proxy {
        #[clap(flatten)]
        conn_args: ClientConnectionArgs,

        /// Local address to listen on
        #[arg(short, long, default_value = "127.0.0.1:1080")]
        listen: String,
        /// Service ID for hub mode (optional)
        #[arg(long)]
        service_id: Option<String>,
    },
    /// Run as a client to a Streamlit Hub
    StreamlitClient {
        /// The public URL of the Streamlit Cloud application
        #[arg(long, default_value = "https://chatdemo.streamlit.app")]
        url: String,
    },
    /// Run in hub-service mode
    HubService {
        #[clap(flatten)]
        conn_args: ClientConnectionArgs,

        /// Service ID
        #[arg(long)]
        service_id: String,
        /// Heartbeat interval in seconds
        #[arg(long, default_value = "30")]
        heartbeat_interval: u64,
    },
    /// Run in server mode
    Server {
        /// Address to listen on
        #[arg(short, long, default_value = "127.0.0.1:8060")]
        listen: String,
        /// Maximum number of concurrent connections
        #[arg(long, default_value = "1000")]
        max_connections: usize,
        /// Keep alive duration in seconds
        #[arg(long, default_value = "60")]
        keep_alive: u64,
        /// DNS cache size
        #[arg(long, default_value = "1000")]
        dns_cache_size: usize,
        /// DNS cache TTL in seconds
        #[arg(long, default_value = "300")]
        dns_cache_ttl: u64,
        /// Connection timeout in seconds
        #[arg(long, default_value = "30")]
        connection_timeout: u64,
        /// Buffer size in bytes
        #[arg(long, default_value = "32768")]
        buffer_size: usize,
        /// Activity timeout in seconds
        #[arg(long, default_value = "300")]
        activity_timeout: u64,
        /// Authentication username (optional)
        #[arg(long)]
        username: Option<String>,
        /// Authentication password (optional)
        #[arg(long)]
        password: Option<String>,
    },
}

#[tokio::main]
async fn main() {
    let cli = Cli::parse();

    let log_level = match cli.log_level.to_lowercase().as_str() {
        "error" => LevelFilter::Error,
        "warn" => LevelFilter::Warn,
        "info" => LevelFilter::Info,
        "debug" => LevelFilter::Debug,
        "trace" => LevelFilter::Trace,
        _ => LevelFilter::Info,
    };

    env_logger::Builder::from_default_env()
        .filter_level(log_level)
        .init();

    let result = match &cli.command {
        Commands::Forward {
            conn_args,
            listen,
            target,
            service_id,
        } => {
            if cli.use_mux {
                info!("Running in client-mux mode (Forward)");
                client_mux::run_client_mux(client_mux::ClientMuxConfig {
                    listen_addr: listen.clone(),
                    server_url: conn_args.server.clone(),
                    target_addr: Some(target.clone()),
                    username: conn_args.username.clone(),
                    password: conn_args.password.clone(),
                    mux_pool_size: 4, // Default pool size
                })
                .await
            } else {
                info!("Running in client mode (Forward)");
                client::run_client(client::ClientConfig {
                    command_type: client::CommandType::Forward,
                    listen: listen.clone(),
                    server: conn_args.server.clone(),
                    target: Some(target.clone()),
                    service_id: service_id.clone(),
                    username: conn_args.username.clone(),
                    password: conn_args.password.clone(),
                    reconnect_attempts: 3,
                    reconnect_delay: 1000,
                    max_connections: Some(100),
                    connection_timeout: Some(30),
                    buffer_size: Some(64 * 1024),
                })
                .await
            }
        }
        Commands::Proxy {
            conn_args,
            listen,
            service_id,
        } => {
            if cli.use_mux {
                info!("Running in client-mux mode (Proxy)");
                client_mux::run_client_mux(client_mux::ClientMuxConfig {
                    listen_addr: listen.clone(),
                    server_url: conn_args.server.clone(),
                    target_addr: None,
                    username: conn_args.username.clone(),
                    password: conn_args.password.clone(),
                    mux_pool_size: 4, // Default pool size
                })
                .await
            } else {
                info!("Running in client mode (Proxy)");
                client::run_client(client::ClientConfig {
                    command_type: client::CommandType::Proxy,
                    listen: listen.clone(),
                    server: conn_args.server.clone(),
                    target: None,
                    service_id: service_id.clone(),
                    username: conn_args.username.clone(),
                    password: conn_args.password.clone(),
                    reconnect_attempts: 3,
                    reconnect_delay: 1000,
                    max_connections: Some(100),
                    connection_timeout: Some(30),
                    buffer_size: Some(64 * 1024),
                })
                .await
            }
        }
        Commands::StreamlitClient { url } => {
            info!("Running in streamlit-client mode");
            streamlit_client::StreamlitClient::run(url)
                .await
                .map_err(|e| crate::common::TunnelError::Other(e.to_string()))
        }
        Commands::HubService {
            conn_args,
            service_id,
            heartbeat_interval,
        } => {
            if cli.use_mux {
                info!("Running Hub-Service in MUX mode.");
                hub_service::run_hub_service(common::HubServiceConfig {
                    server: conn_args.server.clone(),
                    service_id: service_id.clone(),
                    heartbeat_interval: *heartbeat_interval,
                    username: conn_args.username.clone(),
                    password: conn_args.password.clone(),
                })
                .await
            } else {
                info!("Running Hub-Service in Dynamic Tunnel mode.");
                hub_service_dynamic::run_hub_service_dynamic(common::HubServiceDynamicConfig {
                    server: conn_args.server.clone(),
                    service_id: service_id.clone(),
                    heartbeat_interval: *heartbeat_interval,
                    username: conn_args.username.clone(),
                    password: conn_args.password.clone(),
                })
                .await
            }
        }
        Commands::Server {
            listen,
            max_connections,
            keep_alive,
            dns_cache_size,
            dns_cache_ttl,
            connection_timeout,
            buffer_size,
            activity_timeout,
            username,
            password,
        } => {
            info!("Starting server mode");
            server::run_server(server::ServerConfig {
                listen_addr: listen.clone(),
                max_connections: *max_connections as u32,
                keep_alive: *keep_alive > 0,
                dns_cache_size: *dns_cache_size as u32,
                dns_cache_ttl: *dns_cache_ttl,
                connection_timeout: *connection_timeout,
                buffer_size: *buffer_size,
                activity_timeout: *activity_timeout,
                username: username.clone(),
                password: password.clone(),
            })
            .await
        }
    };

    if let Err(e) = result {
        error!("Application error: {}", e);
        process::exit(1);
    }
}
