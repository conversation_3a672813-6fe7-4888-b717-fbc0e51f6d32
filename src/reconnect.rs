//! Common reconnection infrastructure

/// Reconnection state management
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub struct ReconnectState {
    pub attempt_count: u32,
    pub is_reconnecting: bool,
}

impl Default for ReconnectState {
    fn default() -> Self {
        Self {
            attempt_count: 0,
            is_reconnecting: false,
        }
    }
}

/// Reconnection signal enumeration
#[derive(<PERSON>bu<PERSON>, <PERSON>lone, PartialEq, Eq)]
pub enum ReconnectSignal {
    HeartbeatFailed,
    ConnectionLost,
    InitialConnectionFailed,
}


