use std::time::Duration;

use futures_util::{SinkExt, StreamExt};
use prost::Message;
use reqwest::header::{HeaderMap, HeaderValue, COOKIE};
use reqwest::Client;
use tokio_tungstenite::tungstenite::protocol::Message as WsMessage;

pub mod proto {
    #![allow(clippy::all)]
    include!(concat!(env!("OUT_DIR"), "/streamlit.rs"));
}

use self::proto::{
    back_msg, element, forward_msg, BackMsg, ClientState, ForwardMsg, WidgetStates,
};

pub struct StreamlitClient;

impl StreamlitClient {
    pub async fn run(url: &str) -> anyhow::Result<()> {
        let streamlit_host = url;
        let client = Client::builder()
            .timeout(Duration::from_secs(30))
            .build()?;

        // 1. Get initial cookies
        let response = client
            .get(format!("https://{}", streamlit_host))
            .send()
            .await?;
        let mut headers = HeaderMap::new();
        let cookies = response
            .cookies()
            .map(|c| format!("{}={}", c.name(), c.value()))
            .collect::<Vec<_>>()
            .join("; ");
        headers.insert(COOKIE, HeaderValue::from_str(&cookies)?);
        headers.insert(
            "X-Streamlit-Client",
            HeaderValue::from_static("streamlit-rust-client"),
        );

        // 2. Health check and WebSocket handshake
        let _ = client
            .get(format!("https://{}/healthz", streamlit_host))
            .headers(headers.clone())
            .send()
            .await?;

        let ws_url = format!("wss://{}/_stcore/stream", streamlit_host);

        let (mut ws, _) = tokio_tungstenite::connect_async(ws_url).await?;

        // 3. Send a RerunScript message
        let client_state = ClientState {
            query_string: "".to_string(),
            widget_states: Some(WidgetStates {
                widgets: Vec::new(),
            }),
            page_script_hash: "".to_string(),
            page_name: "".to_string(),
            fragment_id: "".to_string(),
            is_auto_rerun: false,
            cached_message_hashes: Vec::new(),
            context_info: None,
        };
        let back_msg = BackMsg {
            r#type: Some(back_msg::Type::RerunScript(client_state)),
            ..Default::default()
        };

        let mut buf = Vec::new();
        back_msg.encode(&mut buf)?;
        ws.send(WsMessage::Binary(buf)).await?;

        // 4. Listen for ForwardMsg
        while let Some(Ok(msg)) = ws.next().await {
            match msg {
                WsMessage::Binary(data) => {
                    let forward_msg = ForwardMsg::decode(&data[..])?;
                    if let Some(forward_msg::Type::Delta(delta)) = forward_msg.r#type {
                        if let Some(delta_type) = delta.r#type {
                            match delta_type {
                                self::proto::delta::Type::NewElement(element) => {
                                    if let Some(element_type) = element.r#type {
                                        match element_type {
                                            element::Type::Text(_text) => {
                                                // Handle text elements
                                            }
                                            _ => {}
                                        }
                                    }
                                }
                                _ => {}
                            }
                        }
                    }
                }
                _ => {}
            }
        }
        Ok(())
    }
}