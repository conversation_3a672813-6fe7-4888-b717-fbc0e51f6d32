use crate::common::{
    add_connection_id_prefix, deserialize_client_mux_message, extract_connection_id_and_payload,
    generate_origin_header, prepare_ws_url, serialize_client_mux_message, ClientMuxMessage,
    ConnectionId, ConnectionIdGenerator, Result, TunnelError, DEFAULT_BUFFER_SIZE,
};
use base64::{engine::general_purpose::STANDARD, Engine as _};
use futures_util::{
    sink::SinkExt,
    stream::StreamExt,
};
use log::{debug, error, info, warn};
use std::{
    collections::HashMap,
    sync::{
        atomic::{AtomicUsize, Ordering},
        Arc,
    },
    time::Duration,
};
use tokio::{
    io::{AsyncReadExt, AsyncWriteExt},
    net::{TcpListener, TcpStream},
    sync::{mpsc, Mutex, RwLock},
};
use tokio_tungstenite::{
    connect_async,
    tungstenite::{client::IntoClientRequest, Message},
    MaybeTlsStream, WebSocketStream,
};


/// Client Mux configuration
#[derive(Debug, Clone)]
pub struct ClientMuxConfig {
    pub listen_addr: String,     // Local listening address
    pub server_url: String,      // WebSocket server URL
    pub target_addr: Option<String>, // Target address (Forward mode)
    pub username: Option<String>, // Authentication username
    pub password: Option<String>, // Authentication password
    pub mux_pool_size: usize,    // Mux connection pool size
}

impl Default for ClientMuxConfig {
    fn default() -> Self {
        Self {
            listen_addr: "127.0.0.1:8080".to_string(),
            server_url: "wss://localhost:8060".to_string(),
            target_addr: None,
            username: None,
            password: None,
            mux_pool_size: 10,
        }
    }
}


/// Client Mux manager
pub struct ClientMuxManager {
    config: ClientMuxConfig,
    id_generator: ConnectionIdGenerator,
    stats: Arc<RwLock<MuxStats>>,

    // --- New connection pool and session management state ---
    session_id: Arc<RwLock<Option<u32>>>,
    // pool field removed
    // live_connections field removed
    
    /// Manually managed write operation pool, providing a sender for each connection's write task
    ws_sinks: Arc<RwLock<Vec<Option<mpsc::UnboundedSender<Message>>>>>,

    /// Atomic counter for round-robin distribution
    round_robin_counter: Arc<AtomicUsize>,

    /// Stores mapping from ConnectionId to live_connections index for upstream data routing
    stream_to_ws_map: Arc<Mutex<HashMap<ConnectionId, usize>>>,

    /// Stores mapping from ConnectionId to its downstream data channel sender
    active_streams: Arc<Mutex<HashMap<ConnectionId, mpsc::UnboundedSender<Vec<u8>>>>>,

    /// Stores channels waiting for connection responses
    pending_responses: Arc<Mutex<HashMap<ConnectionId, mpsc::UnboundedSender<bool>>>>,
}

impl Clone for ClientMuxManager {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            id_generator: self.id_generator.clone(),
            stats: self.stats.clone(),
            session_id: self.session_id.clone(),
            ws_sinks: self.ws_sinks.clone(),
            round_robin_counter: self.round_robin_counter.clone(),
            stream_to_ws_map: self.stream_to_ws_map.clone(),
            active_streams: self.active_streams.clone(),
            pending_responses: self.pending_responses.clone(),
        }
    }
}

/// Mux statistics information
#[derive(Debug, Clone)]
pub struct MuxStats {
    pub bytes_sent: u64,
    pub bytes_received: u64,
}

impl Default for MuxStats {
    fn default() -> Self {
        Self {
            bytes_sent: 0,
            bytes_received: 0,
        }
    }
}

impl ClientMuxManager {
    /// Create new client Mux manager
    pub fn new(config: ClientMuxConfig) -> Arc<Self> {
        // manager and pool creation logic removed
        
        // Initialize our manual pool with fixed size, initially empty
        let mut sinks = Vec::with_capacity(config.mux_pool_size);
        for _ in 0..config.mux_pool_size {
            sinks.push(None);
        }

        Arc::new(Self {
            config,
            id_generator: ConnectionIdGenerator::new(),
            stats: Arc::new(RwLock::new(MuxStats::default())),
            session_id: Arc::new(RwLock::new(None)),
            ws_sinks: Arc::new(RwLock::new(sinks)),
            round_robin_counter: Arc::new(AtomicUsize::new(0)),
            stream_to_ws_map: Arc::new(Mutex::new(HashMap::new())),
            active_streams: Arc::new(Mutex::new(HashMap::new())),
            pending_responses: Arc::new(Mutex::new(HashMap::new())),
        })
    }

    /// Run client Mux manager
    pub async fn run(self: &Arc<Self>) -> Result<()> {
        // Start local server
        let listen_addr = self.config.listen_addr.clone();
        let manager_arc = Arc::clone(self);
        let local_server_task = tokio::spawn(start_local_server(listen_addr, manager_arc));

        // Start guardian tasks for all connections
        for i in 0..self.config.mux_pool_size {
            let manager_clone = Arc::clone(self);
            tokio::spawn(async move {
                manager_clone.connection_guardian_task(i).await;
            });
        }

        // Wait for local server task to complete (theoretically it should run forever)
        local_server_task.await.map_err(|e| TunnelError::Other(format!("Local server task failed: {}", e)))?
    }

    /// Guardian task for a single WebSocket connection, responsible for connection, reading messages and reconnection
    async fn connection_guardian_task(self: &Arc<Self>, ws_index: usize) {
        loop {
            info!("Guardian task #{}: Establishing WebSocket connection...", ws_index);
            
            // Attempt to establish connection and perform initial handshake
            let ws_stream = match self.establish_and_handshake().await {
                Ok(stream) => {
                    info!("Guardian task #{}: WebSocket connection established and handshake completed.", ws_index);
                    stream
                }
                Err(e) => {
                    error!("Guardian task #{}: Failed to establish connection: {}. Will retry in 5 seconds...", ws_index, e);
                    // Clean up any existing old sink
                    self.ws_sinks.write().await[ws_index] = None;
                    tokio::time::sleep(Duration::from_secs(5)).await;
                    continue;
                }
            };
            
            // Split the stream
            let (ws_sink, mut ws_stream) = ws_stream.split();

            // Create a channel for sending data upstream
            let (upstream_tx, mut upstream_rx) = mpsc::unbounded_channel::<Message>();

            // Put the new sender into the pool
            self.ws_sinks.write().await[ws_index] = Some(upstream_tx);

            // Start dedicated write task
            let mut writer_task = tokio::spawn(async move {
                let mut ws_sink = ws_sink;
                while let Some(message) = upstream_rx.recv().await {
                    if ws_sink.send(message).await.is_err() {
                        break; // Send failed, exit write task
                    }
                }
            });

            // Handle read operations in current task
            loop {
                tokio::select! {
                    // Listen for downstream messages
                    Some(message_result) = ws_stream.next() => {
                        match message_result {
                            Ok(message) => {
                                // Handle received message
                                if self.handle_downstream_message(message).await.is_err() {
                                    // Processing failed, might indicate logic error, but we continue listening
                                }
                            }
                            Err(_) => {
                                // Read error means connection is dead
                                break;
                            }
                        }
                    }
                    // Listen for write task exit
                    _ = &mut writer_task => {
                        // Write task exit also means connection is dead
                        break;
                    }
                }
            }

            // If loop exits, connection is disconnected
            warn!("Guardian task #{}: WebSocket connection disconnected. Will start reconnection immediately.", ws_index);
            // Clean up sink so other tasks know this connection is unavailable
            self.ws_sinks.write().await[ws_index] = None;
        }
    }

    /// Establish a new WebSocket connection and complete initialization handshake
    async fn establish_and_handshake(&self) -> Result<WebSocketStream<MaybeTlsStream<TcpStream>>> {
        let mut url = prepare_ws_url(&self.config.server_url)?;
        url.set_path("/mux");
        let origin = generate_origin_header(&url);
        let mut request = url.into_client_request()?;
        request.headers_mut().insert("Origin", tokio_tungstenite::tungstenite::http::header::HeaderValue::from_str(&origin).map_err(|e| TunnelError::Other(format!("Invalid origin header: {}", e)))?);

        // Add authentication (if needed)
        if let (Some(username), Some(password)) = (&self.config.username, &self.config.password) {
            let auth = format!("{}:{}", username, password);
            let encoded = STANDARD.encode(auth.as_bytes());
            let auth_header = format!("Basic {}", encoded);
            request.headers_mut().insert("Authorization", tokio_tungstenite::tungstenite::http::header::HeaderValue::from_str(&auth_header).map_err(|e| TunnelError::Other(format!("Invalid auth header: {}", e)))?);
        }

        let (mut ws_stream, _) = connect_async(request).await.map_err(TunnelError::WebSocketError)?;
        
        // Send session initialization message
        let init_msg = if let Some(sid) = *self.session_id.read().await {
            ClientMuxMessage::JoinSession { session_id: sid }
        } else {
            ClientMuxMessage::InitializeSession
        };
        let msg_str = serialize_client_mux_message(&init_msg)?;
        ws_stream.send(Message::Text(msg_str)).await?;

        // Wait for session establishment response
        if let Some(Ok(Message::Text(response))) = ws_stream.next().await {
            if let Ok(ClientMuxMessage::SessionEstablished { session_id }) = deserialize_client_mux_message(&response) {
                // If this is the first established connection, set session ID
                let mut current_sid = self.session_id.write().await;
                if current_sid.is_none() {
                    *current_sid = Some(session_id);
                    info!("Session established, ID: {}", session_id);
                }
                return Ok(ws_stream);
            }
        }
        Err(TunnelError::Other("Session establishment failed".into()))
    }
    
    /// Unified handling of downstream messages from any connection
    async fn handle_downstream_message(&self, message: Message) -> Result<()> {
        match message {
            Message::Text(text) => {
                self.handle_control_message(&text).await
            }
            Message::Binary(data) => {
                // In the new model, we don't need ws_index because data is always associated with connection_id
                self.handle_data_frame(&data, 0).await 
            }
            Message::Ping(_) => Ok(()), // Handled automatically by tungstenite
            Message::Pong(_) => Ok(()),
            Message::Close(_) => Err(TunnelError::Other("Connection closed by server".into())),
            Message::Frame(_) => Ok(()), // Ignore raw frames
        }
    }

    async fn handle_local_connection(self: Arc<Self>, tcp_stream: TcpStream) -> Result<()> {
        let connection_id = self.id_generator.next_id();
        info!("Handling new local connection, assigned ID: {}", connection_id);

        // --- 1. Round-robin select a WebSocket connection ---
        let ws_index;
        let sender;

        // Loop until finding an available sink
        loop {
            let sinks = self.ws_sinks.read().await;
            if sinks.iter().all(|s| s.is_none()) {
                 return Err(TunnelError::ServiceUnavailable(
                    "No available WebSocket connections".into(),
                ));
            }
            let counter = self.round_robin_counter.fetch_add(1, Ordering::Relaxed);
            let index = counter % sinks.len();
            if let Some(s) = sinks[index].clone() {
                ws_index = index;
                sender = s;
                break;
            }
            // If slot is empty, sleep briefly and retry
            tokio::time::sleep(Duration::from_millis(100)).await;
        }

        info!("Connection {} will use WebSocket connection #{}", connection_id, ws_index);

        // --- 2. Create response waiting channel ---
        let (response_tx, mut response_rx) = mpsc::unbounded_channel::<bool>();
        self.pending_responses.lock().await.insert(connection_id, response_tx);
        debug!("Created response channel for connection {}", connection_id);

        // --- 3. Send connection request ---
        let request_msg = ClientMuxMessage::RequestNewStream {
            connection_id,
            target_addr: self.config.target_addr.clone(),
        };
        let msg_str = serialize_client_mux_message(&request_msg)?;
        sender.send(Message::Text(msg_str)).map_err(|e| TunnelError::Other(format!("Failed to send connection request: {}", e)))?;
        info!("Successfully sent RequestNewStream message to server, connection ID: {}", connection_id);

        debug!("Waiting for server response, connection ID: {}", connection_id);

        // --- 4. Wait for server response ---
        let success = tokio::select! {
            response = response_rx.recv() => {
                match response {
                    Some(success) => {
                        info!("Received server response, connection {} result: {}", connection_id, success);
                        success
                    }
                    None => {
                        error!("Channel closed while waiting for connection {} response", connection_id);
                        return Err(TunnelError::Other("Response channel closed".into()));
                    }
                }
            }
            _ = tokio::time::sleep(tokio::time::Duration::from_secs(10)) => {
                error!("Timeout waiting for connection {} response", connection_id);
                // Clean up pending response
                self.pending_responses.lock().await.remove(&connection_id);
                return Err(TunnelError::Other("Timeout waiting for server response".into()));
            }
        };

        if !success {
            error!("Server rejected connection request for connection {}", connection_id);
            return Err(TunnelError::Other("Server rejected connection request".into()));
        }

        info!("Connection {} successfully established, starting data forwarding", connection_id);

        // --- 5. Bind stream and connection ---
        self.stream_to_ws_map
            .lock()
            .await
            .insert(connection_id, ws_index);

        // --- 6. Set up bidirectional data forwarding ---
        let (read_half, mut write_half) = tcp_stream.into_split();
        let (tx, mut rx) = mpsc::unbounded_channel::<Vec<u8>>();

        // a. Store downstream channel sender
        self.active_streams
            .lock()
            .await
            .insert(connection_id, tx);

        // b. Downstream data task: receive data from channel and write to TCP
        tokio::spawn(async move {
            while let Some(data) = rx.recv().await {
                if write_half.write_all(&data).await.is_err() {
                    break;
                }
            }
        });

        // c. Upstream data task: read data from TCP and forward to WebSocket
        let self_clone = Arc::clone(&self);
        tokio::spawn(async move {
            let mut buffer = vec![0; DEFAULT_BUFFER_SIZE];
            let mut read_half = tokio::io::BufReader::new(read_half);

            loop {
                match read_half.read(&mut buffer).await {
                    Ok(0) => {
                        info!("Local TCP connection {} closed (EOF)", connection_id);
                        break;
                    }
                    Ok(n) => {
                        let data = &buffer[..n];
                        if let Err(e) = self_clone
                            .forward_data_to_ws(connection_id, data, ws_index)
                            .await
                        {
                            error!("Failed to forward upstream data: {}", e);
                            break;
                        }
                    }
                    Err(e) => {
                        error!(
                            "Error reading data from local TCP connection {}: {}",
                            connection_id, e
                        );
                        break;
                    }
                }
            }
            // Cleanup
            self_clone.remove_connection(connection_id).await;

            // Send CloseStream message to server
            if let Err(e) = self_clone.send_close_stream_message(connection_id, ws_index).await {
                error!("Failed to send CloseStream message: {}", e);
            }
        });

        Ok(())
    }

    async fn forward_data_to_ws(
        &self,
        connection_id: ConnectionId,
        data: &[u8],
        ws_index: usize,
    ) -> Result<()> {
        let data_with_id = add_connection_id_prefix(connection_id, data);
        let message = Message::Binary(data_with_id);

        let sinks = self.ws_sinks.read().await;
        if let Some(Some(sender)) = sinks.get(ws_index) {
            sender.send(message).map_err(|e| TunnelError::Other(format!("Failed to send data: {}", e)))?;
            let mut stats = self.stats.write().await;
            stats.bytes_sent += data.len() as u64;
        } else {
            return Err(TunnelError::ConnectionNotFound(connection_id));
        }
        Ok(())
    }

    /// Handle control messages
    async fn handle_control_message(&self, message: &str) -> Result<()> {
        debug!("Handling control message: {}", message);
        
        let control_msg: ClientMuxMessage = deserialize_client_mux_message(message)?;
        debug!("Successfully parsed control message: {:?}", control_msg);

        match control_msg {
            ClientMuxMessage::NewStreamResponse {
                connection_id,
                success,
                error_message,
            } => {
                info!("Received new stream response: connection_id={}, success={}, error={:?}", 
                      connection_id, success, error_message);
                      
                // Notify waiting connection
                if let Some(tx) = self.pending_responses.lock().await.remove(&connection_id) {
                    debug!("Found waiting connection {}, sending result: {}", connection_id, success);
                    if let Err(_) = tx.send(success) {
                        warn!("Unable to send response result to connection {}", connection_id);
                    }
                } else {
                    warn!("Received response for unknown connection {}", connection_id);
                }
            }
            ClientMuxMessage::CloseStream { connection_id } => {
                info!("Received close stream message: connection_id={}", connection_id);
                self.remove_connection(connection_id).await;
            }
            ClientMuxMessage::Heartbeat { .. } => {
                debug!("Received heartbeat message");
                // Client can ignore heartbeat messages or reply
            }
            _ => {
                warn!("Received unhandled control message: {:?}", control_msg);
            }
        }
        Ok(())
    }

    /// Handle data frames
    async fn handle_data_frame(&self, data: &[u8], _ws_index: usize) -> Result<()> {
        let (connection_id, payload) = extract_connection_id_and_payload(data)?;

        let streams = self.active_streams.lock().await;
        if let Some(tx) = streams.get(&connection_id) {
            if tx.send(payload.to_vec()).is_err() {
                error!(
                    "Unable to send data to local TCP connection {} (channel closed)",
                    connection_id
                );
                // Channel closed means receiver (TCP write task) has terminated, we need to cleanup
                drop(streams); // Release lock
                self.remove_connection(connection_id).await;
            } else {
                let mut stats = self.stats.write().await;
                stats.bytes_received += payload.len() as u64;
            }
        } else {
            warn!("Received data for unknown or closed connection {}", connection_id);
        }

        Ok(())
    }



    async fn remove_connection(&self, connection_id: ConnectionId) {
        self.stream_to_ws_map.lock().await.remove(&connection_id);
        self.active_streams.lock().await.remove(&connection_id);
        self.pending_responses.lock().await.remove(&connection_id);
    }

    /// Send CloseStream message to server
    async fn send_close_stream_message(&self, connection_id: ConnectionId, ws_index: usize) -> Result<()> {
        let close_msg = ClientMuxMessage::CloseStream { connection_id };
        let msg_str = serialize_client_mux_message(&close_msg)?;

        let sinks = self.ws_sinks.read().await;
        if let Some(Some(sender)) = sinks.get(ws_index) {
            sender.send(Message::Text(msg_str)).map_err(|e| {
                TunnelError::Other(format!("Failed to send CloseStream message: {}", e))
            })?;
            debug!("Sent CloseStream message, connection ID: {}", connection_id);
        } else {
            warn!("Unable to send CloseStream message, WebSocket connection {} unavailable", ws_index);
        }

        Ok(())
    }
}

pub async fn run_client_mux(config: ClientMuxConfig) -> Result<()> {
    let manager = ClientMuxManager::new(config);

    info!("Starting Client Mux Manager");
    manager.run().await
}

async fn start_local_server(listen_addr: String, manager: Arc<ClientMuxManager>) -> Result<()> {
    let listener = TcpListener::bind(&listen_addr)
        .await
        .map_err(TunnelError::IoError)?;
    info!("Client Mux listening on: {}", listen_addr);

    loop {
        match listener.accept().await {
            Ok((tcp_stream, _)) => {
                let manager_clone = Arc::clone(&manager);
                tokio::spawn(async move {
                    if let Err(e) = manager_clone.handle_local_connection(tcp_stream).await {
                        error!("Error handling local connection: {}", e);
                    }
                });
            }
            Err(e) => {
                error!("Failed to accept local connection: {}", e);
            }
        }
    }
} 