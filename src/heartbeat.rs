//! Generic client heartbeat mechanism
use crate::common::{HubMessage, ReconnectSignal};
use futures_util::stream::SplitSink;
use futures_util::SinkExt;
use log::{debug, error, info};
use std::sync::Arc;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tokio::sync::{watch, Mutex};
use tokio::task::<PERSON><PERSON><PERSON><PERSON><PERSON>;
use tokio_tungstenite::{tungstenite::Message, MaybeTlsStream, WebSocketStream};
use tokio::net::TcpStream;

/// Start heartbeat sending task
pub fn start_heartbeat_task(
    ws_sink: Arc<Mutex<SplitSink<WebSocketStream<MaybeTlsStream<TcpStream>>, Message>>>,
    service_id: String,
    interval_secs: u64,
    reconnect_tx: watch::Sender<Option<ReconnectSignal>>,
) -> Jo<PERSON><PERSON><PERSON><PERSON><()> {
    tokio::spawn(async move {
        let mut interval = tokio::time::interval(Duration::from_secs(interval_secs));

        loop {
            interval.tick().await;

            let timestamp = SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs();

            let heartbeat = HubMessage::HubServiceHeartbeat {
                service_id: service_id.clone(),
                timestamp,
            };

            let json = match serde_json::to_string(&heartbeat) {
                Ok(json) => json,
                Err(e) => {
                    error!("Failed to serialize heartbeat message: {}", e);
                    continue;
                }
            };

            let mut sink = ws_sink.lock().await;
            match sink.send(Message::Text(json.into())).await {
                Ok(_) => {
                    debug!("Heartbeat sent, service ID: {}", service_id);
                }
                Err(e) => {
                    error!("Failed to send heartbeat, service ID: {}: {}", service_id, e);
                    // Send reconnect signal instead of direct exit
                    if let Err(e) = reconnect_tx.send(Some(ReconnectSignal::HeartbeatFailed)) {
                        error!("Failed to send reconnect signal: {}", e);
                    } else {
                        info!("Sent heartbeat failure reconnect signal, service ID: {}", service_id);
                    }
                    break;
                }
            }
        }
    })
} 