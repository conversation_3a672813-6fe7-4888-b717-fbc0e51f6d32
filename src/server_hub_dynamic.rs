//! Server-side dynamic tunnel support module
//!
//! This module implements server-side support for dynamic tunnel mode, including:
//! - Control channel management (/hub/dynamic)
//! - Data tunnel coordination (/hub/data)
//! - Client connection management

use crate::common::{
    ControlMessage, DataTunnelMessage, Result, TunnelError, HubMessage,
    deserialize_control_message, deserialize_data_tunnel_message,
    serialize_data_tunnel_message,
    connect_websocket_with_retry, prepare_ws_url,
};
use axum::{
    extract::{
        ws::{Message, WebSocket, WebSocketUpgrade},
        Extension,
    },
    response::IntoResponse,
};
use futures_util::{SinkExt, StreamExt};
use log::{debug, error, info, warn};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::Mutex;
use bytes::Bytes;
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use tokio::net::TcpStream;
use tokio_tungstenite::tungstenite::Message as TungsteniteMessage;


/// Dynamic tunnel server-side state management
#[derive(Default, Debug)]
pub struct DynamicHubState {
    /// Registered Hub control channels: hub_id -> WebSocket sender
    control_channels: HashMap<String, Arc<Mutex<futures_util::stream::SplitSink<WebSocket, Message>>>>,
    /// Clients waiting for data tunnel connection: client_id -> (client WebSocket sender, client WebSocket receiver)
    pending_clients: HashMap<String, (Arc<Mutex<futures_util::stream::SplitSink<WebSocket, Message>>>, futures_util::stream::SplitStream<WebSocket>)>,
    /// Active data tunnels: client_id -> data tunnel WebSocket sender
    active_tunnels: HashMap<String, Arc<Mutex<futures_util::stream::SplitSink<WebSocket, Message>>>>,
    /// Client connection ID to service ID mapping (for legacy forward requests)
    client_to_service: HashMap<String, String>,
    /// Client connections: client_id -> client WebSocket sender (for legacy forward request response data forwarding)
    client_connections: HashMap<String, Arc<Mutex<futures_util::stream::SplitSink<WebSocket, Message>>>>,
}

impl DynamicHubState {
    pub fn new() -> Self {
        Self::default()
    }

    /// Check if service is available
    pub fn is_service_available(&self, service_id: &str) -> bool {
        self.control_channels.contains_key(service_id)
    }

    /// Get list of available services
    pub fn get_available_services(&self) -> Vec<String> {
        self.control_channels.keys().cloned().collect()
    }

    /// Get control channel connection
    pub fn get_control_channel(&self, service_id: &str) -> Option<Arc<Mutex<futures_util::stream::SplitSink<WebSocket, Message>>>> {
        self.control_channels.get(service_id).cloned()
    }

    /// Register client to service mapping (for legacy forward requests)
    pub fn register_client_to_service(&mut self, client_id: String, service_id: String) {
        self.client_to_service.insert(client_id, service_id);
    }

    /// Register client connection (for legacy forward request response data forwarding)
    pub fn register_client_connection(&mut self, client_id: String, client_sink: Arc<Mutex<futures_util::stream::SplitSink<WebSocket, Message>>>) {
        self.client_connections.insert(client_id, client_sink);
    }

    /// Register data tunnel
    pub fn register_data_tunnel(&mut self, client_id: String, tunnel_sink: Arc<Mutex<futures_util::stream::SplitSink<WebSocket, Message>>>) {
        self.active_tunnels.insert(client_id, tunnel_sink);
    }

    /// Forward data to corresponding data tunnel
    pub async fn forward_client_to_tunnel(&self, client_id: String, data: Vec<u8>) -> Result<()> {
        if let Some(tunnel_sink) = self.active_tunnels.get(&client_id) {
            debug!("Forwarding {} bytes of data to data tunnel, client: {}", data.len(), client_id);
            
            tunnel_sink
                .lock()
                .await
                .send(Message::Binary(Bytes::from(data)))
                .await
                .map_err(|e| {
                    error!("Failed to forward data to data tunnel: {}", e);
                    TunnelError::Other(format!("WebSocket send error: {}", e))
                })?;
            
            Ok(())
        } else {
            error!("Data tunnel not found for client {}", client_id);
            Err(TunnelError::ServiceUnavailable(format!(
                "Data tunnel for client '{}' not found",
                client_id
            )))
        }
    }

    /// Forward data to corresponding client connection (for legacy forward request response data forwarding)
    pub async fn forward_tunnel_to_client(&self, client_id: String, data: Vec<u8>) -> Result<()> {
        if let Some(client_sink) = self.client_connections.get(&client_id) {
            debug!("Forwarding {} bytes of response data to client: {}", data.len(), client_id);
            
            client_sink
                .lock()
                .await
                .send(Message::Binary(Bytes::from(data)))
                .await
                .map_err(|e| {
                    error!("Failed to forward data to client: {}", e);
                    TunnelError::Other(format!("WebSocket send error: {}", e))
                })?;
            
            Ok(())
        } else {
            error!("Client connection not found for client {}", client_id);
            Err(TunnelError::ServiceUnavailable(format!(
                "Client connection '{}' not found",
                client_id
            )))
        }
    }

    /// Clean up client connection
    pub fn cleanup_client(&mut self, client_id: &str) {
        self.client_to_service.remove(client_id);
        self.active_tunnels.remove(client_id);
        self.pending_clients.remove(client_id);
        self.client_connections.remove(client_id);
    }
    
    /// Check if data tunnel is active
    pub fn is_tunnel_active(&self, client_id: &str) -> bool {
        self.active_tunnels.contains_key(client_id)
    }
}

/// Control channel WebSocket handler (/hub/dynamic)
pub async fn dynamic_control_handler(
    ws: WebSocketUpgrade,
    Extension(state): Extension<Arc<Mutex<DynamicHubState>>>,
) -> impl IntoResponse {
    ws.on_upgrade(move |socket| handle_control_channel(socket, state))
}

/// Data tunnel WebSocket handler (/hub/data)
pub async fn dynamic_data_handler(
    ws: WebSocketUpgrade,
    Extension(state): Extension<Arc<Mutex<DynamicHubState>>>,
) -> impl IntoResponse {
    ws.on_upgrade(move |socket| handle_data_tunnel(socket, state))
}

/// Handle control channel connection
async fn handle_control_channel(socket: WebSocket, state: Arc<Mutex<DynamicHubState>>) {
    info!("New control channel connection established");
    
    let (sink, mut stream) = socket.split();
    let sink = Arc::new(Mutex::new(sink));
    
    // Wait for registration message
    if let Some(Ok(Message::Text(text))) = stream.next().await {
        match deserialize_control_message(&text) {
            Ok(ControlMessage::RegisterHub { hub_id }) => {
                info!("Hub '{}' control channel registered", hub_id);
                
                // Register control channel
                {
                    let mut state_guard = state.lock().await;
                    state_guard.control_channels.insert(hub_id.clone(), sink.clone());
                }
                
                // Listen for other control messages from Hub
                while let Some(message) = stream.next().await {
                    match message {
                        Ok(Message::Text(text)) => {
                            if let Err(e) = handle_hub_control_message(&text, &state, &hub_id).await {
                                error!("Failed to handle Hub control message: {}", e);
                            }
                        }
                        Ok(Message::Close(_)) => {
                            info!("Hub '{}' control channel closed", hub_id);
                            break;
                        }
                        Ok(_) => {
                            // Ignore other message types
                        }
                        Err(e) => {
                            error!("Control channel message receive error: {}", e);
                            break;
                        }
                    }
                }
                
                // Clean up registered control channel
                {
                    let mut state_guard = state.lock().await;
                    state_guard.control_channels.remove(&hub_id);
                }
                info!("Hub '{}' control channel disconnected", hub_id);
            }
            _ => {
                warn!("Control channel received invalid registration message");
            }
        }
    } else {
        warn!("Control channel did not receive valid registration message");
    }
}

/// Handle data tunnel connection
async fn handle_data_tunnel(socket: WebSocket, state: Arc<Mutex<DynamicHubState>>) {
    info!("New data tunnel connection established");
    
    let (sink, mut stream) = socket.split();
    let sink = Arc::new(Mutex::new(sink));
    
    // Wait for data tunnel registration message
    if let Some(Ok(Message::Text(text))) = stream.next().await {
        match deserialize_data_tunnel_message(&text) {
            Ok(DataTunnelMessage::RegisterDataTunnel { client_id }) => {
                info!("Data tunnel registered for client '{}'", client_id);
                
                // First check if this is a pending client connection (true dynamic tunnel client)
                let client_connection = {
                    let mut state_guard = state.lock().await;
                    state_guard.pending_clients.remove(&client_id)
                };
                
                if let Some((client_sink, mut client_stream)) = client_connection {
                    info!("Found pending client '{}', starting data forwarding", client_id);
                    
                    // Start bidirectional data forwarding (true dynamic tunnel client)
                    let upload_task = {
                        let mut client_sink = client_sink.lock_owned().await;
                        tokio::spawn(async move {
                            while let Some(message) = stream.next().await {
                                match message {
                                    Ok(msg) => {
                                        if client_sink.send(msg).await.is_err() {
                                            debug!("Client connection closed, stopping upstream forwarding");
                                            break;
                                        }
                                    }
                                    Err(e) => {
                                        error!("Data tunnel receive error: {}", e);
                                        break;
                                    }
                                }
                            }
                        })
                    };
                    
                    let download_task = tokio::spawn(async move {
                        while let Some(message) = client_stream.next().await {
                            match message {
                                Ok(msg) => {
                                    if sink.lock().await.send(msg).await.is_err() {
                                        debug!("Data tunnel connection closed, stopping downstream forwarding");
                                        break;
                                    }
                                }
                                Err(e) => {
                                    error!("Client receive error: {}", e);
                                    break;
                                }
                            }
                        }
                    });
                    
                    // Wait for either direction to end
                    tokio::select! {
                        _ = upload_task => debug!("Client '{}' upstream transmission ended", client_id),
                        _ = download_task => debug!("Client '{}' downstream transmission ended", client_id),
                    }
                } else {
                    // This is a data tunnel from legacy forward request
                    info!("Registering data tunnel, client ID: {}", client_id);
                    
                    // Register data tunnel in active_tunnels
                    {
                        let mut state_guard = state.lock().await;
                        state_guard.register_data_tunnel(client_id.clone(), sink.clone());
                    }
                    
                    // Listen for response data from hub-service
                    // This data needs to be forwarded to corresponding client connection
                    // For legacy forward requests, data forwarding is bidirectional:
                    // Client -> /hub/forward -> data tunnel -> hub-service -> target
                    // Response data: target -> hub-service -> data tunnel -> /hub/forward -> client
                    
                    while let Some(message) = stream.next().await {
                        match message {
                            Ok(Message::Close(_)) => {
                                info!("Data tunnel '{}' closed", client_id);
                                break;
                            }
                            Ok(Message::Binary(data)) => {
                                debug!("Data tunnel '{}' received {} bytes from hub-service", client_id, data.len());
                                // Forward data to corresponding client connection
                                let state_guard = state.lock().await;
                                if let Err(e) = state_guard.forward_tunnel_to_client(client_id.clone(), data.to_vec()).await {
                                    error!("Failed to forward response data to client: {}", e);
                                    break;
                                }
                            }
                            Ok(_) => {
                                // Ignore other message types
                            }
                            Err(e) => {
                                error!("Data tunnel receive error: {}", e);
                                break;
                            }
                        }
                    }
                    
                    // Clean up data tunnel registration
                    {
                        let mut state_guard = state.lock().await;
                        state_guard.cleanup_client(&client_id);
                    }
                }
            }
            Err(e) => {
                error!("Unable to parse data tunnel registration message: {}", e);
            }
        }
    } else {
        warn!("Data tunnel registration failed: pending client '{}' not found", "unknown");
    }
}

/// Handle control messages from Hub
async fn handle_hub_control_message(
    text: &str,
    state: &Arc<Mutex<DynamicHubState>>,
    hub_id: &str,
) -> Result<()> {
    // First try to parse as ControlMessage (dynamic tunnel specific message)
    if let Ok(control_msg) = deserialize_control_message(text) {
        debug!("Received control message: {:?}", control_msg);
        
        match control_msg {
            ControlMessage::PrepareNewTunnel { client_id: _ } => {
                warn!("Received PrepareNewTunnel message, but this feature is not currently supported");
            }
            ControlMessage::TunnelReady { tunnel_id, client_id } => {
                debug!("Received tunnel ready message: tunnel_id={}, client_id={}", tunnel_id, client_id);
            }
            ControlMessage::TunnelCreationFailed { client_id, reason } => {
                error!("Hub '{}' reported tunnel creation failed for client '{}': {}", hub_id, client_id, reason);
            }
            ControlMessage::RegisterHub { .. } => {
                debug!("Received register Hub message echo");
            }
        }
        
        return Ok(());
    }

    // If not ControlMessage, try to parse as HubMessage (unified message format)
    if let Ok(hub_msg) = serde_json::from_str::<HubMessage>(text) {
        debug!("Received Hub message: {:?}", hub_msg);
        
        match hub_msg {
            HubMessage::HubServiceHeartbeat { service_id, timestamp } => {
                debug!("Received heartbeat message: service_id={}, timestamp={}", service_id, timestamp);
                // Heartbeat messages don't need special handling, just confirm connection is active
            }
            HubMessage::ServiceForwardInstruction { connection_id, instruction_type, target_addr } => {
                info!("Received forward instruction: connection_id={}, type={}, target={}", 
                    connection_id, instruction_type, target_addr);
                
                // Handle forward instruction
                if instruction_type == "target" {
                    // Create a tunnel for this connection
                    let client_id = format!("legacy_{}", connection_id);
                    
                    // Create tunnel task
                    let tunnel_task = create_tunnel_task(
                        client_id.clone(),
                        target_addr,
                        state.clone(),
                    );
                    
                    tokio::spawn(tunnel_task);
                }
            }
            _ => {
                warn!("Received unsupported Hub message type");
            }
        }
        
        return Ok(());
    }

    // If neither, log error
    warn!("Received unparseable control message: {}", text);
    Ok(())
}

/// Create tunnel task
async fn create_tunnel_task(
    client_id: String,
    target_addr: String,
    state: Arc<Mutex<DynamicHubState>>,
) -> Result<()> {
    info!("Creating tunnel task, client ID: {}, target: {}", client_id, target_addr);
    
    // Connect to target server
    let target_stream = match TcpStream::connect(&target_addr).await {
        Ok(stream) => stream,
        Err(e) => {
            error!("Unable to connect to target {}: {}", target_addr, e);
            
            // Send tunnel creation failed message
            let failure_msg = ControlMessage::TunnelCreationFailed {
                client_id: client_id.clone(),
                reason: format!("Unable to connect to local target: {}", e),
            };
            
            // This should be sent to Hub, but for simplicity, we log the error first
            debug!("Should send tunnel creation failed message: {:?}", failure_msg);
            
            return Err(TunnelError::Other(format!("Failed to connect to target: {}", e)));
        }
    };

    // Connect to data tunnel endpoint
    let server_url = "ws://127.0.0.1:8060"; // This should be obtained from configuration
    let clean_server_url = crate::common::trim_server_url_slashes(server_url);
    let data_ws_url = prepare_ws_url(&format!("{}/hub/data", clean_server_url))?;
    
    let data_ws_stream = match connect_websocket_with_retry(&data_ws_url, 3, 1000, None).await {
        Ok(stream) => stream,
        Err(e) => {
            error!("Unable to connect to data tunnel endpoint: {}", e);
            return Err(e);
        }
    };
    
    let (mut data_ws_sink, mut data_ws_stream) = data_ws_stream.split();
    
    // Register data tunnel
    let register_msg = DataTunnelMessage::RegisterDataTunnel {
        client_id: client_id.clone(),
    };
    
    let json_str = serialize_data_tunnel_message(&register_msg)?;
    data_ws_sink.send(TungsteniteMessage::Text(json_str)).await
        .map_err(|e| TunnelError::Other(format!("Failed to send data tunnel registration message: {}", e)))?;
    
    info!("Data tunnel registration successful, client ID: {}", client_id);
    
    // Start bidirectional data forwarding
    let (mut target_read, mut target_write) = target_stream.into_split();
    
    let upload_task = tokio::spawn(async move {
        while let Some(message) = data_ws_stream.next().await {
            match message {
                Ok(TungsteniteMessage::Binary(data)) => {
                    if let Err(e) = target_write.write_all(&data).await {
                        error!("Failed to write to target server: {}", e);
                        break;
                    }
                }
                Ok(TungsteniteMessage::Close(_)) => {
                    debug!("Data tunnel closed");
                    break;
                }
                Ok(_) => {} // Ignore other messages
                Err(e) => {
                    error!("Data tunnel receive error: {}", e);
                    break;
                }
            }
        }
    });
    
    let download_task = tokio::spawn(async move {
        loop {
            let mut buffer = vec![0u8; 32768];
            match target_read.read(&mut buffer).await {
                Ok(0) => {
                    debug!("Target connection closed");
                    break;
                }
                Ok(n) => {
                    let data = &buffer[..n];
                    if let Err(e) = data_ws_sink.send(TungsteniteMessage::Binary(data.to_vec())).await {
                        error!("Failed to send data to data tunnel: {}", e);
                        break;
                    }
                }
                Err(e) => {
                    error!("Failed to read data from target: {}", e);
                    break;
                }
            }
        }
    });
    
    // Wait for either direction to end
    tokio::select! {
        _ = upload_task => debug!("Client '{}' upstream transmission ended", client_id),
        _ = download_task => debug!("Client '{}' downstream transmission ended", client_id),
    }
    
    // Clean up state
    {
        let mut state_guard = state.lock().await;
        state_guard.cleanup_client(&client_id);
    }
    
    Ok(())
}

 