//! Server-side implementation for Mux mode
//!
//! Handles connections from `wsstun client mux` and forwards them to target addresses.

use crate::common::{
    add_connection_id_prefix, extract_connection_id_and_payload, serialize_client_mux_message, ClientMuxMessage, ConnectionId,
    ConnectionIdGenerator, Result, TunnelError, deserialize_client_mux_message,
};
use crate::proxy_server::connect_to_target;
use crate::server::{ServerConfig, AppState};
use axum::{
    extract::{
        ws::{Message, WebSocket, WebSocketUpgrade},
        Extension, State,
    },
    response::IntoResponse,
};
use futures_util::{stream::SplitSink, SinkExt, StreamExt};
use log::{debug, error, info, warn};
use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::Arc;
use std::time::Instant;
use tokio::io::{AsyncReadExt, AsyncWriteExt, <PERSON>Half, WriteHalf};
use tokio::net::TcpStream;
use tokio::sync::{Mutex, RwLock};
use tokio::task::JoinHandle;

/// Connection state
#[derive(Debug)]
pub enum ConnectionState {
    /// Connecting to target address
    Connecting,
    /// Connected, contains TCP writer and forwarding task handle
    Connected {
        tcp_writer: Arc<Mutex<WriteHalf<TcpStream>>>,
        task_handle: JoinHandle<()>,
    },
}

impl std::fmt::Debug for ClientSession {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("ClientSession")
            .field("session_id", &self.session_id)
            .field("config", &self.config)
            .field("id_generator", &self.id_generator.current_count())
            .field("connections", &"Arc<Mutex<HashMap<...>>>") // Omitted for brevity
            .field("pending_data", &"Arc<Mutex<HashMap<...>>>") // Omitted for brevity
            .field("last_heartbeat", &self.last_heartbeat)
            .field("ws_senders", &"Arc<RwLock<Vec<...>>>") // Omitted for brevity
            .field("stream_to_ws_map", &"Arc<Mutex<HashMap<...>>>") // Omitted for brevity
            .field("stats", &self.stats)
            .finish()
    }
}

/// Client session manager
pub struct ClientSession {
    /// Session ID
    pub session_id: u32,
    /// Global configuration
    config: Arc<ServerConfig>,
    /// Connection ID generator
    id_generator: ConnectionIdGenerator,
    /// All active connection states
    connections: Arc<Mutex<HashMap<ConnectionId, ConnectionState>>>,
    /// Cache pending data for connections being established
    pending_data: Arc<Mutex<HashMap<ConnectionId, Vec<Vec<u8>>>>>,
    /// Last heartbeat time
    last_heartbeat: Arc<RwLock<Instant>>,
    /// Collection of senders to client WebSockets
    ws_senders: Arc<RwLock<Vec<Arc<Mutex<SplitSink<WebSocket, Message>>>>>>,
    /// Routing map from data streams to WebSocket connections
    stream_to_ws_map: Arc<Mutex<HashMap<ConnectionId, usize>>>,
    /// Session statistics
    stats: Arc<Mutex<SessionStats>>,
}

/// Session statistics
#[derive(Debug, Clone)]
pub struct SessionStats {
    pub total_streams: u64,
    pub active_streams: u64,
    pub failed_streams: u64,
    pub bytes_sent: u64,
    pub bytes_received: u64,
    pub heartbeats_received: u64,
}

impl Default for SessionStats {
    fn default() -> Self {
        Self {
            total_streams: 0,
            active_streams: 0,
            failed_streams: 0,
            bytes_sent: 0,
            bytes_received: 0,
            heartbeats_received: 0,
        }
    }
}

impl ClientSession {
    pub fn new(session_id: u32, config: Arc<ServerConfig>) -> Self {
        Self {
            session_id,
            config,
            id_generator: ConnectionIdGenerator::new(),
            connections: Arc::new(Mutex::new(HashMap::new())),
            pending_data: Arc::new(Mutex::new(HashMap::new())),
            last_heartbeat: Arc::new(RwLock::new(Instant::now())),
            ws_senders: Arc::new(RwLock::new(Vec::new())),
            stream_to_ws_map: Arc::new(Mutex::new(HashMap::new())),
            stats: Arc::new(Mutex::new(SessionStats::default())),
        }
    }

    /// Add a new WebSocket connection sender to the session, returns connection index
    pub async fn add_sender(&self, sender: Arc<Mutex<SplitSink<WebSocket, Message>>>) -> usize {
        let mut senders = self.ws_senders.write().await;
        senders.push(sender);
        senders.len() - 1 // Return index of newly added connection
    }

    /// Update heartbeat time
    pub async fn update_heartbeat(&self) {
        *self.last_heartbeat.write().await = Instant::now();
        let mut stats = self.stats.lock().await;
        stats.heartbeats_received += 1;
    }



    /// Remove connection stream and clean up related resources
    pub async fn remove_connection(&self, connection_id: ConnectionId) {
        if let Some(conn_state) = self.connections.lock().await.remove(&connection_id) {
            if let ConnectionState::Connected { task_handle, .. } = conn_state {
                task_handle.abort();
            }
            // Clean up any pending data
            self.pending_data.lock().await.remove(&connection_id);
            // Clean up routing map
            self.stream_to_ws_map.lock().await.remove(&connection_id);
            
            let mut stats = self.stats.lock().await;
            stats.active_streams = stats.active_streams.saturating_sub(1);
            debug!("Removed connection {} from session, active: {}", connection_id, stats.active_streams);
        }
    }

    /// Handle control messages
    async fn handle_control_message(self: Arc<Self>, text: &str, ws_sender: Arc<Mutex<SplitSink<WebSocket, Message>>>, ws_index: usize) -> Result<()> {
        debug!("Starting to handle control message, ws_index: {}, message content: {}", ws_index, text);
        
        let control_msg: ClientMuxMessage = deserialize_client_mux_message(text)?;

        debug!("Successfully parsed control message: {:?}", control_msg);

        match control_msg {
            ClientMuxMessage::RequestNewStream { connection_id, target_addr } => {
                info!("Processing RequestNewStream for connection {} to target {:?}", connection_id, target_addr);
                
                let target = match target_addr {
                    Some(addr) => addr,
                    None => {
                        let msg = "No target address specified by client for new stream request";
                        error!("{}", msg);
                        self.send_response_specific(ws_sender, new_stream_response(connection_id, false, Some(msg.to_string()))).await?;
                        return Ok(());
                    }
                };

                // Record routing mapping
                self.stream_to_ws_map.lock().await.insert(connection_id, ws_index);
                info!("Recorded route mapping: connection {} -> ws_index {}", connection_id, ws_index);

                // Immediately set state to Connecting to prevent data loss
                self.connections.lock().await.insert(connection_id, ConnectionState::Connecting);
                let mut stats = self.stats.lock().await;
                stats.total_streams += 1;
                stats.active_streams += 1;
                drop(stats);

                // Handle connection process asynchronously
                let session_clone = self.clone();
                tokio::spawn(async move {
                    info!("Starting async connection process for connection {}", connection_id);
                    if let Err(e) = session_clone.clone().process_new_stream_request(connection_id, target, ws_sender).await {
                        error!("Failed to process new stream for conn {}: {}", connection_id, e);
                        session_clone.remove_connection(connection_id).await;
                    }
                });
            }
            ClientMuxMessage::Heartbeat { .. } => {
                debug!("Handling heartbeat message");
                self.update_heartbeat().await;
            }
            ClientMuxMessage::CloseStream { connection_id } => {
                info!("Handling close stream message, connection_id: {}", connection_id);
                self.remove_connection(connection_id).await;
            }
            // Server should not receive these messages
            ClientMuxMessage::InitializeSession | ClientMuxMessage::JoinSession { .. } => {
                warn!("Server received unexpected session message: {:?}", control_msg);
            }
            _ => warn!("Received unhandled control message: {:?}", control_msg),
        }
        Ok(())
    }

    /// Handle binary data frames
    async fn handle_binary_frame(self: Arc<Self>, data: Vec<u8>) -> Result<()> {
        let (connection_id, payload_slice) = extract_connection_id_and_payload(&data)?;
        let payload = payload_slice.to_vec(); // Copy immediately to avoid data races

        // Only clone the needed part, not the entire enum, to avoid cloning JoinHandle
        let writer_clone = {
            let connections = self.connections.lock().await;
            if let Some(ConnectionState::Connected { tcp_writer, .. }) = connections.get(&connection_id) {
                Some(tcp_writer.clone())
            } else {
                None
            }
        };

        if let Some(tcp_writer_arc) = writer_clone {
            if let Err(e) = tcp_writer_arc.lock().await.write_all(&payload).await {
                error!("Failed to write to TCP stream for conn {}: {}", connection_id, e);
                self.remove_connection(connection_id).await;
                return Err(e.into());
            }
            self.stats.lock().await.bytes_sent += payload.len() as u64;
        } else {
            // If no writer, check if connection is in progress
            let is_connecting = self.connections.lock().await.get(&connection_id)
                .map_or(false, |s| matches!(s, ConnectionState::Connecting { .. }));

            if is_connecting {
                debug!("Caching data for connecting stream {}", connection_id);
                self.pending_data.lock().await.entry(connection_id).or_default().push(payload);
            } else {
                warn!("Received data for unknown connection ID: {}", connection_id);
            }
        }
        Ok(())
    }

    /// Start data forwarding from TCP to WebSocket
    async fn forward_tcp_to_ws(self: Arc<Self>, connection_id: ConnectionId, mut tcp_reader: ReadHalf<TcpStream>) -> Result<()> {
        let mut buffer = vec![0u8; self.config.buffer_size];
        loop {
            let n = tcp_reader.read(&mut buffer).await?;
            if n == 0 {
                info!("TCP connection {} closed by remote", connection_id);
                break;
            }
            let data = &buffer[..n];
            let prefixed_data = add_connection_id_prefix(connection_id, data);
            
            // Use routing map to send data to specific WebSocket connection
            let ws_index = {
                let map = self.stream_to_ws_map.lock().await;
                map.get(&connection_id).copied()
            };

            if let Some(ws_index) = ws_index {
                let senders = self.ws_senders.read().await;
                if let Some(sender) = senders.get(ws_index) {
                    let message = Message::Binary(prefixed_data.into());
                    if let Err(e) = sender.lock().await.send(message).await {
                        error!("Failed to send data to client {} in session {}: {}", ws_index, self.session_id, e);
                        break;
                    }
                } else {
                    error!("WebSocket sender index {} not found for connection {}", ws_index, connection_id);
                    break;
                }
            } else {
                error!("No WebSocket route found for connection {}", connection_id);
                break;
            }
            
            self.stats.lock().await.bytes_received += n as u64;
        }
        Ok(())
    }

    /// Asynchronously handle the establishment process of new streams
    async fn process_new_stream_request(self: Arc<Self>, connection_id: ConnectionId, target_addr: String, ws_sender: Arc<Mutex<SplitSink<WebSocket, Message>>>) -> Result<()> {
        info!("Starting connection process for {} to target {}", connection_id, target_addr);
        
        let addr: SocketAddr = match target_addr.parse() {
            Ok(addr) => addr,
            Err(e) => {
                let err_msg = format!("Invalid target address '{}': {}", target_addr, e);
                error!("{}", err_msg);
                self.send_response_specific(ws_sender, new_stream_response(connection_id, false, Some(err_msg.clone()))).await?;
                return Err(TunnelError::Other(err_msg));
            }
        };

        info!("Parsed target address: {}", addr);

        match connect_to_target(addr.ip().to_string().as_str(), addr.port()).await {
            Ok(tcp_stream) => {
                info!("Successfully connected to target {} for connection {}", addr, connection_id);
                self.send_response_specific(ws_sender, new_stream_response(connection_id, true, None)).await?;
                info!("Sent successful response for connection {}", connection_id);
                
                let (tcp_reader, tcp_writer) = tokio::io::split(tcp_stream);
                let tcp_writer_arc = Arc::new(Mutex::new(tcp_writer));

                let forwarder_session = self.clone(); // Clone Arc for forwarding task
                let task_handle = tokio::spawn(async move {
                    if let Err(e) = forwarder_session.clone().forward_tcp_to_ws(connection_id, tcp_reader).await { // Clone for forwarding
                        warn!("TCP->WS forwarder for conn {} ended with error: {}", connection_id, e);
                    }
                    
                    // Send close notification to specific WebSocket connection
                    let ws_index = {
                        let map = forwarder_session.stream_to_ws_map.lock().await;
                        map.get(&connection_id).copied()
                    };
                    
                    if let Some(ws_index) = ws_index {
                        let senders = forwarder_session.ws_senders.read().await;
                        if let Some(sender) = senders.get(ws_index) {
                            let close_msg = ClientMuxMessage::CloseStream { connection_id };
                            if let Ok(json) = serialize_client_mux_message(&close_msg) {
                                let message = Message::Text(json.into());
                                if let Err(e) = sender.lock().await.send(message).await {
                                    warn!("Failed to send CloseStream notification for conn {}: {}", connection_id, e);
                                }
                            }
                        }
                    }
                });

                // Replay cached data
                if let Some(pending) = self.pending_data.lock().await.remove(&connection_id) {
                    debug!("Replaying {} cached data packets for conn {}", pending.len(), connection_id);
                    let mut writer = tcp_writer_arc.lock().await;
                    for data in pending {
                        if let Err(e) = writer.write_all(&data).await {
                             error!("Error replaying cached data for conn {}: {}", connection_id, e);
                             // If replay fails, close connection
                             self.remove_connection(connection_id).await;
                             return Err(e.into());
                        }
                    }
                }

                // Update connection state to Connected
                // Note: No longer need #[derive(Clone)] on ConnectionState
                let new_state = ConnectionState::Connected {
                    tcp_writer: tcp_writer_arc,
                    task_handle,
                };
                self.connections.lock().await.insert(connection_id, new_state);
                info!("Connection {} is now in Connected state", connection_id);
            }
            Err(e) => {
                let err_msg = format!("Failed to connect to target '{}': {}", target_addr, e);
                error!("{}", err_msg);
                self.stats.lock().await.failed_streams += 1;
                self.send_response_specific(ws_sender, new_stream_response(connection_id, false, Some(err_msg.clone()))).await?;
                return Err(TunnelError::Other(err_msg));
            }
        }
        Ok(())
    }



    /// Send response to specific client WebSocket
    async fn send_response_specific(&self, ws_sender: Arc<Mutex<SplitSink<WebSocket, Message>>>, response: ClientMuxMessage) -> Result<()> {
        let msg_str = serialize_client_mux_message(&response)?;
        ws_sender.lock().await.send(Message::Text(msg_str.into())).await.map_err(Into::into)
    }
    

}


pub async fn ws_handler(
    ws: WebSocketUpgrade,
    Extension(config): Extension<Arc<ServerConfig>>,
    State(state): State<Arc<RwLock<AppState>>>,
) -> impl IntoResponse {
    ws.on_upgrade(move |socket| handle_mux_connection(socket, config, state))
}

async fn handle_mux_connection(socket: WebSocket, config: Arc<ServerConfig>, state: Arc<RwLock<AppState>>) {
    let (ws_sender, mut ws_receiver) = socket.split();
    let ws_sender = Arc::new(Mutex::new(ws_sender));

    debug!("Starting to handle new WebSocket connection");

    let session: Arc<ClientSession> = match ws_receiver.next().await {
        Some(Ok(Message::Text(text))) => {
            debug!("Received initial text message: {}", text);
            match deserialize_client_mux_message(&text) {
                Ok(ClientMuxMessage::InitializeSession) => {
                    debug!("Handling InitializeSession message");
                    let mut state_guard = state.write().await;
                    let session_id: u32 = loop {
                        let id: u32 = rand::random::<u32>();
                        if !state_guard.sessions.contains_key(&id) {
                            break id;
                        }
                    };
                    
                    let new_session = Arc::new(ClientSession::new(session_id, config.clone()));
                    state_guard.sessions.insert(session_id, new_session.clone());
                    
                    let response = ClientMuxMessage::SessionEstablished { session_id };
                    if let Ok(msg_str) = serialize_client_mux_message(&response) {
                        debug!("Sending SessionEstablished response: {}", msg_str);
                        if ws_sender.lock().await.send(Message::Text(msg_str.into())).await.is_err() {
                            error!("Failed to send SessionEstablished response");
                            return; 
                        }
                    }
                    info!("New session initialized with ID: {}", session_id);
                    new_session
                },
                Ok(ClientMuxMessage::JoinSession { session_id }) => {
                    debug!("Handling JoinSession message, session_id: {}", session_id);
                    let state_guard = state.read().await;
                    if let Some(existing_session) = state_guard.sessions.get(&session_id) {
                        info!("Client joined existing session: {}", session_id);
                        existing_session.clone()
                    } else {
                        error!("Client tried to join non-existent session: {}", session_id);
                        return;
                    }
                },
                Ok(msg) => {
                    error!("First message from client was unexpected: {:?}", msg);
                    return;
                }
                Err(e) => {
                    error!("Failed to deserialize initial message: {}", e);
                    return;
                }
            }
        }
        Some(Ok(msg)) => {
            error!("First message from client was not text: {:?}", msg);
            return;
        }
        Some(Err(e)) => {
            error!("Error receiving initial message: {}", e);
            return;
        }
        None => {
            error!("Connection closed before receiving initial message");
            return;
        }
    };

    let ws_index = session.add_sender(ws_sender.clone()).await;
    info!("WebSocket connection added to session {}, index: {}", session.session_id, ws_index);

    while let Some(msg) = ws_receiver.next().await {
        match msg {
            Ok(Message::Text(text)) => {
                debug!("Received text message: {}", text);
                let session_clone = session.clone();
                let ws_sender_clone = ws_sender.clone();
                 tokio::spawn(async move {
                    if let Err(e) = session_clone.handle_control_message(&text, ws_sender_clone, ws_index).await {
                        error!("Error handling control message: {}", e);
                    }
                });
            }
            Ok(Message::Binary(data)) => {
                debug!("Received binary message, length: {}", data.len());
                let session_clone = session.clone();
                 tokio::spawn(async move {
                    if let Err(e) = session_clone.handle_binary_frame(data.to_vec()).await {
                        error!("Error handling binary frame: {}", e);
                    }
                });
            }
            Ok(Message::Ping(_)) => {
                // axum will automatically handle pong
                debug!("Received Ping");
            }
            Ok(Message::Pong(_)) => {
                debug!("Received Pong");
                session.update_heartbeat().await;
            }
            Ok(Message::Close(c)) => {
                info!("Client requested close: {:?}", c);
                break;
            }
            Err(e) => {
                error!("WebSocket error: {}", e);
                break;
            }
        }
    }

    info!("WebSocket connection for session {} closed.", session.session_id);
    // Note: We don't immediately clean up the session here, session lifecycle is managed by AppState
    // Sessions are only cleaned up when all related connections are disconnected, or after long periods without heartbeat
}


/// Create a new stream response message
fn new_stream_response(
    connection_id: ConnectionId,
    success: bool,
    error_message: Option<String>,
) -> ClientMuxMessage {
    ClientMuxMessage::NewStreamResponse {
        connection_id,
        success,
        error_message,
    }
} 