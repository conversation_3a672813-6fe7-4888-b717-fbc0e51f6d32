use crate::common::prepare_ws_url;
use crate::common::{Result, TunnelError};
use base64::{engine::general_purpose::STANDARD, Engine as _};
use futures_util::{SinkExt, StreamExt};
use log::{debug, error, info, warn};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use tokio::net::TcpStream;
use tokio::select;
use tokio::sync::{RwLock, Semaphore};
use tokio::time::{sleep, timeout};
use tokio_tungstenite::{tungstenite::Message, MaybeTlsStream, WebSocketStream};
use url::{form_urlencoded, Url};

/// Command type enumeration
#[derive(Debug, Clone)]
pub enum CommandType {
    Forward,
    Proxy,
}

/// Connection statistics
#[derive(Debug, Default, Clone)]
pub struct ConnectionStats {
    pub total_connections: u64,
    pub active_connections: u64,
    pub failed_connections: u64,
    pub reconnections: u64,
    pub bytes_sent: u64,
    pub bytes_received: u64,
    pub last_connection: Option<Instant>,
}

impl ConnectionStats {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn increment_total(&mut self) {
        self.total_connections += 1;
        self.last_connection = Some(Instant::now());
    }

    pub fn increment_active(&mut self) {
        self.active_connections += 1;
    }

    pub fn decrement_active(&mut self) {
        self.active_connections = self.active_connections.saturating_sub(1);
    }

    pub fn increment_failed(&mut self) {
        self.failed_connections += 1;
    }

    pub fn increment_reconnections(&mut self) {
        self.reconnections += 1;
    }

    pub fn add_bytes_sent(&mut self, bytes: u64) {
        self.bytes_sent += bytes;
    }

    pub fn add_bytes_received(&mut self, bytes: u64) {
        self.bytes_received += bytes;
    }

    pub fn log_stats(&self) {
        info!(
            "Connection Stats - Total: {}, Active: {}, Failed: {}, Reconnections: {}, Sent: {}KB, Received: {}KB",
            self.total_connections,
            self.active_connections,
            self.failed_connections,
            self.reconnections,
            self.bytes_sent / 1024,
            self.bytes_received / 1024
        );
    }
}

/// Connection pool state
struct ConnectionPool {
    semaphore: Arc<Semaphore>,
    stats: Arc<RwLock<ConnectionStats>>,
}

impl ConnectionPool {
    fn new(max_connections: u32, _connection_timeout_secs: u64) -> Self {
        Self {
            semaphore: Arc::new(Semaphore::new(max_connections as usize)),
            stats: Arc::new(RwLock::new(ConnectionStats::new())),
        }
    }

    async fn acquire_connection(&self) -> Result<ConnectionPoolGuard> {
        let permit = Arc::clone(&self.semaphore)
            .acquire_owned()
            .await
            .map_err(|_| TunnelError::Other("Unable to acquire connection pool permit".to_string()))?;

        {
            let mut stats = self.stats.write().await;
            stats.increment_total();
            stats.increment_active();
        }

        Ok(ConnectionPoolGuard {
            _permit: permit,
            stats: Arc::clone(&self.stats),
        })
    }

    async fn get_stats(&self) -> ConnectionStats {
        self.stats.read().await.clone()
    }
}

/// Connection pool guard, ensures proper resource release when connection ends
struct ConnectionPoolGuard {
    _permit: tokio::sync::OwnedSemaphorePermit,
    stats: Arc<RwLock<ConnectionStats>>,
}

impl Drop for ConnectionPoolGuard {
    fn drop(&mut self) {
        let stats = Arc::clone(&self.stats);
        tokio::spawn(async move {
            let mut stats = stats.write().await;
            stats.decrement_active();
        });
    }
}

/// Optimized client configuration structure
pub struct ClientConfig {
    pub command_type: CommandType,  // Forward | Proxy
    pub listen: String,             // Listen address
    pub server: String,             // WebSocket server URL (without path)
    pub target: Option<String>,     // Target address (only needed for forward)
    pub service_id: Option<String>, // Service ID (Hub mode)
    pub username: Option<String>,   // Authentication username
    pub password: Option<String>,   // Authentication password
    pub reconnect_attempts: u32,    // Reconnection attempts
    pub reconnect_delay: u64,       // Reconnection delay (milliseconds)
    // New performance configuration parameters
    pub max_connections: Option<u32>,    // Maximum concurrent connections
    pub connection_timeout: Option<u64>, // Connection timeout (seconds)
    pub buffer_size: Option<usize>,      // Buffer size
}

impl Default for ClientConfig {
    fn default() -> Self {
        Self {
            command_type: CommandType::Forward,
            listen: "127.0.0.1:8080".to_string(),
            server: "wss://localhost:8060".to_string(),
            target: None,
            service_id: None,
            username: None,
            password: None,
            reconnect_attempts: 3,
            reconnect_delay: 1000,
            max_connections: Some(100),
            connection_timeout: Some(30),
            buffer_size: Some(64 * 1024), // 64KB
        }
    }
}

/// Optimized client main function
pub async fn run_client(config: ClientConfig) -> Result<()> {
    let listener = crate::common::setup_tcp_listener(&config.listen).await?;

    info!(
        "Optimized Client listening on {} (mode: {:?}, target: {:?}, service_id: {:?})",
        &config.listen, &config.command_type, &config.target, &config.service_id
    );

    let ws_url = Arc::new(build_websocket_url(&config)?);
    let target_addr = Arc::new(config.target.clone());
    let auth_header = if let (Some(username), Some(password)) = (&config.username, &config.password)
    {
        let auth = format!("{}:{}", username, password);
        let encoded = STANDARD.encode(auth.as_bytes());
        Some(format!("Basic {}", encoded))
    } else {
        None
    };
    let auth_header = Arc::new(auth_header);
    let command_type = Arc::new(config.command_type.clone());

    // Create connection pool
    let connection_pool = Arc::new(ConnectionPool::new(
        config.max_connections.unwrap_or(100),
        config.connection_timeout.unwrap_or(30),
    ));

    // Create shared configuration
    let shared_config = Arc::new(OptimizedConfig {
        reconnect_attempts: config.reconnect_attempts,
        reconnect_delay_ms: config.reconnect_delay,
        buffer_size: config.buffer_size.unwrap_or(64 * 1024),
    });

    // Start statistics monitoring task
    start_stats_monitoring(Arc::clone(&connection_pool));

    loop {
        match listener.accept().await {
            Ok((local_tcp_stream, addr)) => {
                info!("Accepted connection from {}", addr);

                // Acquire connection pool permit
                let pool_guard = match connection_pool.acquire_connection().await {
                    Ok(guard) => guard,
                    Err(e) => {
                        error!("Unable to acquire connection pool permit: {}", e);
                        continue;
                    }
                };

                let ws_url_clone = Arc::clone(&ws_url);
                let target_addr_clone = Arc::clone(&target_addr);
                let auth_header_clone = Arc::clone(&auth_header);
                let command_type_clone = Arc::clone(&command_type);
                let shared_config_clone = Arc::clone(&shared_config);
                let stats = Arc::clone(&connection_pool.stats);

                tokio::spawn(async move {
                    let _guard = pool_guard; // Ensure guard is valid throughout the async task

                    if let Err(e) = handle_optimized_connection(
                        local_tcp_stream,
                        ws_url_clone,
                        target_addr_clone,
                        auth_header_clone,
                        command_type_clone,
                        shared_config_clone,
                        stats,
                    )
                    .await
                    {
                        error!("Optimized connection handling error: {}", e);
                    }
                });
            }
            Err(e) => {
                error!("Error accepting connection: {}", e);
            }
        }
    }
}

/// Optimized configuration structure
#[derive(Debug, Clone)]
struct OptimizedConfig {
    reconnect_attempts: u32,
    reconnect_delay_ms: u64,
    buffer_size: usize,
}

/// Start statistics monitoring task
fn start_stats_monitoring(connection_pool: Arc<ConnectionPool>) {
    tokio::spawn(async move {
        let mut interval = tokio::time::interval(Duration::from_secs(60)); // Record statistics every minute

        loop {
            interval.tick().await;

            let stats = connection_pool.get_stats().await;
            info!("=== Client Performance Statistics ===");
            stats.log_stats();
        }
    });
}

/// Build WebSocket URL logic (optimized version)
fn build_websocket_url(config: &ClientConfig) -> Result<Url> {
    let base_path = match config.command_type {
        CommandType::Forward => {
            if config.service_id.is_some() {
                "/hub/forward"
            } else {
                "/forward"
            }
        }
        CommandType::Proxy => {
            if config.service_id.is_some() {
                "/hub/proxy"
            } else {
                "/proxy"
            }
        }
    };

    // Ensure server URL doesn't end with slash to avoid double slash issues
    let server_url = crate::common::trim_server_url_slashes(&config.server);
    let mut final_url = format!("{}{}", server_url, base_path);

    // Add query parameters
    let mut query_params = Vec::new();

    if let Some(service_id) = &config.service_id {
        query_params.push(("service_id", service_id.as_str()));
    }

    if !query_params.is_empty() {
        final_url.push('?');
        let query_string = query_params
            .iter()
            .map(|(k, v)| format!("{}={}", k, v))
            .collect::<Vec<_>>()
            .join("&");
        final_url.push_str(&query_string);
    }

    prepare_ws_url(&final_url).map_err(|e| {
        error!("Invalid WebSocket URL format {}: {}", &final_url, e);
        e
    })
}

/// Optimized connection handling function
async fn handle_optimized_connection(
    local_tcp_stream: TcpStream,
    ws_url: Arc<Url>,
    target_addr: Arc<Option<String>>,
    auth_header: Arc<Option<String>>,
    command_type: Arc<CommandType>,
    config: Arc<OptimizedConfig>,
    stats: Arc<RwLock<ConnectionStats>>,
) -> Result<()> {
    let ws_stream = connect_to_ws_server_optimized(
        ws_url,
        target_addr,
        auth_header,
        command_type,
        &config,
        Arc::clone(&stats),
    )
    .await?;

    // Optimized bidirectional forwarding
    forward_bidirectional_optimized(local_tcp_stream, ws_stream, config, stats).await
}

/// Optimized WebSocket connection function
async fn connect_to_ws_server_optimized(
    ws_url: Arc<Url>,
    target_addr: Arc<Option<String>>,
    auth_header: Arc<Option<String>>,
    command_type: Arc<CommandType>,
    config: &OptimizedConfig,
    stats: Arc<RwLock<ConnectionStats>>,
) -> Result<WebSocketStream<MaybeTlsStream<TcpStream>>> {
    // Build complete URL with target parameter (only in forward mode with target)
    let final_url = match (&*command_type, target_addr.as_ref()) {
        (CommandType::Forward, Some(target)) => {
            // Forward mode needs to include target parameter in URL
            let base_url_has_query = ws_url.query().is_some();
            let mut final_url_str = ws_url.as_str().to_string();

            let query_string = form_urlencoded::Serializer::new(String::new())
                .append_pair("target", target)
                .finish();

            if base_url_has_query {
                final_url_str.push('&');
            } else {
                final_url_str.push('?');
            }
            final_url_str.push_str(&query_string);

            debug!("Constructed complete WebSocket URL: {}", final_url_str);

            Url::parse(&final_url_str).map_err(|e| {
                error!("Failed to construct WebSocket URL: {}", e);
                TunnelError::UrlParseError(e)
            })?
        }
        _ => {
            // Proxy mode or no target case, use original URL directly
            debug!("Using original WebSocket URL: {}", ws_url.as_str());
            ws_url.as_ref().clone()
        }
    };

    // Optimized reconnection logic
    let mut attempts = 0;
    loop {
        match timeout(
            Duration::from_secs(30), // Connection timeout
            crate::common::connect_websocket_with_retry(
                &final_url,
                1, // Only connect once per attempt
                0, // No internal retry
                auth_header.as_deref(),
            ),
        )
        .await
        {
            Ok(Ok(ws_stream)) => {
                debug!("Successfully established WebSocket connection");
                return Ok(ws_stream);
            }
            Ok(Err(e)) => {
                attempts += 1;
                {
                    let mut stats_guard = stats.write().await;
                    stats_guard.increment_failed();
                }

                if attempts >= config.reconnect_attempts {
                    error!("WebSocket connection failed, reached maximum retry attempts: {}", attempts);
                    return Err(TunnelError::Other(format!(
                        "Connection failed, retry attempts: {}",
                        attempts
                    )));
                }

                {
                    let mut stats_guard = stats.write().await;
                    stats_guard.increment_reconnections();
                }

                warn!(
                    "WebSocket connection failed, retrying after {}ms (attempt {}/{}): {}",
                    config.reconnect_delay_ms, attempts, config.reconnect_attempts, e
                );
                sleep(Duration::from_millis(config.reconnect_delay_ms)).await;
            }
            Err(_) => {
                attempts += 1;
                {
                    let mut stats_guard = stats.write().await;
                    stats_guard.increment_failed();
                }

                if attempts >= config.reconnect_attempts {
                    error!("WebSocket connection timeout, reached maximum retry attempts: {}", attempts);
                    return Err(TunnelError::Other(format!(
                        "Connection timeout, retry attempts: {}",
                        attempts
                    )));
                }

                {
                    let mut stats_guard = stats.write().await;
                    stats_guard.increment_reconnections();
                }

                warn!(
                    "WebSocket connection timeout, retrying after {}ms (attempt {}/{})",
                    config.reconnect_delay_ms, attempts, config.reconnect_attempts
                );
                sleep(Duration::from_millis(config.reconnect_delay_ms)).await;
            }
        }
    }
}

/// Optimized bidirectional forwarding function
async fn forward_bidirectional_optimized(
    local_tcp_stream: TcpStream,
    ws_stream: WebSocketStream<MaybeTlsStream<TcpStream>>,
    config: Arc<OptimizedConfig>,
    stats: Arc<RwLock<ConnectionStats>>,
) -> Result<()> {
    let (mut local_read, mut local_write) = tokio::io::split(local_tcp_stream);
    let (mut ws_write, mut ws_read) = ws_stream.split();

    // Use optimized buffer size
    let buffer_size = config.buffer_size;

    // Local TCP to WebSocket (optimized version)
    let t1 = async {
        let mut buffer = vec![0u8; buffer_size];
        loop {
            let n = match local_read.read(&mut buffer).await {
                Ok(0) => {
                    debug!("Local TCP connection closed (EOF)");
                    // Gracefully close WebSocket connection
                    let _ = ws_write.send(Message::Close(None)).await;
                    return Ok(());
                }
                Ok(n) => n,
                Err(e) => {
                    error!("Error reading from local TCP: {}", e);
                    return Err(TunnelError::IoError(e));
                }
            };

            let data = &buffer[..n];
            {
                let mut stats_guard = stats.write().await;
                stats_guard.add_bytes_sent(n as u64);
            }



            if let Err(e) = ws_write.send(Message::Binary(data.to_vec())).await {
                error!("Error sending data to WebSocket: {}", e);
                return Err(TunnelError::WebSocketError(e));
            }
        }
    };

    // WebSocket to local TCP (optimized version)
    let t2 = async {
        while let Some(message) = ws_read.next().await {
            let message = match message {
                Ok(msg) => msg,
                Err(e) => {
                    error!("Error receiving message from WebSocket: {}", e);
                    return Err(TunnelError::WebSocketError(e));
                }
            };

            match message {
                Message::Binary(data) => {
                    {
                        let mut stats_guard = stats.write().await;
                        stats_guard.add_bytes_received(data.len() as u64);
                    }



                    if let Err(e) = local_write.write_all(&data).await {
                        error!("Error writing to local TCP: {}", e);
                        return Err(TunnelError::IoError(e));
                    }


                }
                Message::Text(data) => {
                    {
                        let mut stats_guard = stats.write().await;
                        stats_guard.add_bytes_received(data.len() as u64);
                    }

                    if let Err(e) = local_write.write_all(data.as_bytes()).await {
                        error!("Error writing to local TCP: {}", e);
                        return Err(TunnelError::IoError(e));
                    }
                }
                Message::Ping(_) => {
                    debug!("Received ping");
                }
                Message::Pong(_) => {
                    debug!("Received pong");
                }
                Message::Close(_) => {
                    info!("WebSocket connection closed by server");
                    return Ok(());
                }
                Message::Frame(_) => {
                    debug!("Received unhandled WebSocket frame");
                }
            }
        }
        info!("WebSocket read stream ended");
        Ok(())
    };

    // Run two tasks concurrently, terminate when either ends
    select! {
        result1 = t1 => {
            debug!("Local TCP->WebSocket task ended: {:?}", result1);
            result1
        },
        result2 = t2 => {
            debug!("WebSocket->Local TCP task ended: {:?}", result2);
            result2
        },
    }
}
