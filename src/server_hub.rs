use crate::common::{
    HubMessage, Result, TunnelError, ConnectionId, ConnectionIdGenerator,
    extract_connection_id_and_payload, add_connection_id_prefix,
};
use crate::server::ServerConfig;
use axum::{
    extract::{
        ws::{Message, WebSocket, WebSocketUpgrade},
        Extension,
    },
    response::IntoResponse,
};
use bytes::Bytes;
use futures_util::{SinkExt, StreamExt};
use log::{debug, error, info, warn};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{Mutex, RwLock};
use tokio::time::{timeout, Duration, Instant};

// Service hub state management
#[derive(Debug)]
pub struct ServiceHub {
    // Mapping from service ID to Hub Service WebSocket connection
    hub_services: HashMap<String, HubServiceState>,
    // Mapping from client connection ID to corresponding service ID
    client_to_service: HashMap<ConnectionId, String>,
    // Connection statistics
    connection_stats: ConnectionStats,
    // Connection ID generator
    connection_id_generator: ConnectionIdGenerator,
}

#[derive(Debug)]
struct HubServiceState {
    sink: Arc<Mutex<futures_util::stream::SplitSink<WebSocket, Message>>>,
    // Each client connection ID corresponds to a client WebSocket receiver
    clients: HashMap<ConnectionId, Arc<Mutex<futures_util::stream::SplitSink<WebSocket, Message>>>>,
}

#[derive(Default, Debug)]
struct ConnectionStats {
    total_hub_services: u64,
    total_clients: u64,
    active_connections: u64,
    failed_connections: u64,
}

impl ConnectionStats {
    fn new() -> Self {
        ConnectionStats {
            total_hub_services: 0,
            total_clients: 0,
            active_connections: 0,
            failed_connections: 0,
        }
    }

    fn log_stats(&self) {
        info!(
            "Connection Stats - Hub Services: {}, Clients: {}, Active: {}, Failed: {}",
            self.total_hub_services,
            self.total_clients,
            self.active_connections,
            self.failed_connections
        );
    }
}

impl ServiceHub {
    pub fn new() -> Self {
        ServiceHub {
            hub_services: HashMap::new(),
            client_to_service: HashMap::new(),
            connection_stats: ConnectionStats::new(),
            connection_id_generator: ConnectionIdGenerator::new(),
        }
    }

    // Handle client disconnection
    pub async fn handle_client_disconnect(&mut self, client_id: ConnectionId) {
        debug!("Handling client disconnect: {}", client_id);

        if let Some(service_id) = self.client_to_service.remove(&client_id) {
            debug!(
                "Client {} was connected to service {}",
                client_id, service_id
            );

            if let Some(hub_service) = self.hub_services.get_mut(&service_id) {
                let notification = HubMessage::ClientDisconnectedNotification {
                    client_connection_id: client_id,
                };

                let json = serde_json::to_string(&notification).unwrap_or_else(|e| {
                    error!("Failed to serialize disconnect notification: {}", e);
                    "".to_string()
                });

                if !json.is_empty() {
                    if let Err(e) = hub_service
                        .sink
                        .lock()
                        .await
                        .send(Message::Text(json.into()))
                        .await
                    {
                        error!(
                            "Failed to notify hub service about client disconnect: {}",
                            e
                        );
                    } else {
                        debug!("Notified hub service about client {} disconnect", client_id);
                    }
                }

                hub_service.clients.remove(&client_id);
                debug!(
                    "Cleaned up client {} from service {}",
                    client_id, service_id
                );
            } else {
                debug!("No hub service found for service {}", service_id);
            }

            // Update statistics
            self.connection_stats.active_connections =
                self.connection_stats.active_connections.saturating_sub(1);
            self.connection_stats.log_stats();
        } else {
            debug!("Client {} was not registered", client_id);
        }
    }

    // Handle Hub Service disconnection
    pub async fn handle_hubservice_disconnect(&mut self, service_id: &str) {
        if let Some(hub_service) = self.hub_services.remove(service_id) {

            // Notify all clients connected to this service
            let client_count = hub_service.clients.len() as u64;

            for (client_id, client_sink) in hub_service.clients {
                let notification = HubMessage::ServiceHubServiceDisconnectedNotification {
                    service_id: service_id.to_string(),
                };

                let json = serde_json::to_string(&notification).unwrap_or_else(|e| {
                    error!("Failed to serialize message: {}", e);
                    "".to_string()
                });

                if !json.is_empty() {
                    if let Err(e) = client_sink
                        .lock()
                        .await
                        .send(Message::Text(json.into()))
                        .await
                    {
                        error!(
                            "Failed to notify client about hub service disconnect: {}",
                            e
                        );
                    }
                }

                // Remove from client-to-service mapping
                self.client_to_service.remove(&client_id);
            }

            // Update statistics
            self.connection_stats.active_connections = self
                .connection_stats
                .active_connections
                .saturating_sub(client_count + 1);
            info!(
                "Hub Service for service '{}' disconnected, {} clients affected",
                service_id, client_count
            );
            self.connection_stats.log_stats();
        }
    }

    // Forward binary message from client to Hub Service
    pub async fn forward_client_to_hubservice(&self, client_id: ConnectionId, data: Vec<u8>) -> Result<()> {
        if let Some(service_id) = self.client_to_service.get(&client_id) {
            if let Some(hub_service) = self.hub_services.get(service_id) {
                let mut hub_service_sink = hub_service.sink.lock().await;

                // Add client_id prefix (fixed 4 bytes, no separator)
                let prefixed_data = add_connection_id_prefix(client_id, &data);

                debug!(
                    "Forwarding {} bytes from client {} to service {}",
                    data.len(),
                    client_id,
                    service_id
                );

                hub_service_sink
                    .send(Message::Binary(Bytes::from(prefixed_data)))
                    .await
                    .map_err(|e| {
                        error!(
                            "Error forwarding data to hub service for service {}: {}",
                            service_id, e
                        );
                        TunnelError::Other(format!("WebSocket error sending to hub service: {}", e))
                    })?;

                Ok(())
            } else {
                error!("Hub Service not found for service: {}", service_id);
                Err(TunnelError::ServiceUnavailable(format!(
                    "Service '{}' is not available",
                    service_id
                )))
            }
        } else {
            error!("Client '{}' is not registered", client_id);
            Err(TunnelError::Other(format!(
                "Client '{}' is not registered",
                client_id
            )))
        }
    }

    // Forward binary message from Hub Service to client
    pub async fn forward_hubservice_to_client(
        &self,
        service_id: &str,
        client_id: ConnectionId,
        data: Vec<u8>,
    ) -> Result<()> {
        if let Some(hub_service) = self.hub_services.get(service_id) {
            if let Some(client_sink) = hub_service.clients.get(&client_id) {
                let mut client_sink = client_sink.lock().await;
                client_sink
                    .send(Message::Binary(Bytes::from(data)))
                    .await
                    .map_err(|e| TunnelError::Other(format!("WebSocket error: {}", e)))?;

                Ok(())
            } else {
                Err(TunnelError::Other(format!(
                    "Client '{}' is not connected to service '{}'",
                    client_id, service_id
                )))
            }
        } else {
            Err(TunnelError::ServiceUnavailable(format!(
                "Service '{}' is not available",
                service_id
            )))
        }
    }

    // Check if service is available
    pub fn is_service_available(&self, service_id: &str) -> bool {
        self.hub_services.contains_key(service_id)
    }

    // Register Hub Service
    pub fn register_hubservice(
        &mut self,
        service_id: String,
        sink: Arc<Mutex<futures_util::stream::SplitSink<WebSocket, Message>>>,
    ) {
        self.hub_services.insert(
            service_id.clone(),
            HubServiceState {
                sink,
                clients: HashMap::new(),
            },
        );

        // Update statistics
        self.connection_stats.total_hub_services += 1;
        self.connection_stats.active_connections += 1;
        info!("Registered hub service for service: {}", service_id);
        self.connection_stats.log_stats();
    }

    // Register client
    pub fn register_client(
        &mut self,
        client_id: ConnectionId,
        service_id: String,
        sink: Arc<Mutex<futures_util::stream::SplitSink<WebSocket, Message>>>,
    ) {
        self.client_to_service
            .insert(client_id, service_id.clone());

        if let Some(hub_service) = self.hub_services.get_mut(&service_id) {
            hub_service.clients.insert(client_id, sink);

            // Update statistics
            self.connection_stats.total_clients += 1;
            self.connection_stats.active_connections += 1;
            info!(
                "Registered client {} for service: {}",
                client_id, service_id
            );
            self.connection_stats.log_stats();
        }
    }

    // Get Hub Service Sink for sending notifications
    pub fn get_hubservice_sink(
        &self,
        service_id: &str,
    ) -> Option<Arc<Mutex<futures_util::stream::SplitSink<WebSocket, Message>>>> {
        self.hub_services
            .get(service_id)
            .map(|hub_service| Arc::clone(&hub_service.sink))
    }

    // Get list of active services
    pub fn list_active_services(&self) -> Vec<String> {
        self.hub_services.keys().cloned().collect()
    }

    // Generate new connection ID
    pub fn generate_connection_id(&mut self) -> ConnectionId {
        self.connection_id_generator.next_id()
    }

    // Manually trigger statistics logging (for periodic monitoring)
    pub fn log_current_stats(&self) {
        self.connection_stats.log_stats();
    }
}

// Handle WebSocket connections for service hub mode
pub async fn hub_handler(
    ws: WebSocketUpgrade,
    Extension(service_hub): Extension<Arc<RwLock<ServiceHub>>>,
    Extension(server_config): Extension<Arc<ServerConfig>>,
) -> impl IntoResponse {
    ws.on_upgrade(move |socket| handle_hub_socket(socket, service_hub, server_config))
}

// Start statistics monitoring task
pub fn start_stats_monitoring_task(service_hub: Arc<RwLock<ServiceHub>>) {
    tokio::spawn(async move {
        let mut interval = tokio::time::interval(Duration::from_secs(300)); // Record statistics every 5 minutes

        loop {
            interval.tick().await;

            let hub = service_hub.read().await;
            info!("=== Hub Statistics (5min interval) ===");
            hub.log_current_stats();

            // Add more detailed statistics if needed
            let hub_service_count = hub.hub_services.len();
            let client_count = hub.client_to_service.len();
            info!(
                "Current active hub services: {}, clients: {}",
                hub_service_count, client_count
            );
        }
    });
}

// Handle service hub WebSocket connections
async fn handle_hub_socket(
    socket: WebSocket,
    service_hub: Arc<RwLock<ServiceHub>>,
    server_config: Arc<ServerConfig>,
) {
    debug!("New hub connection established");

    let (sink, mut stream) = socket.split();
    let sink = Arc::new(Mutex::new(sink));

    // Wait for first message to determine if it's a service provider or client
    if let Some(Ok(msg)) = stream.next().await {
        match msg {
            Message::Text(text) => {
                match serde_json::from_str::<HubMessage>(&text) {
                    Ok(HubMessage::RegisterService { service_id }) => {
                        // Handle Hub Service registration
                        info!("Registering hub service for service: {}", service_id);

                        let service_id_clone = service_id.clone();

                        // Send confirmation message to Hub Service
                        {
                            let ack_msg = HubMessage::ServiceRegisteredAck {
                                service_id: service_id_clone.clone(),
                            };

                            let mut sink_guard = sink.lock().await;
                            if let Err(e) = crate::common::send_hub_message_with_context(
                                &mut *sink_guard,
                                &ack_msg,
                                "service registration",
                            )
                            .await
                            {
                                error!("Failed to send service registration ack: {}", e);
                                return;
                            }
                            drop(sink_guard);

                            // Register Hub Service in service hub
                            let mut hub = service_hub.write().await;
                            hub.register_hubservice(service_id_clone.clone(), Arc::clone(&sink));


                        }

                        // Continue handling subsequent messages from this Hub Service
                        handle_hubservice_messages(service_hub, service_id_clone, stream, Arc::clone(&server_config)).await;
                    }
                    Ok(HubMessage::ConnectToServiceRequest { service_id }) => {
                        // Handle client connection request
                        info!("Client requesting connection to service: {}", service_id);

                        let service_id_clone = service_id.clone();
                        let client_id = {
                            let mut hub = service_hub.write().await;
                            hub.generate_connection_id()
                        };

                        // Check if service exists
                        let service_available = {
                            let hub = service_hub.read().await;
                            hub.is_service_available(&service_id_clone)
                        };

                        if service_available {
                            // Notify Hub Service of new client connection
                            {
                                let hub = service_hub.read().await;
                                if let Some(hub_service_sink) =
                                    hub.get_hubservice_sink(&service_id_clone)
                                {
                                    let notification = HubMessage::NewClientNotification {
                                        client_connection_id: client_id,
                                    };

                                    let json =
                                        serde_json::to_string(&notification).unwrap_or_else(|e| {
                                            error!("Failed to serialize message: {}", e);
                                            "".to_string()
                                        });

                                    if !json.is_empty() {
                                        if let Err(e) = hub_service_sink
                                            .lock()
                                            .await
                                            .send(Message::Text(json.into()))
                                            .await
                                        {
                                            error!(
                                                "Failed to notify hub service about new client: {}",
                                                e
                                            );
                                        }
                                    }
                                }
                            }

                            // Send confirmation message to client
                            let ack = HubMessage::ServiceConnectionAck {
                                service_id: service_id_clone.clone(),
                                client_connection_id: client_id,
                            };

                            let json = serde_json::to_string(&ack).unwrap_or_else(|e| {
                                error!("Failed to serialize message: {}", e);
                                "".to_string()
                            });

                            if !json.is_empty() {
                                if let Err(e) =
                                    sink.lock().await.send(Message::Text(json.into())).await
                                {
                                    error!("Failed to send service connection ack: {}", e);
                                    return;
                                }
                            }

                            // Register client
                            {
                                let mut hub = service_hub.write().await;
                                hub.register_client(
                                    client_id,
                                    service_id_clone.clone(),
                                    Arc::clone(&sink),
                                );
                            }

                            // Handle subsequent messages from this client
                            handle_client_messages(
                                service_hub,
                                client_id,
                                service_id_clone,
                                stream,
                            )
                            .await;
                        } else {
                            // Service unavailable, send error message
                            let error_msg = HubMessage::ServiceUnavailableNotification {
                                service_id: service_id_clone.clone(),
                                reason: "Service is not registered".to_string(),
                            };

                            let json = serde_json::to_string(&error_msg).unwrap_or_else(|e| {
                                error!("Failed to serialize message: {}", e);
                                "".to_string()
                            });

                            if !json.is_empty() {
                                if let Err(e) =
                                    sink.lock().await.send(Message::Text(json.into())).await
                                {
                                    error!("Failed to send error notification: {}", e);
                                }
                            }

                            // Update failed connection statistics
                            {
                                let mut hub = service_hub.write().await;
                                hub.connection_stats.failed_connections += 1;
                                warn!(
                                    "Client connection failed - service '{}' not available",
                                    service_id_clone
                                );
                                hub.connection_stats.log_stats();
                            }
                        }
                    }
                    _ => {
                        error!("Invalid first message for hub connection");

                        // Send error message
                        let error_msg = HubMessage::ErrorNotification {
                            message: "Invalid first message, expected RegisterService or ConnectToServiceRequest".to_string(),
                        };

                        let json = serde_json::to_string(&error_msg).unwrap_or_else(|e| {
                            error!("Failed to serialize message: {}", e);
                            "".to_string()
                        });

                        if !json.is_empty() {
                            if let Err(e) = sink.lock().await.send(Message::Text(json.into())).await
                            {
                                error!("Failed to send error notification: {}", e);
                            }
                        }
                    }
                }
            }
            _ => {
                error!("Expected text message as first message");

                // Send error message
                let error_msg = HubMessage::ErrorNotification {
                    message: "Expected text message as first message".to_string(),
                };

                let json = serde_json::to_string(&error_msg).unwrap_or_else(|e| {
                    error!("Failed to serialize message: {}", e);
                    "".to_string()
                });

                if !json.is_empty() {
                    if let Err(e) = sink.lock().await.send(Message::Text(json.into())).await {
                        error!("Failed to send error notification: {}", e);
                    }
                }
            }
        }
    }
}

// Handle subsequent messages from Hub Service
async fn handle_hubservice_messages(
    service_hub: Arc<RwLock<ServiceHub>>,
    service_id: String,
    mut stream: futures_util::stream::SplitStream<WebSocket>,
    server_config: Arc<ServerConfig>,
) {
    debug!(
        "[DEBUG] handle_hubservice_messages: Started for service {}",
        service_id
    );

    let mut last_activity = Instant::now();
    let activity_timeout = Duration::from_secs(server_config.activity_timeout); // Read connection activity timeout from config

    loop {
        let msg_result = timeout(Duration::from_secs(5), stream.next()).await;

        match msg_result {
            Ok(Some(Ok(msg))) => {
                last_activity = Instant::now(); // Update last activity time

                match msg {
                    Message::Binary(data) => {
                        let (client_id, payload_slice) = match extract_connection_id_and_payload(&data) {
                            Ok((id, payload)) => (id, payload),
                            Err(e) => {
                                error!("Failed to extract connection ID from hub service message: {}", e);
                                continue;
                            }
                        };
                        
                        // Immediately copy data to avoid data races in async calls
                        let payload = payload_slice.to_vec();

                        // Async forwarding
                        if let Err(e) = service_hub
                            .read()
                            .await
                            .forward_hubservice_to_client(&service_id, client_id, payload)
                            .await
                        {
                            error!(
                                "Failed to forward data from service {} to client {}: {}",
                                service_id, client_id, e
                            );
                        }
                    }
                    Message::Text(text) => {
                        // Handle control messages, e.g., ClientConnectionEndedByTarget
                        match serde_json::from_str::<HubMessage>(&text) {
                            Ok(HubMessage::ClientConnectionEndedByTarget {
                                client_connection_id,
                            }) => {
                                info!(
                                    "Hub Service for service '{}' reported client connection {} ended by target",
                                    service_id, client_connection_id
                                );

                                // Check if client still exists to avoid duplicate cleanup
                                let client_exists = {
                                    let hub = service_hub.read().await;
                                    hub.client_to_service.contains_key(&client_connection_id)
                                };

                                if !client_exists {
                                    debug!(
                                        "Client {} already disconnected, skipping cleanup",
                                        client_connection_id
                                    );
                                    continue;
                                }

                                // Add detailed logging
                                debug!(
                                    "[DEBUG] Before handle_client_disconnect: Is client {} registered? {}",
                                    client_connection_id, client_exists
                                );

                                // Remove this client connection from service hub
                                service_hub
                                    .write()
                                    .await
                                    .handle_client_disconnect(client_connection_id)
                                    .await;

                                // No longer try to send notifications to cleaned up client, as handle_client_disconnect already handled it
                                debug!("[DEBUG] Client {} cleanup completed", client_connection_id);
                            }
                            Ok(HubMessage::HubServiceHeartbeat {
                                service_id: heartbeat_service_id,
                                timestamp,
                            }) => {
                                debug!(
                                    "[Heartbeat-Server] Received application heartbeat from hub service '{}' timestamp: {}",
                                    heartbeat_service_id, timestamp
                                );

                                // Verify service ID matches
                                if heartbeat_service_id != service_id {
                                    warn!(
                                        "[Heartbeat-Server] Heartbeat service ID mismatch: expected '{}', got '{}'",
                                        service_id, heartbeat_service_id
                                    );
                                }

                                // Application layer heartbeat doesn't need special response, just log it
                                // This proves the connection is active and not intercepted by LB
                            }
                            Err(e) => {
                                error!("Failed to parse text message from hub service: {}", e);
                            }
                            _ => {
                                // Ignore other text messages
                                debug!("Received unhandled text message from hub service");
                            }
                        }
                    }
                    Message::Close(_) => {
                        info!("Hub Service for service '{}' disconnected", service_id);
                        debug!(
                            "[DEBUG] handle_hubservice_messages: Received Close frame for service {}",
                            service_id
                        );
                        service_hub
                            .write()
                            .await
                            .handle_hubservice_disconnect(&service_id)
                            .await;
                        break;
                    }
                    Message::Ping(payload) => {
                        let payload_str = String::from_utf8_lossy(&payload);
                        debug!(
                            "[Heartbeat-Server] Received ping from hub service for service '{}' with payload: '{}'",
                            service_id, payload_str
                        );
                        // Pong will be sent automatically
                    }
                    Message::Pong(payload) => {
                        let payload_str = String::from_utf8_lossy(&payload);
                        debug!(
                            "[Heartbeat-Server] Received pong from hub service for service '{}' with payload: '{}'",
                            service_id, payload_str
                        );
                    }
                }
            }
            Ok(Some(Err(e))) => {
                error!("Error reading from hub service WebSocket: {}", e);
                break;
            }
            Ok(None) => {
                debug!("Hub Service WebSocket stream ended");
                break;
            }
            Err(_) => {
                // Timeout check
                if last_activity.elapsed() > activity_timeout {
                    warn!(
                        "Hub Service for service '{}' timed out (no activity for {}s)",
                        service_id,
                        activity_timeout.as_secs()
                    );
                    break;
                } else {
                    // Brief timeout, continue waiting
                    continue;
                }
            }
        }
    }

    // WebSocket stream ended, handle disconnection
    debug!(
        "[DEBUG] handle_hubservice_messages: WebSocket stream ended for service {}",
        service_id
    );
    service_hub
        .write()
        .await
        .handle_hubservice_disconnect(&service_id)
        .await;
}

// Handle subsequent messages from client
async fn handle_client_messages(
    service_hub: Arc<RwLock<ServiceHub>>,
    client_id: ConnectionId,
    service_id: String,
    mut stream: futures_util::stream::SplitStream<WebSocket>,
) {
    debug!(
        "[DEBUG] handle_client_messages: Started for client {} on service {}",
        client_id, service_id
    );

    let mut is_disconnected = false; // Add state tracking

    while let Some(Ok(msg)) = stream.next().await {
        match msg {
            Message::Binary(data) => {
                debug!(
                    "[DEBUG] handle_client_messages: Received binary data of size {} from client {}",
                    data.len(),
                    client_id
                );

                // Verify if client_id is still registered in service hub
                let is_registered = {
                    let hub = service_hub.read().await;
                    hub.client_to_service.contains_key(&client_id)
                };

                debug!(
                    "[DEBUG] handle_client_messages: Is client {} still registered? {}",
                    client_id, is_registered
                );

                if !is_registered {
                    debug!(
                        "[DEBUG] handle_client_messages: Skipping forward for unregistered client {}",
                        client_id
                    );
                    // If client is no longer registered, should disconnect
                    break;
                }

                // Forward directly to Hub Service, forward_client_to_hubservice will add prefix
                if let Err(e) = service_hub
                    .read()
                    .await
                    .forward_client_to_hubservice(client_id, data.to_vec())
                    .await
                {
                    error!("Failed to forward data to hub service for client {}: {}", client_id, e);
                    
                    // Any failure to forward to Hub Service should disconnect client connection
                    // This includes: WebSocket send failure, network error, Hub Service disconnect, etc.
                    debug!(
                        "[DEBUG] Breaking client message loop due to hub service forward error: {}",
                        e
                    );
                    break;
                }
            }
            Message::Close(_) => {
                info!(
                    "Client '{}' for service '{}' disconnected",
                    client_id, service_id
                );
                debug!(
                    "[DEBUG] handle_client_messages: Received Close frame for client {}",
                    client_id
                );
                if !is_disconnected {
                    service_hub
                        .write()
                        .await
                        .handle_client_disconnect(client_id)
                        .await;
                    is_disconnected = true;
                }
                break;
            }
            Message::Ping(payload) => {
                let payload_str = String::from_utf8_lossy(&payload);
                debug!(
                    "[Heartbeat-Server] Received ping from client '{}' for service '{}' with payload: '{}'",
                    client_id, service_id, payload_str
                );
                // Pong will be sent automatically
            }
            Message::Pong(payload) => {
                let payload_str = String::from_utf8_lossy(&payload);
                debug!(
                    "[Heartbeat-Server] Received pong from client '{}' for service '{}' with payload: '{}'",
                    client_id, service_id, payload_str
                );
            }
            Message::Text(text) => {
                debug!(
                    "[DEBUG] handle_client_messages: Received text message from client {}: {}",
                    client_id, text
                );
                // Clients usually don't send text messages, but log it
            }
        }
    }

    // WebSocket stream ended, handle disconnection (only if not already cleaned up)
    if !is_disconnected {
        debug!(
            "[DEBUG] handle_client_messages: WebSocket stream ended for client {}",
            client_id
        );
        service_hub
            .write()
            .await
            .handle_client_disconnect(client_id)
            .await;
    }
}


