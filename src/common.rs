use axum::extract::ws::Message;
use futures_util::SinkExt;
use log::{error, info};
use serde::{Deserialize, Serialize};
use std::io;
use std::sync::atomic::{AtomicU32, Ordering};
use std::sync::Arc;
use thiserror::Error;
use tokio::net::TcpStream;
use tokio_tungstenite::{MaybeTlsStream, WebSocketStream};
use url::Url;

pub use crate::reconnect::{ReconnectSignal, ReconnectState};

/// Hub client common configuration
#[derive(Debug, Clone)]
pub struct HubClientConfig {
    pub server: String,
    pub service_id: String,
    pub heartbeat_interval: u64,
    pub username: Option<String>,
    pub password: Option<String>,
}

pub type HubServiceConfig = HubClientConfig;
pub type HubServiceDynamicConfig = HubClientConfig;

pub const DEFAULT_BUFFER_SIZE: usize = 32 * 1024; // Increased to 32KB for remote network adaptation

// Connection ID related type definitions
pub type ConnectionId = u32;

/// Connection ID generator - using atomic increment counter
#[derive(Debug, Clone)]
pub struct ConnectionIdGenerator {
    counter: Arc<AtomicU32>,
}

impl ConnectionIdGenerator {
    /// Create new connection ID generator
    pub fn new() -> Self {
        Self {
            counter: Arc::new(AtomicU32::new(1)), // Start from 1, avoid 0 value
        }
    }
    
    /// Generate next connection ID
    pub fn next_id(&self) -> ConnectionId {
        self.counter.fetch_add(1, Ordering::Relaxed)
    }
    
    /// Get current counter value (without incrementing)
    pub fn current_count(&self) -> u32 {
        self.counter.load(Ordering::Relaxed)
    }
}

impl Default for ConnectionIdGenerator {
    fn default() -> Self {
        Self::new()
    }
}

/// Encode connection ID as 4-byte big-endian format
pub fn encode_connection_id(id: ConnectionId) -> [u8; 4] {
    id.to_be_bytes()
}

/// Decode connection ID from byte array
pub fn decode_connection_id(bytes: &[u8]) -> Result<ConnectionId> {
    if bytes.len() < 4 {
        return Err(TunnelError::InvalidConnectionId(format!(
            "Need at least 4 bytes, but only {} bytes",
            bytes.len()
        )));
    }
    Ok(ConnectionId::from_be_bytes([bytes[0], bytes[1], bytes[2], bytes[3]]))
}

/// Add connection ID prefix to data packet
pub fn add_connection_id_prefix(id: ConnectionId, data: &[u8]) -> Vec<u8> {
    let mut prefixed_data = encode_connection_id(id).to_vec();
    prefixed_data.extend_from_slice(data);
    prefixed_data
}

/// Extract connection ID and payload from data packet
pub fn extract_connection_id_and_payload(data: &[u8]) -> Result<(ConnectionId, &[u8])> {
    if data.len() < 4 {
        return Err(TunnelError::InvalidConnectionId(format!(
            "Insufficient packet length, need at least 4 bytes for connection ID prefix, actual length: {}",
            data.len()
        )));
    }
    
    let connection_id = decode_connection_id(&data[..4])?;
    let payload = &data[4..];
    Ok((connection_id, payload))
}

#[derive(Error, Debug)]
pub enum TunnelError {
    #[error("IO error: {0}")]
    IoError(#[from] io::Error),

    #[error("WebSocket error: {0}")]
    WebSocketError(#[from] tokio_tungstenite::tungstenite::Error),

    #[error("URL parse error: {0}")]
    UrlParseError(#[from] url::ParseError),

    #[error("Failed to connect to target: {0}")]
    TargetConnectionFailed(String),

    #[error("Configuration error: {0}")]
    ConfigError(String),

    #[error("Service unavailable: {0}")]
    ServiceUnavailable(String),

    #[error("JSON error: {0}")]
    JsonError(#[from] serde_json::Error),

    #[error("Other error: {0}")]
    Other(String),

    #[error("Invalid connection ID: {0}")]
    InvalidConnectionId(String),
    
    #[error("Connection not found: {0}")]
    ConnectionNotFound(ConnectionId),

    #[error("Axum error: {0}")]
    AxumError(#[from] axum::Error),
}

pub type Result<T> = std::result::Result<T, TunnelError>;

/// Clean consecutive slashes at the end of server URL
/// Example: "wss://host/" -> "wss://host", "wss://host///" -> "wss://host"
pub fn trim_server_url_slashes(url: &str) -> String {
    url.trim_end_matches('/').to_string()
}

// Function moved from common_utils.rs
pub fn prepare_ws_url(url_str: &str) -> Result<Url> {
    let url = Url::parse(url_str).map_err(TunnelError::UrlParseError)?;
    if url.scheme() != "ws" && url.scheme() != "wss" {
        error!(
            "Invalid WebSocket scheme: {}. Must be 'ws' or 'wss'.",
            url.scheme()
        );
        return Err(TunnelError::ConfigError(format!(
            "Invalid WebSocket scheme: {}. Must be 'ws' or 'wss'.",
            url.scheme()
        )));
    }
    Ok(url)
}

// Common utility functions for reducing code duplication

/// Safely serialize a HubMessage to JSON string
pub fn serialize_hub_message(msg: &HubMessage) -> std::result::Result<String, String> {
    serde_json::to_string(msg).map_err(|e| {
        error!("Failed to serialize message: {}", e);
        e.to_string()
    })
}

// Message types for service center Hub mode
#[derive(Serialize, Deserialize, Clone, Debug)]
#[serde(tag = "type")]
pub enum HubMessage {
    // Service provider -> Server
    #[serde(rename = "register_service")]
    RegisterService { service_id: String },

    // Server -> Service provider
    #[serde(rename = "service_registered_ack")]
    ServiceRegisteredAck { service_id: String },

    #[serde(rename = "new_client_notification")]
    NewClientNotification { client_connection_id: ConnectionId },

    #[serde(rename = "client_disconnected_notification")]
    ClientDisconnectedNotification { client_connection_id: ConnectionId },

    // Service consumer -> Server
    #[serde(rename = "connect_to_service_request")]
    ConnectToServiceRequest { service_id: String },

    // Server -> Service consumer
    #[serde(rename = "service_connection_ack")]
    ServiceConnectionAck {
        service_id: String,
        client_connection_id: ConnectionId,
    },

    #[serde(rename = "service_unavailable_notification")]
    ServiceUnavailableNotification { service_id: String, reason: String },

    #[serde(rename = "service_hubservice_disconnected_notification")]
    ServiceHubServiceDisconnectedNotification { service_id: String },

    #[serde(rename = "error_notification")]
    ErrorNotification { message: String },

    // Service provider -> Server: Target TCP connection closed
    #[serde(rename = "client_connection_ended_by_target")]
    ClientConnectionEndedByTarget { client_connection_id: ConnectionId },

    // Service provider -> Server: Application layer heartbeat message (avoid LB intercepting ping/pong)
    #[serde(rename = "hubservice_heartbeat")]
    HubServiceHeartbeat { service_id: String, timestamp: u64 },

    // === New message types for refactoring design ===

    // Hub forward request (includes target address)
    #[serde(rename = "hub_forward_request")]
    HubForwardRequest {
        service_id: String,
        target_addr: String, // Specified by client
    },

    // Hub proxy request (no target address)
    #[serde(rename = "hub_proxy_request")]
    HubProxyRequest {
        service_id: String,
        // target_addr sent separately after proxy protocol parsing
    },

    // Server distributes instruction to hub-service
    #[serde(rename = "service_forward_instruction")]
    ServiceForwardInstruction {
        connection_id: ConnectionId,
        instruction_type: String, // "target" or "proxy"
        target_addr: String,      // Target address
    },

    // hub-service response
    #[serde(rename = "service_instruction_ack")]
    ServiceInstructionAck {
        connection_id: ConnectionId,
        success: bool,
        error_message: Option<String>,
    },
}



/// Generate Origin header based on WebSocket URL
pub fn generate_origin_header(url: &Url) -> String {
    let scheme = match url.scheme() {
        "wss" => "https",
        "ws" => "http",
        _ => url.scheme(), // fallback
    };

    let mut origin = format!("{}://{}", scheme, url.host_str().unwrap_or("localhost"));

    // Add port (if not default port)
    if let Some(port) = url.port() {
        let default_port = match scheme {
            "https" => 443,
            "http" => 80,
            _ => 0,
        };
        if port != default_port {
            origin.push_str(&format!(":{}", port));
        }
    }

    origin
}

/// Generic WebSocket connection with retry logic
pub async fn connect_websocket_with_retry(
    url: &Url,
    max_attempts: u32,
    delay_ms: u64,
    auth_header: Option<&str>,
) -> Result<WebSocketStream<MaybeTlsStream<TcpStream>>> {
    let mut last_error: Option<tokio_tungstenite::tungstenite::Error> = None;
    let origin = generate_origin_header(url);

    for attempt in 0..=max_attempts {
        let mut request =
            tokio_tungstenite::tungstenite::client::IntoClientRequest::into_client_request(
                url.clone(),
            )
            .map_err(TunnelError::WebSocketError)?;

        // Add Origin header
        request.headers_mut().insert(
            "Origin",
            tokio_tungstenite::tungstenite::http::header::HeaderValue::from_str(&origin)
                .map_err(|e| TunnelError::Other(format!("Invalid origin header: {}", e)))?,
        );

        // Add authentication header (if any)
        if let Some(auth_str) = auth_header {
            request.headers_mut().insert(
                "Authorization",
                tokio_tungstenite::tungstenite::http::header::HeaderValue::from_str(auth_str)
                    .map_err(|e| TunnelError::Other(format!("Invalid auth header: {}", e)))?,
            );
        }

        let result = tokio_tungstenite::connect_async(request).await;

        match result {
            Ok((stream, _)) => {
                if attempt > 0 {
                    info!(
                        "Connected to WebSocket server {} on attempt {}",
                        url,
                        attempt + 1
                    );
                } else {
                    info!("Connected to WebSocket server {} on first attempt", url);
                }
                return Ok(stream);
            }
            Err(e) => {
                last_error = Some(e);

                if attempt < max_attempts {
                    error!(
                        "Failed to connect to WebSocket server (attempt {}/{}): {}. Retrying in {}ms...",
                        attempt + 1,
                        max_attempts + 1,
                        last_error.as_ref().unwrap(),
                        delay_ms
                    );
                    tokio::time::sleep(tokio::time::Duration::from_millis(delay_ms)).await;
                } else {
                    error!(
                        "Failed to connect to WebSocket server after {} attempts: {}",
                        max_attempts + 1,
                        last_error.as_ref().unwrap()
                    );
                }
            }
        }
    }

    Err(TunnelError::WebSocketError(last_error.unwrap_or_else(
        || tokio_tungstenite::tungstenite::Error::ConnectionClosed,
    )))
}

/// Improved hub message handling with better error context
pub async fn send_hub_message_with_context<T>(
    sink: &mut T,
    msg: &HubMessage,
    context: &str,
) -> Result<()>
where
    T: SinkExt<Message> + Unpin,
    T::Error: std::fmt::Display,
{
    match serialize_hub_message(msg) {
        Ok(json) => {
            sink.send(Message::Text(json.into())).await.map_err(|e| {
                error!("Failed to send message in {}: {}", context, e);
                TunnelError::Other(format!("Failed to send message in {}: {}", context, e))
            })?;
            Ok(())
        }
        Err(e) => {
            error!("Failed to serialize message in {}: {}", context, e);
            Err(TunnelError::Other(format!(
                "Serialization error in {}: {}",
                context, e
            )))
        }
    }
}

/// Common TCP listener setup with error handling
pub async fn setup_tcp_listener(addr: &str) -> Result<tokio::net::TcpListener> {
    tokio::net::TcpListener::bind(addr).await.map_err(|e| {
        error!("Failed to bind to {}: {}", addr, e);
        TunnelError::IoError(e)
    })
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_connection_id_generator() {
        let generator = ConnectionIdGenerator::new();
        
        // Test initial value
        assert_eq!(generator.current_count(), 1);

        // Test increment
        let id1 = generator.next_id();
        let id2 = generator.next_id();
        let id3 = generator.next_id();
        
        assert_eq!(id1, 1);
        assert_eq!(id2, 2);
        assert_eq!(id3, 3);
        assert_eq!(generator.current_count(), 4);
    }
    
    #[test]
    fn test_connection_id_encoding() {
        // Test encoding
        let id: ConnectionId = 0x12345678;
        let encoded = encode_connection_id(id);
        assert_eq!(encoded, [0x12, 0x34, 0x56, 0x78]);

        // Test decoding
        let decoded = decode_connection_id(&encoded).unwrap();
        assert_eq!(decoded, 0x12345678);

        // Test boundary values
        let max_id = u32::MAX;
        let encoded_max = encode_connection_id(max_id);
        let decoded_max = decode_connection_id(&encoded_max).unwrap();
        assert_eq!(decoded_max, max_id);
        
        let min_id = 1u32;
        let encoded_min = encode_connection_id(min_id);
        let decoded_min = decode_connection_id(&encoded_min).unwrap();
        assert_eq!(decoded_min, min_id);
    }
    
    #[test]
    fn test_connection_id_prefix() {
        let id: ConnectionId = 0x12345678;
        let data = b"hello world";
        
        // Test adding prefix
        let prefixed = add_connection_id_prefix(id, data);
        assert_eq!(prefixed.len(), 4 + data.len());
        assert_eq!(&prefixed[..4], [0x12, 0x34, 0x56, 0x78]);
        assert_eq!(&prefixed[4..], data);

        // Test extracting connection ID and payload
        let (extracted_id, payload) = extract_connection_id_and_payload(&prefixed).unwrap();
        assert_eq!(extracted_id, id);
        assert_eq!(payload, data);
    }
    
    #[test]
    fn test_connection_id_errors() {
        // Test decode error - insufficient data
        let short_data = [0x12, 0x34]; // only 2 bytes
        let result = decode_connection_id(&short_data);
        assert!(result.is_err());

        // Test extract error - insufficient data
        let result = extract_connection_id_and_payload(&short_data);
        assert!(result.is_err());

        // Test empty data
        let empty_data = [];
        let result = decode_connection_id(&empty_data);
        assert!(result.is_err());
    }

    #[test]
    fn test_trim_server_url_slashes() {
        // Test single trailing slash
        assert_eq!(
            trim_server_url_slashes("wss://example.com/"),
            "wss://example.com"
        );

        // Test multiple trailing slashes
        assert_eq!(
            trim_server_url_slashes("wss://example.com//"),
            "wss://example.com"
        );
        assert_eq!(
            trim_server_url_slashes("wss://example.com///"),
            "wss://example.com"
        );

        // Test URL without trailing slash
        assert_eq!(
            trim_server_url_slashes("wss://example.com"),
            "wss://example.com"
        );
        assert_eq!(
            trim_server_url_slashes("wss://example.com:8080"),
            "wss://example.com:8080"
        );

        // Test slashes in the middle of the path (should not be removed)
        assert_eq!(
            trim_server_url_slashes("wss://example.com/path/to/endpoint"),
            "wss://example.com/path/to/endpoint"
        );

        // Test multiple trailing slashes in path
        assert_eq!(
            trim_server_url_slashes("wss://example.com/path///"),
            "wss://example.com/path"
        );

        // Test empty string
        assert_eq!(trim_server_url_slashes(""), "");

        // Test string with only slashes
        assert_eq!(trim_server_url_slashes("///"), "");
    }

    #[test]
    fn test_generate_origin_header() {
        // Test wss:// URL
        let wss_url = Url::parse("wss://example.com:8080/path").unwrap();
        let origin = generate_origin_header(&wss_url);
        assert_eq!(origin, "https://example.com:8080");

        // Test ws:// URL
        let ws_url = Url::parse("ws://example.com:3000/path").unwrap();
        let origin = generate_origin_header(&ws_url);
        assert_eq!(origin, "http://example.com:3000");

        // Test default port (wss)
        let wss_default_url = Url::parse("wss://example.com/path").unwrap();
        let origin = generate_origin_header(&wss_default_url);
        assert_eq!(origin, "https://example.com");

        // Test default port (ws)
        let ws_default_url = Url::parse("ws://example.com/path").unwrap();
        let origin = generate_origin_header(&ws_default_url);
        assert_eq!(origin, "http://example.com");

        // Test wss port 443 (default port)
        let wss_443_url = Url::parse("wss://example.com:443/path").unwrap();
        let origin = generate_origin_header(&wss_443_url);
        assert_eq!(origin, "https://example.com");

        // Test ws port 80 (default port)
        let ws_80_url = Url::parse("ws://example.com:80/path").unwrap();
        let origin = generate_origin_header(&ws_80_url);
        assert_eq!(origin, "http://example.com");

        // Test localhost
        let localhost_url = Url::parse("ws://localhost:8080/path").unwrap();
        let origin = generate_origin_header(&localhost_url);
        assert_eq!(origin, "http://localhost:8080");
    }

    #[test]
    fn test_connection_id_integration() {
        // Test full encode/decode workflow
        let generator = ConnectionIdGenerator::new();

        // Generate multiple connection IDs
        let ids: Vec<ConnectionId> = (0..10).map(|_| generator.next_id()).collect();

        // Verify IDs are sequential
        for (i, &id) in ids.iter().enumerate() {
            assert_eq!(id, (i + 1) as u32);
        }

        // Test packet processing flow
        let test_data = b"Hello, WSSTun!";
        for &id in &ids {
            // Add prefix
            let prefixed = add_connection_id_prefix(id, test_data);

            // Extract connection ID and payload
            let (extracted_id, payload) = extract_connection_id_and_payload(&prefixed).unwrap();

            // Verify results
            assert_eq!(extracted_id, id);
            assert_eq!(payload, test_data);
        }

        // Simulate concurrency safety
        use std::sync::Arc;
        use std::thread;

        let shared_generator = Arc::new(generator);
        let handles: Vec<_> = (0..4).map(|_| {
            let generator_clone = Arc::clone(&shared_generator);
            thread::spawn(move || {
                let mut local_ids = Vec::new();
                for _ in 0..25 {
                    local_ids.push(generator_clone.next_id());
                }
                local_ids
            })
        }).collect();

        let mut all_ids = Vec::new();
        for handle in handles {
            all_ids.extend(handle.join().unwrap());
        }

        // Verify all IDs are unique
        all_ids.sort();
        for i in 0..all_ids.len() - 1 {
            assert_ne!(all_ids[i], all_ids[i + 1], "Duplicate connection ID found");
        }

        // Verify IDs are within reasonable range (11-110, as 10 were generated earlier)
        assert!(all_ids.iter().all(|&id| id >= 11 && id <= 110));
    }

    #[test]
    fn test_connection_id_concurrency() {
        // Ensure generator works correctly under concurrency
        use std::sync::Arc;
        use std::thread;
        let generator = Arc::new(ConnectionIdGenerator::new());
        let handles: Vec<_> = (0..8).map(|_| {
            let g = Arc::clone(&generator);
            thread::spawn(move || {
                let mut ids = Vec::new();
                for _ in 0..100 {
                    ids.push(g.next_id());
                }
                ids
            })
        }).collect();
        let mut all = Vec::new();
        for h in handles { all.extend(h.join().unwrap()); }
        // Check uniqueness
        let mut sorted = all.clone();
        sorted.sort();
        sorted.dedup();
        assert_eq!(sorted.len(), all.len());
    }

    #[test]
    fn test_connection_id_uniqueness() {
        // Check uniqueness across a large sample
        let g = ConnectionIdGenerator::new();
        let ids: Vec<_> = (0..1000).map(|_| g.next_id()).collect();
        let mut sorted = ids.clone();
        sorted.sort();
        sorted.dedup();
        assert_eq!(sorted.len(), ids.len());
    }

    #[test]
    fn test_prepare_ws_url() {
        // Valid ws scheme
        let url = prepare_ws_url("ws://example.com").unwrap();
        assert_eq!(url.scheme(), "ws");
        // Valid wss scheme
        let url = prepare_ws_url("wss://example.com").unwrap();
        assert_eq!(url.scheme(), "wss");
        // Invalid scheme
        let err = prepare_ws_url("http://example.com").err().unwrap();
        let msg = format!("{}", err);
        assert!(msg.contains("Must be 'ws' or 'wss'"));
    }

    #[test]
    fn test_serialize_hub_message() {
        // Ensure serialization produces expected JSON tag
        let msg = HubMessage::RegisterService { service_id: "svc".into() };
        let json = serialize_hub_message(&msg).expect("serialize ok");
        assert!(json.contains("\"type\":\"register_service\""));
        assert!(json.contains("\"service_id\":\"svc\""));
    }

    #[test]
    fn test_serialize_client_mux_message() {
        let msg = ClientMuxMessage::InitializeSession;
        let json = serialize_client_mux_message(&msg).expect("serialize ok");
        assert!(json.contains("\"type\":\"initialize_session\""));
    }
}

/// Tunnel ID type definition
pub type TunnelId = u32;

/// Tunnel ID generator - using atomic increment counter
#[derive(Debug, Clone)]
pub struct TunnelIdGenerator {
    counter: Arc<AtomicU32>,
}

impl TunnelIdGenerator {
    /// Create new tunnel ID generator
    pub fn new() -> Self {
        Self {
            counter: Arc::new(AtomicU32::new(1)), // Start from 1, avoid zero value
        }
    }

    /// Generate next tunnel ID
    pub fn next_id(&self) -> TunnelId {
        self.counter.fetch_add(1, Ordering::Relaxed)
    }
    

}

impl Default for TunnelIdGenerator {
    fn default() -> Self {
        Self::new()
    }
}

// === Client Mux mode message protocol ===
// For single WebSocket connection multiplexing communication between client and server

/// Client Mux mode control message enumeration
/// Maintains consistent design philosophy with HubMessage in hub_service.rs, using JSON serialization
/// Now supports all client modes: Forward, Proxy, HubService
#[derive(Serialize, Deserialize, Debug, Clone)]
#[serde(tag = "type", content = "payload")]
pub enum ClientMuxMessage {
    /// Client -> Server: Request to establish a new stream
    #[serde(rename = "request_new_stream")]
    RequestNewStream {
        connection_id: ConnectionId,
        /// For 'forward' mode, target address needs to be pre-specified
        /// For 'proxy' mode, this is None
        target_addr: Option<String>,
    },

    /// Server -> Client: Response to RequestNewStream
    #[serde(rename = "new_stream_response")]
    NewStreamResponse {
        connection_id: ConnectionId,
        success: bool,
        error_message: Option<String>,
    },

    /// Client -> Server: Heartbeat to maintain connection
    #[serde(rename = "heartbeat")]
    Heartbeat {
        timestamp: u64,
    },

    /// Bidirectional: Notify that a stream has been closed
    #[serde(rename = "close_stream")]
    CloseStream {
        connection_id: ConnectionId,
    },

    // === Hub service related messages ===

    /// Client -> Server: Register as Hub service
    #[serde(rename = "register_hub_service")]
    RegisterHubService {
        service_id: String,
    },

    /// Server -> Client: Hub service registration confirmation
    #[serde(rename = "hub_service_registered")]
    HubServiceRegistered {
        service_id: String,
        success: bool,
        error_message: Option<String>,
    },

    /// Server -> Client: New client connection to Hub service
    #[serde(rename = "new_client_connection")]
    NewClientConnection {
        client_connection_id: ConnectionId,
    },

    /// Server -> Client: Client disconnected from Hub service
    #[serde(rename = "client_disconnected")]
    ClientDisconnected {
        client_connection_id: ConnectionId,
    },

    /// Client -> Server: Hub service notifies target connection closed
    #[serde(rename = "target_connection_closed")]
    TargetConnectionClosed {
        client_connection_id: ConnectionId,
    },

    // === Client to Hub service connection requests ===

    /// Client -> Server: Request connection to Hub service
    #[serde(rename = "connect_to_hub_service")]
    ConnectToHubService {
        service_id: String,
    },

    /// Server -> Client: Hub service connection confirmation
    #[serde(rename = "hub_service_connection")]
    HubServiceConnection {
        service_id: String,
        client_connection_id: ConnectionId,
        success: bool,
        error_message: Option<String>,
    },

    /// Server -> Client: Hub service unavailable
    #[serde(rename = "hub_service_unavailable")]
    HubServiceUnavailable {
        service_id: String,
        reason: String,
    },

    // === Mux connection pool handshake messages ===

    /// Client -> Server: Request to initialize a new session
    #[serde(rename = "initialize_session")]
    InitializeSession,

    /// Server -> Client: Response to InitializeSession, returns session ID
    #[serde(rename = "session_established")]
    SessionEstablished {
        session_id: u32,
    },

    /// Client -> Server: Join existing session using known session ID
    #[serde(rename = "join_session")]
    JoinSession {
        session_id: u32,
    },
}

/// Safely serialize ClientMuxMessage to JSON string
pub fn serialize_client_mux_message(msg: &ClientMuxMessage) -> std::result::Result<String, serde_json::Error> {
    serde_json::to_string(msg)
}

/// Deserialize ClientMuxMessage from JSON string
pub fn deserialize_client_mux_message(json_str: &str) -> std::result::Result<ClientMuxMessage, serde_json::Error> {
    serde_json::from_str(json_str)
}



// === Dynamic tunnel mode related message types ===

/// Control channel messages (server <-> hub_service)
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum ControlMessage {
    /// Hub -> Server: Register Hub service
    #[serde(rename = "register_hub")]
    RegisterHub { hub_id: String },

    /// Server -> Hub: Instruct Hub to prepare for new client
    #[serde(rename = "prepare_new_tunnel")]
    PrepareNewTunnel { client_id: String },

    /// Hub -> Server: Confirm ready
    #[serde(rename = "tunnel_ready")]
    TunnelReady { client_id: String, tunnel_id: TunnelId },

    /// Hub -> Server: Report tunnel creation failure
    #[serde(rename = "tunnel_creation_failed")]
    TunnelCreationFailed { client_id: String, reason: String },
}

/// Data tunnel messages (hub_service -> server)
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum DataTunnelMessage {
    /// First message sent on new data tunnel to identify itself
    #[serde(rename = "register_data_tunnel")]
    RegisterDataTunnel { client_id: String },
}

/// Safely serialize ControlMessage to JSON string
pub fn serialize_control_message(msg: &ControlMessage) -> std::result::Result<String, serde_json::Error> {
    serde_json::to_string(msg)
}

/// Deserialize ControlMessage from JSON string
pub fn deserialize_control_message(json_str: &str) -> std::result::Result<ControlMessage, serde_json::Error> {
    serde_json::from_str(json_str)
}



/// Safely serialize DataTunnelMessage to JSON string
pub fn serialize_data_tunnel_message(msg: &DataTunnelMessage) -> std::result::Result<String, serde_json::Error> {
    serde_json::to_string(msg)
}

/// Deserialize DataTunnelMessage from JSON string
pub fn deserialize_data_tunnel_message(json_str: &str) -> std::result::Result<DataTunnelMessage, serde_json::Error> {
    serde_json::from_str(json_str)
}


