//! Unified Hub service provider
//! Replaces bridge_provider, supports dynamic forwarding and proxy

use crate::common::{
    connect_websocket_with_retry, prepare_ws_url, HubMessage, Result, TunnelError,
    ConnectionId,
    extract_connection_id_and_payload, add_connection_id_prefix,
    HubServiceConfig, ReconnectState, ReconnectSignal,
};
use crate::proxy_server::connect_to_target;
use crate::heartbeat::start_heartbeat_task;
use futures_util::stream::SplitSink;
use futures_util::{SinkExt, StreamExt};
use log::{debug, error, info, warn};

use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use tokio::net::TcpStream;
use tokio::sync::{Mutex, watch};
use tokio::task::JoinHandle;
use tokio_tungstenite::{tungstenite::Message, MaybeTlsStream, WebSocketStream};

/// Connection state enumeration
#[derive(Debug, Clone)]
enum ConnectionState {
    Connected {
        reader: Arc<Mutex<tokio::io::ReadHalf<TcpStream>>>,
        writer: Arc<Mutex<tokio::io::WriteHalf<TcpStream>>>,
    },
}

/// Hub service connection state management
struct HubServiceState {
    active_connections: HashMap<ConnectionId, ConnectionState>,
    pending_data: HashMap<ConnectionId, Vec<Vec<u8>>>, // Cache data waiting for connection establishment
}

/// Global service state management
struct ServiceManager {
    config: HubServiceConfig,
    reconnect_state: ReconnectState,
    heartbeat_task: Option<JoinHandle<()>>,
    message_handler: Option<JoinHandle<()>>,
    ws_sink: Option<Arc<Mutex<SplitSink<WebSocketStream<MaybeTlsStream<TcpStream>>, Message>>>>,
    service_state: Arc<Mutex<HubServiceState>>,
    reconnect_tx: watch::Sender<Option<ReconnectSignal>>,
    reconnect_rx: watch::Receiver<Option<ReconnectSignal>>,
}

impl ServiceManager {
    fn new(config: HubServiceConfig) -> Self {
        let (reconnect_tx, reconnect_rx) = watch::channel(None);
        Self {
            config,
            reconnect_state: ReconnectState::default(),
            heartbeat_task: None,
            message_handler: None,
            ws_sink: None,
            service_state: Arc::new(Mutex::new(HubServiceState {
                active_connections: HashMap::new(),
                pending_data: HashMap::new(),
            })),
            reconnect_tx,
            reconnect_rx,
        }
    }

    async fn cleanup_resources(&mut self) {
        info!("Starting resource cleanup, service ID: {}", self.config.service_id);
        if let Some(heartbeat_task) = self.heartbeat_task.take() {
            heartbeat_task.abort();
            debug!("Stopped heartbeat task");
        }
        if let Some(message_handler) = self.message_handler.take() {
            message_handler.abort();
            debug!("Stopped message handling task");
        }
        if let Some(ws_sink) = self.ws_sink.take() {
            let mut sink = ws_sink.lock().await;
            let _ = sink.close().await;
            debug!("Closed WebSocket connection");
        }
        {
            let mut state = self.service_state.lock().await;
            let connection_count = state.active_connections.len();
            state.active_connections.clear();
            state.pending_data.clear();
            info!("Cleaned up {} active TCP connections", connection_count);
        }
        info!("Resource cleanup completed, service ID: {}", self.config.service_id);
    }

    async fn try_reconnect(&mut self) -> Result<()> {
        info!("Attempting to reconnect, service ID: {}", self.config.service_id);
        self.cleanup_resources().await;

        let server_url = crate::common::trim_server_url_slashes(&self.config.server);
        let ws_url_str = format!("{}/hub", server_url);
        let ws_url = prepare_ws_url(&ws_url_str)?;

        let ws_stream = connect_websocket_with_retry(&ws_url, 3, 1000, None).await?;
        let (ws_sink, ws_stream_reader) = ws_stream.split();
        let ws_sink = Arc::new(Mutex::new(ws_sink));

        let register_msg = HubMessage::RegisterService {
            service_id: self.config.service_id.clone(),
        };
        let register_json = serde_json::to_string(&register_msg)?;
        ws_sink.lock().await.send(Message::Text(register_json)).await?;

        info!("Resent service registration message, service ID: {}", self.config.service_id);

        let heartbeat_task = start_heartbeat_task(
            Arc::clone(&ws_sink),
            self.config.service_id.clone(),
            self.config.heartbeat_interval,
            self.reconnect_tx.clone(),
        );

        let message_handler = start_message_handler(
            ws_stream_reader,
            Arc::clone(&ws_sink),
            Arc::clone(&self.service_state),
            self.config.service_id.clone(),
            self.reconnect_tx.clone(),
        );

        self.heartbeat_task = Some(heartbeat_task);
        self.message_handler = Some(message_handler);
        self.ws_sink = Some(ws_sink);

        info!("Reconnection successful, service ID: {}", self.config.service_id);
        Ok(())
    }
}

/// Start message handling task
fn start_message_handler(
    mut ws_stream_reader: futures_util::stream::SplitStream<
        WebSocketStream<MaybeTlsStream<TcpStream>>,
    >,
    ws_sink: Arc<Mutex<SplitSink<WebSocketStream<MaybeTlsStream<TcpStream>>, Message>>>,
    service_state: Arc<Mutex<HubServiceState>>,
    service_id: String,
    reconnect_tx: watch::Sender<Option<ReconnectSignal>>,
) -> JoinHandle<()> {
    tokio::spawn(async move {
        let mut connection_lost_signaled = false;

        // Start message processing loop
        while let Some(message) = ws_stream_reader.next().await {
            let message = match message {
                Ok(msg) => msg,
                Err(e) => {
                    error!("Error receiving WebSocket message: {}", e);
                    // Send connection lost signal
                    if let Err(e) = reconnect_tx.send(Some(ReconnectSignal::ConnectionLost)) {
                        error!("Failed to send reconnect signal: {}", e);
                    } else {
                        info!("Sent connection lost reconnect signal, service ID: {}", service_id);
                    }
                    connection_lost_signaled = true;
                    break;
                }
            };

            match message {
                Message::Text(text) => {
                    // Handle control messages
                    if let Err(e) = handle_control_message(
                        &text,
                        Arc::clone(&ws_sink),
                        Arc::clone(&service_state),
                        &service_id,
                    )
                    .await
                    {
                        error!("Error handling control message: {}", e);
                    }
                }
                Message::Binary(data) => {
                    // Handle binary data messages
                    if let Err(e) =
                        handle_binary_message(data.to_vec(), Arc::clone(&service_state), Arc::clone(&ws_sink)).await
                    {
                        error!("Error handling binary message: {}", e);
                    }
                }
                Message::Close(_) => {
                    info!("Hub server closed WebSocket connection");
                    // Send connection lost signal
                    if let Err(e) = reconnect_tx.send(Some(ReconnectSignal::ConnectionLost)) {
                        error!("Failed to send reconnect signal: {}", e);
                    } else {
                        info!("Sent connection close reconnect signal, service ID: {}", service_id);
                    }
                    connection_lost_signaled = true;
                    break;
                }
                Message::Ping(_) => debug!("Received ping"),
                Message::Pong(_) => debug!("Received pong"),
                _ => debug!("Received other message type"),
            }
        }

        // Ensure reconnect signal is sent when task ends (if not already sent)
        if !connection_lost_signaled {
            warn!("WebSocket stream ended unexpectedly, sending reconnect signal, service ID: {}", service_id);
            if let Err(e) = reconnect_tx.send(Some(ReconnectSignal::ConnectionLost)) {
                error!("Failed to send reconnect signal: {}", e);
            } else {
                info!("Sent stream end reconnect signal, service ID: {}", service_id);
            }
        }

        info!("Message handling task ended, service ID: {}", service_id);
    })
}

/// Main entry function for Hub service
pub async fn run_hub_service(config: HubServiceConfig) -> Result<()> {
    info!("Starting Hub service, service ID: {}", config.service_id);
    info!("Server address: {}", config.server);

    let mut service_manager = ServiceManager::new(config.clone());

    if let Err(e) = service_manager.try_reconnect().await {
        error!("Initial connection failed, service ID: {}: {}, starting reconnection process", config.service_id, e);
        if service_manager.reconnect_tx.send(Some(ReconnectSignal::InitialConnectionFailed)).is_err() {
            error!("Failed to send initial reconnect signal");
            return Err(TunnelError::Other("Failed to send reconnect signal".to_string()));
        }
        info!("Sent initial reconnect signal, entering main loop");
    }

    loop {
        tokio::select! {
            _ = service_manager.reconnect_rx.changed() => {
                let signal_opt = service_manager.reconnect_rx.borrow().clone();
                if let Some(signal) = signal_opt {
                    warn!("Received {:?} signal, starting reconnection process, service ID: {}", signal, config.service_id);
                    let _ = service_manager.reconnect_tx.send(None);

                    // To avoid borrow checker issues, we need to implement reconnection logic directly here
                    service_manager.reconnect_state.is_reconnecting = true;
                    let mut attempt = 1;
                    
                    loop {
                        match service_manager.try_reconnect().await {
                            Ok(()) => {
                                info!("Reconnection successful, service ID: {}", config.service_id);
                                service_manager.reconnect_state.is_reconnecting = false;
                                service_manager.reconnect_state.attempt_count = 0;
                                break;
                            }
                            Err(e) => {
                                error!("Reconnection attempt {} failed, service ID: {}: {}", attempt, config.service_id, e);
                                service_manager.reconnect_state.attempt_count = attempt;

                                // Calculate backoff delay
                                let delay_ms = if attempt <= 3 {
                                    1000 // Fast reconnect: 1 second
                                } else if attempt <= 10 {
                                    5000 // Slow reconnect: 5 seconds
                                } else {
                                    30000 // Persistent reconnect: 30 seconds
                                };

                                info!("Waiting {}ms before next reconnection attempt", delay_ms);
                                tokio::time::sleep(std::time::Duration::from_millis(delay_ms)).await;
                                attempt += 1;
                            }
                        }
                    }
                }
            }
            _ = tokio::time::sleep(Duration::from_secs(60)) => {
                if service_manager.reconnect_state.is_reconnecting {
                    info!("Service is reconnecting, service ID: {}", config.service_id);
                } else {
                    debug!("Service running normally, service ID: {}", config.service_id);
                }
            }
        }
    }
}

/// Handle control messages (JSON format)
async fn handle_control_message(
    text: &str,
    ws_sink: Arc<
        Mutex<futures_util::stream::SplitSink<WebSocketStream<MaybeTlsStream<TcpStream>>, Message>>,
    >,
    service_state: Arc<Mutex<HubServiceState>>,
    service_id: &str,
) -> Result<()> {
    debug!("Processing control message from service {}: {}", service_id, text);

    match serde_json::from_str::<HubMessage>(text) {
        Ok(HubMessage::ServiceForwardInstruction {
            connection_id,
            instruction_type,
            target_addr,
        }) => {
            info!(
                "Received forward instruction - Connection ID: {}, Type: {}, Target: {}",
                connection_id, instruction_type, target_addr
            );

            // Process forward instruction asynchronously to avoid blocking message loop
            let ws_sink_clone = Arc::clone(&ws_sink);
            let service_state_clone = Arc::clone(&service_state);
            let service_id_owned = service_id.to_string();

            tokio::spawn(async move {
                if let Err(e) = handle_forward_instruction(
                    connection_id,
                    target_addr,
                    ws_sink_clone,
                    service_state_clone,
                    service_id_owned,
                )
                .await
                {
                    error!("Failed to process forward instruction: {}", e);
                }
            });
        }
        Ok(HubMessage::ClientDisconnectedNotification {
            client_connection_id,
        }) => {
            info!("Client disconnected: {}", client_connection_id);
            let mut state = service_state.lock().await;
            if state.active_connections.remove(&client_connection_id).is_some() {
                debug!("Cleaned up target connection for connection ID {}", client_connection_id);
            }
            state.pending_data.remove(&client_connection_id);
        }
        Ok(msg) => {
            debug!("Received other control message: {:?}", msg);
        }
        Err(e) => {
            error!("Failed to parse control message: {} - {}", e, text);
            return Err(TunnelError::JsonError(e));
        }
    }
    Ok(())
}

/// Handle binary data messages
async fn handle_binary_message(
    data: Vec<u8>,
    service_state: Arc<Mutex<HubServiceState>>,
    ws_sink: Arc<Mutex<futures_util::stream::SplitSink<WebSocketStream<MaybeTlsStream<TcpStream>>, Message>>>,
) -> Result<()> {
    // Extract connection_id from data (fixed 4-byte prefix)
    let (client_id, payload) = extract_connection_id_and_payload(&data)?;

    debug!("Received {} bytes of data from client {}", payload.len(), client_id);

    // Forward to corresponding target connection
    let state = service_state.lock().await;
    match state.active_connections.get(&client_id) {
        Some(ConnectionState::Connected { writer, .. }) => {
            let target_writer = writer.clone();
            let client_id_for_log = client_id;
            let payload_len = payload.len();
            let payload_owned = payload.to_vec();
            drop(state); // Release lock

            // 🔧 Phase 1 fix: Synchronous write ensures data order, remove async spawn
            let mut should_disconnect = false;

            // Add timeout for write operation (increased to 15 seconds for SSH key exchange)
            let result = tokio::time::timeout(Duration::from_secs(15), async {
                let mut writer = target_writer.lock().await;
                writer.write_all(&payload_owned).await
            }).await;

            match result {
                Ok(Ok(())) => {
                    debug!(
                        "Forwarded {} bytes to target connection, client: {}",
                        payload_len, client_id_for_log
                    );
                }
                Ok(Err(e)) => {
                    error!("Failed to write data to target, client {}: {}", client_id_for_log, e);
                    should_disconnect = true;
                }
                Err(_) => {
                    error!(
                        "Timeout writing data to target (15s), client {}, data length: {}",
                        client_id_for_log, payload_len
                    );
                    should_disconnect = true;
                }
            }

            // 🔧 If write failed, clean up connection and notify Server
            if should_disconnect {
                // Clean up connection state
                {
                    let mut state = service_state.lock().await;
                    state.active_connections.remove(&client_id_for_log);
                    state.pending_data.remove(&client_id_for_log);
                    debug!("Cleaned up connection state - client: {}", client_id_for_log);
                }

                // Send disconnect notification to Server
                let disconnect_msg = crate::common::HubMessage::ClientConnectionEndedByTarget {
                    client_connection_id: client_id_for_log,
                };

                if let Ok(json) = serde_json::to_string(&disconnect_msg) {
                    let mut sink = ws_sink.lock().await;
                    if let Err(e) = sink.send(Message::Text(json.into())).await {
                        error!("Failed to send target disconnect notification: {}", e);
                    } else {
                        debug!("Sent target write failure disconnect notification - connection ID: {}", client_id_for_log);
                    }
                }
            }
        }
        None => {
            warn!("Target connection not found for client {}", client_id);
        }
    }

    Ok(())
}

/// Handle forward instruction
async fn handle_forward_instruction(
    connection_id: ConnectionId,
    target_addr: String,
    ws_sink: Arc<
        Mutex<futures_util::stream::SplitSink<WebSocketStream<MaybeTlsStream<TcpStream>>, Message>>,
    >,
    service_state: Arc<Mutex<HubServiceState>>,
    _service_id: String,
) -> Result<()> {
    info!("Processing forward instruction - connecting to target: {}", target_addr);

    // Parse target address
    let parts: Vec<&str> = target_addr.split(':').collect();
    if parts.len() != 2 {
        error!("Invalid target address format: {}", target_addr);
        return Err(TunnelError::Other("Invalid target address format".to_string()));
    }

    let host = parts[0];
    let port = parts[1].parse::<u16>().map_err(|e| {
        error!("Invalid port number: {}: {}", parts[1], e);
        TunnelError::Other(format!("Invalid port number: {}", e))
    })?;

    // Connect to target
    let target_stream = connect_to_target(host, port).await?;
    info!("Connected to target: {}", target_addr);

    // Split TCP stream to avoid read-write lock contention
    let (target_reader, target_writer) = tokio::io::split(target_stream);
    let target_reader = Arc::new(Mutex::new(target_reader));
    let target_writer = Arc::new(Mutex::new(target_writer));

    // Update connection state and synchronously replay cached data
    {
        let mut state = service_state.lock().await;

        // Get and remove cached data
        let cached_data = state.pending_data.remove(&connection_id);

        // Set connection state to connected first
        state.active_connections.insert(
            connection_id.clone(),
            ConnectionState::Connected {
                reader: target_reader.clone(),
                writer: target_writer.clone(),
            },
        );

        // Release state lock then synchronously replay cached data to ensure order
        drop(state);

        // Synchronously replay cached data to ensure new messages don't jump the queue
        if let Some(cached_data) = cached_data {
            let total_packets = cached_data.len();

            // Set timeout for entire replay process, acquire lock once for efficiency
            match tokio::time::timeout(Duration::from_secs(30), async {
                let mut writer = target_writer.lock().await;  // Acquire lock only once

                for (i, data) in cached_data.into_iter().enumerate() {
                    match writer.write_all(&data).await {
                        Ok(()) => {
                            debug!("Replayed cached data packet #{}, {} bytes", i + 1, data.len());
                        }
                        Err(e) => {
                            error!("Failed to replay data packet #{}: {}", i + 1, e);
                            return Err(e);
                        }
                    }
                }

                Ok::<(), std::io::Error>(())
            }).await {
                Ok(Ok(())) => {
                    debug!("Completed synchronous replay of {} cached data packets, client: {}", total_packets, connection_id);
                }
                Ok(Err(e)) => {
                    error!("Failed to replay cached data, client {}: {}", connection_id, e);
                    let mut state = service_state.lock().await;
                    state.active_connections.remove(&connection_id);
                    return Err(TunnelError::Other(format!("Cache replay failed: {}", e)));
                }
                Err(_) => {
                    error!("Timeout replaying cached data (30s), client {}", connection_id);
                    let mut state = service_state.lock().await;
                    state.active_connections.remove(&connection_id);
                    return Err(TunnelError::Other("Cache replay timeout".to_string()));
                }
            }
        }
    }

    // Start target to WebSocket data forwarding task
    tokio::spawn(async move {
        if let Err(e) = forward_target_to_websocket(connection_id, service_state, ws_sink).await {
            error!("Target to WebSocket forwarding failed: {}", e);
        }
    });

    Ok(())
}

/// Forward data from target connection to WebSocket
async fn forward_target_to_websocket(
    connection_id: ConnectionId,
    service_state: Arc<Mutex<HubServiceState>>,
    ws_sink: Arc<
        Mutex<futures_util::stream::SplitSink<WebSocketStream<MaybeTlsStream<TcpStream>>, Message>>,
    >,
) -> Result<()> {
    // Get target connection reader
    let target_reader = {
        let state = service_state.lock().await;
        match state.active_connections.get(&connection_id) {
            Some(ConnectionState::Connected { reader, .. }) => Some(reader.clone()),
            _ => None,
        }
    };

    let target_reader = match target_reader {
        Some(reader) => reader,
        None => {
            error!("Target connection not found for connection ID {}", connection_id);
            return Ok(());
        }
    };

    let mut buffer = vec![0u8; 8192];
    loop {
        let n = {
            let mut reader = target_reader.lock().await;
            match reader.read(&mut buffer).await {
                Ok(0) => {
                    debug!("Target connection closed (EOF) - connection ID: {}", connection_id);

                    // Send disconnect notification to Server
                    let disconnect_msg = crate::common::HubMessage::ClientConnectionEndedByTarget {
                        client_connection_id: connection_id,
                    };

                    if let Ok(json) = serde_json::to_string(&disconnect_msg) {
                        let mut sink = ws_sink.lock().await;
                        if let Err(e) = sink.send(Message::Text(json.into())).await {
                            error!("Failed to send target disconnect notification: {}", e);
                        } else {
                            debug!("Sent target disconnect notification - connection ID: {}", connection_id);
                        }
                    }

                    break;
                }
                Ok(n) => n,
                Err(e) => {
                    error!("Failed to read from target connection: {}", e);

                    // Send disconnect notification on read error as well
                    let disconnect_msg = crate::common::HubMessage::ClientConnectionEndedByTarget {
                        client_connection_id: connection_id,
                    };

                    if let Ok(json) = serde_json::to_string(&disconnect_msg) {
                        let mut sink = ws_sink.lock().await;
                        if let Err(e) = sink.send(Message::Text(json.into())).await {
                            error!("Failed to send target disconnect notification: {}", e);
                        } else {
                            debug!("Sent target read failure disconnect notification - connection ID: {}", connection_id);
                        }
                    }

                    break;
                }
            }
        };

        let data = &buffer[..n];

        // Add connection_id prefix and send to WebSocket (fixed 4 bytes, no separator)
        let prefixed_data = add_connection_id_prefix(connection_id, data);
        let prefixed_data_len = prefixed_data.len();

        debug!(
            "Forwarding {} bytes from target to WebSocket - connection ID: {}, total length after prefix: {}",
            n, connection_id, prefixed_data_len
        );
        debug!("Target data first 50 bytes: {:?}", &data[..std::cmp::min(50, n)]);

        {
            let mut sink = ws_sink.lock().await;
            if let Err(e) = sink.send(Message::Binary(prefixed_data)).await {
                error!("Failed to send data to WebSocket: {}", e);
                break;
            }
        }
    }

    // Clean up connection
    {
        let mut state = service_state.lock().await;
        state.active_connections.remove(&connection_id);
    }

    info!("Target to WebSocket forwarding task ended - connection ID: {}", connection_id);
    Ok(())
}


