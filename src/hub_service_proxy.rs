//! Handle /hub/proxy WebSocket connections
//! Parse proxy protocol sent by clients, distribute proxy instructions to corresponding hub-service

use crate::common::{
    HubMessage, Result, TunnelError, ConnectionId,
};
use crate::proxy_server::parse_proxy_request;
use crate::server_hub::ServiceHub;
use axum::{
    extract::{
        ws::{Message, WebSocket, WebSocketUpgrade},
        Extension,
        Query,
    },
    http::StatusCode,
    response::IntoResponse,
};
use bytes::Bytes;
use futures_util::{SinkExt, StreamExt};
use log::{debug, error, info, warn};

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;

// Handle WebSocket connection requests for Hub proxy functionality
pub async fn ws_handler(
    ws: WebSocketUpgrade,
    _headers: axum::http::HeaderMap,
    Query(params): Query<HashMap<String, String>>,
    Extension(service_hub): Extension<Arc<RwLock<ServiceHub>>>,
) -> impl IntoResponse {
    info!("Handling new WebSocket connection on /hub/proxy route");

    // Get service_id from query parameters
    let service_id = params.get("service_id").cloned();

    if let Some(service_id) = service_id {
        info!("/hub/proxy - Service ID: {}", service_id);

        // Check if service is available
        let is_available = {
            let hub = service_hub.read().await;
            hub.is_service_available(&service_id)
        };

        if !is_available {
            warn!("Service {} is not available", service_id);
            return StatusCode::SERVICE_UNAVAILABLE.into_response();
        }

        ws.on_upgrade(move |socket| handle_hub_proxy_socket(socket, service_id.to_string(), service_hub))
    } else {
        warn!("Missing service_id parameter");
        StatusCode::BAD_REQUEST.into_response()
    }
}

// Handle Hub proxy WebSocket connection
async fn handle_hub_proxy_socket(
    ws: WebSocket,
    service_id: String,
    service_hub: Arc<RwLock<ServiceHub>>,
) {
    info!("Establishing Hub proxy WebSocket connection, Service ID: {}", service_id);

    // Generate connection ID
    let connection_id = {
        let mut hub = service_hub.write().await;
        hub.generate_connection_id()
    };

    // Split WebSocket stream
    let (mut ws_sink, mut ws_stream) = ws.split();

    // Wait for first message and parse proxy protocol
    let proxy_request = match ws_stream.next().await {
        Some(Ok(Message::Binary(data))) => {
            // Parse proxy protocol
            let mut cursor = std::io::Cursor::new(data.clone());
            match parse_proxy_request(&mut cursor).await {
                Ok((proxy_request, response_data)) => {
                    info!(
                        "Parsed proxy request: {:?}://{}:{}",
                        proxy_request.protocol,
                        proxy_request.target_host,
                        proxy_request.target_port
                    );

                    // If there's response data, send it back to client first
                    if let Some(response_bytes) = response_data {
                        if let Err(e) = ws_sink
                            .send(Message::Binary(Bytes::from(response_bytes)))
                            .await
                        {
                            error!("Failed to send proxy response: {}", e);
                            return;
                        }
                    }

                    proxy_request
                }
                Err(e) => {
                    error!("Failed to parse proxy request: {}", e);
                    return;
                }
            }
        }
        Some(Ok(msg)) => {
            error!("Expected binary message, received: {:?}", msg);
            return;
        }
        Some(Err(e)) => {
            error!("Error receiving WebSocket message: {}", e);
            return;
        }
        None => {
            warn!("WebSocket connection closed immediately");
            return;
        }
    };

    // Send proxy instruction to hub-service (including target address)
    let target_addr = format!(
        "{}:{}",
        proxy_request.target_host, proxy_request.target_port
    );

    if let Err(e) = send_service_instruction(
        &service_hub,
        &service_id,
        connection_id,
        "proxy",
        &target_addr,
    )
    .await
    {
        error!("Failed to send proxy instruction: {}", e);
        return;
    }

    info!(
        "Sent proxy instruction to service {}, connection ID: {}, target: {}",
        service_id, connection_id, target_addr
    );

    // Reunite WebSocket
    let ws = ws_sink.reunite(ws_stream).expect("Failed to reunite WebSocket");
    let (ws_sink, ws_stream) = ws.split();
    let ws_sink = Arc::new(tokio::sync::Mutex::new(ws_sink));

    // Register client connection
    {
        let mut hub = service_hub.write().await;
        hub.register_client(
            connection_id,
            service_id.clone(),
            Arc::clone(&ws_sink),
        );
    }

    info!("Registered client connection, Connection ID: {}", connection_id);

    // Start message handling
    if let Err(e) =
        handle_client_messages(ws_stream, connection_id, service_hub.clone()).await
    {
        error!("Failed to handle client messages: {}", e);
    }

    // Clean up connection
    {
        let mut hub = service_hub.write().await;
        hub.handle_client_disconnect(connection_id).await;
    }

    info!("Hub proxy connection disconnected, Connection ID: {}", connection_id);
}

/// Send service instruction to hub-service (shared function)
async fn send_service_instruction(
    service_hub: &Arc<RwLock<ServiceHub>>,
    service_id: &str,
    connection_id: ConnectionId,
    instruction_type: &str,
    target_addr: &str,
) -> Result<()> {
    let instruction = HubMessage::ServiceForwardInstruction {
        connection_id,
        instruction_type: instruction_type.to_string(),
        target_addr: target_addr.to_string(),
    };

    let instruction_json = serde_json::to_string(&instruction)
        .map_err(|e| TunnelError::Other(format!("Failed to serialize instruction: {}", e)))?;

    // Get service provider's sink and send instruction
    let hub = service_hub.read().await;
    if let Some(hubservice_sink) = hub.get_hubservice_sink(service_id) {
        let mut sink = hubservice_sink.lock().await;
        sink.send(Message::Text(instruction_json.into()))
            .await
            .map_err(|e| TunnelError::Other(format!("Failed to send instruction: {}", e)))?;
    } else {
        return Err(TunnelError::Other(format!(
            "Service provider for service {} not found",
            service_id
        )));
    }

    Ok(())
}

/// Handle client WebSocket messages (shared function)
async fn handle_client_messages(
    mut ws_stream: futures_util::stream::SplitStream<WebSocket>,
    connection_id: ConnectionId,
    service_hub: Arc<RwLock<ServiceHub>>,
) -> Result<()> {
    while let Some(message) = ws_stream.next().await {
        let message = match message {
            Ok(msg) => msg,
            Err(e) => {
                error!("Error receiving WebSocket message: {}", e);
                return Err(TunnelError::Other(format!("WebSocket receive error: {}", e)));
            }
        };

        match message {
            Message::Binary(data) => {
                debug!(
                    "Received {} bytes of binary data from client {}",
                    data.len(),
                    connection_id
                );

                // Forward to service provider
                let hub = service_hub.read().await;
                if let Err(e) = hub
                    .forward_client_to_hubservice(connection_id, data.to_vec())
                    .await
                {
                    error!("Failed to forward data: {}", e);
                    return Err(e);
                }
            }
            Message::Text(text) => {
                debug!("Received text message from client {}: {}", connection_id, text);
                // Currently no special text message handling needed
            }
            Message::Close(_) => {
                info!("Client {} closed WebSocket connection", connection_id);
                break;
            }
            Message::Ping(_) => debug!("Received ping"),
            Message::Pong(_) => debug!("Received pong"),
        }
    }

    info!("Client message handling loop ended, Connection ID: {}", connection_id);
    Ok(())
}
