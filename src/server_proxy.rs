//! Handle /proxy WebSocket connections
//! Uses proxy_server.rs common logic to directly connect to dynamic target addresses

use crate::common::{Result, TunnelError};
use crate::proxy_server::{connect_to_target_optimized, parse_proxy_request, ConnectionManager};
use crate::server::ServerConfig;
use axum::{
    extract::{
        ws::{Message, WebSocket, WebSocketUpgrade}, Extension,
        Query,
    },
    response::IntoResponse,
};
use bytes::Bytes;
use futures_util::{SinkExt, StreamExt};
use log::{debug, error, info, warn};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use tokio::net::TcpStream;
use tokio::select;
use tokio::sync::{Mutex, OnceCell};

// Global connection manager, lazy initialization
static CONNECTION_MANAGER: OnceCell<Arc<ConnectionManager>> = OnceCell::const_new();

// Handle WebSocket connection requests for proxy functionality
pub async fn ws_handler(
    ws: WebSocketUpgrade,
    _headers: axum::http::HeaderMap,
    Query(params): Query<HashMap<String, String>>,
    Extension(server_config): Extension<Arc<ServerConfig>>,
) -> impl IntoResponse {
    info!("Handling new WebSocket connection on /proxy route");

    // Initialize connection manager (if not already initialized)
    let connection_manager = CONNECTION_MANAGER
        .get_or_init(|| async {
            Arc::new(ConnectionManager::new(
                server_config.dns_cache_size,
                server_config.dns_cache_ttl,
                server_config.connection_timeout,
            ))
        })
        .await;

    // For direct proxy mode, target address is obtained from query parameters (optional)
    let target_from_query = params.get("target").cloned();

    if let Some(target) = target_from_query {
        info!("/proxy target address (direct mode): {}", target);
        let connection_manager = Arc::clone(connection_manager);
        let server_config = Arc::clone(&server_config);
        ws.on_upgrade(move |socket| {
            handle_direct_proxy_socket(socket, target, connection_manager, server_config)
        })
    } else {
        info!("/proxy dynamic proxy mode");
        let connection_manager = Arc::clone(connection_manager);
        let server_config = Arc::clone(&server_config);
        ws.on_upgrade(move |socket| {
            handle_dynamic_proxy_socket(socket, connection_manager, server_config)
        })
    }
}

// Handle direct proxy WebSocket connection (known target address)
async fn handle_direct_proxy_socket(
    ws: WebSocket,
    target_addr: String,
    connection_manager: Arc<ConnectionManager>,
    server_config: Arc<ServerConfig>,
) {
    info!("Establishing direct proxy WebSocket connection, target: {}", target_addr);

    // Connect directly to target TCP service
    let parts: Vec<&str> = target_addr.split(':').collect();
    if parts.len() != 2 {
        error!("Invalid target address format: {}", target_addr);
        return;
    }

    let host = parts[0];
    let port = match parts[1].parse::<u16>() {
        Ok(p) => p,
        Err(e) => {
            error!("Invalid port number: {}: {}", parts[1], e);
            return;
        }
    };

    match connect_to_target_optimized(&connection_manager, host, port).await {
        Ok(target_stream) => {
            info!("Connected to target: {}", target_addr);
            if let Err(e) = handle_connection(ws, target_stream, server_config).await {
                error!("Error handling connection: {}", e);
            }
        }
        Err(e) => {
            error!("Failed to connect to target {}: {}", target_addr, e);
            // WebSocket will automatically close when it goes out of scope
        }
    }
}

// Handle dynamic proxy WebSocket connection (parse target address from WebSocket data)
async fn handle_dynamic_proxy_socket(
    ws: WebSocket,
    connection_manager: Arc<ConnectionManager>,
    server_config: Arc<ServerConfig>,
) {
    info!("Establishing dynamic proxy WebSocket connection");

    // Receive first message from WebSocket, should contain client's proxy request
    let (mut ws_sender, mut ws_receiver) = ws.split();

    let target_stream = match ws_receiver.next().await {
        Some(Ok(Message::Binary(data))) => {
            // Parse proxy protocol
            let mut cursor = std::io::Cursor::new(data.clone());
            match parse_proxy_request(&mut cursor).await {
                Ok((proxy_request, response_data)) => {
                    info!(
                        "Parsed proxy request: {:?}://{}:{}",
                        proxy_request.protocol,
                        proxy_request.target_host,
                        proxy_request.target_port
                    );

                    // If there's response data, send it back to client first
                    if let Some(response_bytes) = response_data {
                        if let Err(e) = ws_sender
                            .send(Message::Binary(Bytes::from(response_bytes)))
                            .await
                        {
                            error!("Failed to send proxy response: {}", e);
                            return;
                        }
                    }

                    // Use optimized connection manager to connect to target
                    match connect_to_target_optimized(
                        &connection_manager,
                        &proxy_request.target_host,
                        proxy_request.target_port,
                    )
                    .await
                    {
                        Ok(stream) => stream,
                        Err(e) => {
                            error!("Failed to connect to target: {}", e);
                            return;
                        }
                    }
                }
                Err(e) => {
                    error!("Failed to parse proxy request: {}", e);
                    return;
                }
            }
        }
        Some(Ok(msg)) => {
            error!("Expected binary message, received: {:?}", msg);
            return;
        }
        Some(Err(e)) => {
            error!("Error receiving WebSocket message: {}", e);
            return;
        }
        None => {
            warn!("WebSocket connection closed immediately");
            return;
        }
    };

    // Reunite WebSocket for connection handling
    let ws = ws_sender
        .reunite(ws_receiver)
        .expect("Failed to reunite WebSocket");

    if let Err(e) = handle_connection(ws, target_stream, server_config).await {
        error!("Error handling connection: {}", e);
    }
}

// Handle bidirectional data forwarding (optimized version)
async fn handle_connection(
    ws: WebSocket,
    target_stream: TcpStream,
    server_config: Arc<ServerConfig>,
) -> Result<()> {
    let (ws_sink, ws_stream) = ws.split();
    let (target_reader, target_writer) = tokio::io::split(target_stream);

    let ws_sink = Arc::new(Mutex::new(ws_sink));
    let target_writer = Arc::new(Mutex::new(target_writer));
    let target_reader = Arc::new(Mutex::new(target_reader));

    // Use configured buffer size
    let buffer_size = server_config.buffer_size;

    // WebSocket to target TCP
    let t1 = {
        let target_writer = Arc::clone(&target_writer);
        async move {
            let mut ws_stream = ws_stream;
            while let Some(msg) = ws_stream.next().await {
                let msg = match msg {
                    Ok(msg) => msg,
                    Err(e) => {
                        error!("Error receiving WebSocket message: {}", e);
                        return Err(TunnelError::Other(format!("WebSocket receive error: {}", e)));
                    }
                };

                match msg {
                    Message::Binary(data) => {
                        let mut target_writer = target_writer.lock().await;
                        if let Err(e) = target_writer.write_all(&data).await {
                            error!("Error writing to target: {}", e);
                            return Err(TunnelError::IoError(e));
                        }
                    }
                    Message::Text(data) => {
                        let mut target_writer = target_writer.lock().await;
                        if let Err(e) = target_writer.write_all(data.as_bytes()).await {
                            error!("Error writing to target: {}", e);
                            return Err(TunnelError::IoError(e));
                        }
                    }
                    Message::Close(_) => {
                        info!("WebSocket closed by client");
                        return Ok(());
                    }
                    Message::Ping(_) => debug!("Received ping"),
                    Message::Pong(_) => debug!("Received pong"),
                }
            }
            info!("WebSocket stream ended");
            Ok(())
        }
    };

    // Target TCP to WebSocket
    let t2 = {
        let ws_sink = Arc::clone(&ws_sink);
        let target_reader = Arc::clone(&target_reader);
        async move {
            let mut buffer = vec![0u8; buffer_size];
            loop {
                let n = {
                    let mut target_reader = target_reader.lock().await;
                    match target_reader.read(&mut buffer).await {
                        Ok(0) => {
                            debug!("Target TCP connection closed (EOF)");
                            return Ok(());
                        }
                        Ok(n) => n,
                        Err(e) => {
                            error!("Error reading from target: {}", e);
                            return Err(TunnelError::IoError(e));
                        }
                    }
                };

                let data = &buffer[..n];
                {
                    let mut ws_sink = ws_sink.lock().await;
                    if let Err(e) = ws_sink
                        .send(Message::Binary(Bytes::copy_from_slice(data)))
                        .await
                    {
                        error!("Error sending data to WebSocket: {}", e);
                        return Err(TunnelError::Other(format!("WebSocket send error: {}", e)));
                    }
                }
            }
        }
    };

    // Run both tasks concurrently, terminate when either ends
    select! {
        result1 = t1 => {
            debug!("WebSocket->TCP task ended: {:?}", result1);
            result1
        },
        result2 = t2 => {
            debug!("TCP->WebSocket task ended: {:?}", result2);
            result2
        },
    }
}
