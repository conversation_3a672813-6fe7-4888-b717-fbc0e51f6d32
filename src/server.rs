use crate::common::{Result, TunnelError};
use crate::hub_service_forward;
use crate::hub_service_proxy;
use crate::server_forward;
use crate::server_hub::{hub_handler, start_stats_monitoring_task, ServiceHub};
use crate::server_hub_dynamic::{self, DynamicHubState};
use crate::server_mux::{self, ClientSession};
use crate::server_proxy;
use axum::{
    extract::{Extension, Request},
    http::{header, StatusCode},
    middleware::{self, Next},
    response::{Html, IntoResponse, Json, Response},
    routing::get,
    Router,
};
use base64::{engine::general_purpose, Engine as _};
use log::{info, warn};
use serde_json::json;
use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::Arc;
use std::time::{SystemTime, UNIX_EPOCH};
use tokio::sync::RwLock;

#[derive(Debug)]
pub struct AppState {
    pub sessions: HashMap<u32, Arc<ClientSession>>,
    pub dynamic_hub_state: Arc<tokio::sync::Mutex<DynamicHubState>>,
}

impl AppState {
    fn new() -> Self {
        Self {
            sessions: HashMap::new(),
            dynamic_hub_state: Arc::new(tokio::sync::Mutex::new(DynamicHubState::new())),
        }
    }
}

#[derive(Debug, Clone)]
pub struct ServerConfig {
    pub listen_addr: String,
    pub max_connections: u32, // Maximum concurrent connections (default 1000)
    pub keep_alive: bool,     // Whether to enable HTTP Keep-Alive (default true)

    pub dns_cache_size: u32,     // DNS cache size (default 1000)
    pub dns_cache_ttl: u64,      // DNS cache TTL in seconds (default 300)
    pub connection_timeout: u64, // Connection timeout in seconds (default 10)
    pub buffer_size: usize,      // Data buffer size (default 32KB)
    pub activity_timeout: u64,   // Connection activity timeout (default 90 seconds)
    pub username: Option<String>,
    pub password: Option<String>,
}

impl Default for ServerConfig {
    fn default() -> Self {
        Self {
            listen_addr: "127.0.0.1:8060".to_string(),
            max_connections: 1000,
            keep_alive: true,
            dns_cache_size: 1000,
            dns_cache_ttl: 300,
            connection_timeout: 10,
            buffer_size: 32 * 1024, // 32KB
            activity_timeout: 90,
            username: None,
            password: None,
        }
    }
}

// Authentication middleware to check for Basic Auth header.
// It is only active if username and password are provided in the server config.
async fn auth_middleware(
    Extension(server_config): Extension<Arc<ServerConfig>>,
    request: Request,
    next: Next,
) -> Response {
    // This middleware is layered, so it will only be called if auth is enabled.
    // We can safely unwrap here because the presence of user/pass is checked before applying the middleware.
    let username = server_config.username.as_ref().unwrap();
    let password = server_config.password.as_ref().unwrap();

    let auth_header = request
        .headers()
        .get(header::AUTHORIZATION)
        .and_then(|header| header.to_str().ok());

    if let Some(auth_header) = auth_header {
        if auth_header.starts_with("Basic ") {
            let credentials = &auth_header[6..];
            if let Ok(decoded_credentials) = general_purpose::STANDARD.decode(credentials) {
                if let Ok(credentials_str) = String::from_utf8(decoded_credentials) {
                    let mut parts = credentials_str.splitn(2, ':');
                    if let (Some(user), Some(pass)) = (parts.next(), parts.next()) {
                        if user == username && pass == password {
                            info!("Authentication successful for user: {}", user);
                            return next.run(request).await;
                        } else {
                            warn!("Authentication failed: invalid credentials for user: {}", user);
                        }
                    }
                }
            }
        }
    }

    // Invalid or missing credentials, reject the request
    warn!("Authentication failed: Unauthorized connection attempt.");
    let mut response = (
        StatusCode::UNAUTHORIZED,
        "Authentication required".to_string(),
    )
        .into_response();
    response.headers_mut().insert(
        header::WWW_AUTHENTICATE,
        "Basic realm=\"wsstun\"".parse().unwrap(),
    );
    response
}

pub async fn run_server(config: ServerConfig) -> Result<()> {
    // Parse listening address
    let addr: SocketAddr = config.listen_addr.parse().map_err(|e| {
        TunnelError::ConfigError(format!(
            "Invalid listen address: {}: {}",
            config.listen_addr, e
        ))
    })?;

    // Create service hub state instance
    let service_hub = Arc::new(RwLock::new(ServiceHub::new()));
    // Create Mux session state
    let app_state = Arc::new(RwLock::new(AppState::new()));
    let dynamic_hub_state = app_state.read().await.dynamic_hub_state.clone();

    // Start statistics monitoring task
    start_stats_monitoring_task(Arc::clone(&service_hub));
    info!("Started hub statistics monitoring task");

    // Create shared server configuration
    let server_config = Arc::new(config.clone());

    info!(
        "Server configuration - max_connections: {}, keep_alive: {}, dns_cache_size: {}, buffer_size: {}",
        config.max_connections, config.keep_alive, config.dns_cache_size, config.buffer_size
    );
    
    // Check if auth is configured properly
    let auth_enabled = if let (Some(user), Some(pass)) = (&server_config.username, &server_config.password) {
        if !user.is_empty() && !pass.is_empty() {
            info!("Basic authentication is enabled for all WebSocket endpoints.");
            true
        } else {
            warn!("Authentication username or password is provided but empty, authentication is disabled.");
            false
        }
    } else if server_config.username.is_some() || server_config.password.is_some() {
        warn!("Both username and password must be provided to enable authentication. Authentication is disabled.");
        false
    } else {
        info!("Basic authentication is disabled.");
        false
    };

    // Define WebSocket routes
    let ws_routes = Router::new()
        .route("/forward", get(server_forward::ws_handler))
        .route("/proxy", get(server_proxy::ws_handler))
        .route("/mux", get(server_mux::ws_handler))
        .route("/hub", get(hub_handler))
        .route("/hub/forward", get(hub_service_forward::ws_handler))
        .route("/hub/proxy", get(hub_service_proxy::ws_handler))
        .route("/hub/dynamic", get(server_hub_dynamic::dynamic_control_handler))
        .route("/hub/data", get(server_hub_dynamic::dynamic_data_handler));

    // Conditionally apply the authentication middleware to WebSocket routes
    let app = if auth_enabled {
        let authenticated_ws_routes = ws_routes
            .route_layer(middleware::from_fn_with_state(server_config.clone(), auth_middleware));

        Router::new()
            .merge(authenticated_ws_routes)
            .route("/", get(root_handler))
            .route("/health", get(health_check))
            .route("/status", get(status_handler))
            .route("/metrics", get(metrics_handler))
            .layer(Extension(service_hub))
            .layer(Extension(server_config))
            .layer(Extension(dynamic_hub_state))
            .with_state(app_state)
    } else {
        Router::new()
            .merge(ws_routes)
            .route("/", get(root_handler))
            .route("/health", get(health_check))
            .route("/status", get(status_handler))
            .route("/metrics", get(metrics_handler))
            .layer(Extension(service_hub))
            .layer(Extension(server_config))
            .layer(Extension(dynamic_hub_state))
            .with_state(app_state)
    };
    
    // Start server
    info!("Starting server on {}", addr);
    info!("Available endpoints:");
    info!("  WebSocket: /forward, /proxy, /mux, /hub, /hub/forward, /hub/proxy, /hub/dynamic, /hub/data");
    info!("  HTTP: /, /health, /status, /metrics");

    let listener = tokio::net::TcpListener::bind(addr).await?;
    axum::serve(listener, app).await.map_err(|e| {
        TunnelError::IoError(std::io::Error::new(
            std::io::ErrorKind::Other,
            e.to_string(),
        ))
    })?;

    Ok(())
}

// New handler for the root path, serving HTML
async fn root_handler() -> impl IntoResponse {
    // Embed HTML template at compile time
    let html_template = include_str!("../resources/index.html");

    // Get version information
    let version = env!("CARGO_PKG_VERSION");
    let build_time = env!("BUILD_TIMESTAMP_CST");

    // Replace placeholders with actual values
    let html_content = html_template
        .replace("{{VERSION}}", version)
        .replace("{{BUILD_TIME}}", build_time);

    Html(html_content)
}

/// Health check endpoint
async fn health_check() -> impl IntoResponse {
    (
        StatusCode::OK,
        Json(json!({
            "status": "healthy",
            "timestamp": SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
            "service": "wsstun-server"
        })),
    )
}

/// Server status endpoint
async fn status_handler(
    Extension(service_hub): Extension<Arc<RwLock<ServiceHub>>>,
    Extension(dynamic_hub_state): Extension<Arc<tokio::sync::Mutex<DynamicHubState>>>,
    Extension(server_config): Extension<Arc<ServerConfig>>,
) -> impl IntoResponse {
    // Get services from mux mode
    let hub = service_hub.read().await;
    let mux_services = hub.list_active_services();
    
    // Get services from dynamic tunnel mode
    let dynamic_state = dynamic_hub_state.lock().await;
    let dynamic_services = dynamic_state.get_available_services();
    
    // Merge all services and deduplicate
    let mut all_services = mux_services;
    for service in dynamic_services {
        if !all_services.contains(&service) {
            all_services.push(service);
        }
    }
    
    let uptime = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_secs();

    (
        StatusCode::OK,
        Json(json!({
            "status": "running",
            "version": env!("CARGO_PKG_VERSION"),
            "build_time": env!("BUILD_TIMESTAMP_CST"),
            "uptime_seconds": uptime,
            "config": {
                "listen_addr": server_config.listen_addr,
                "max_connections": server_config.max_connections,
                "dns_cache_size": server_config.dns_cache_size,
                "buffer_size": server_config.buffer_size
            },
            "services": {
                "active_count": all_services.len(),
                "services": all_services
            },
            "endpoints": [
                "/forward",
                "/proxy",
                "/mux",
                "/hub",
                "/hub/forward",
                "/hub/proxy",
                "/hub/dynamic",
                "/hub/data"
            ]
        })),
    )
}

/// Server performance metrics endpoint
async fn metrics_handler(
    Extension(service_hub): Extension<Arc<RwLock<ServiceHub>>>,
    Extension(dynamic_hub_state): Extension<Arc<tokio::sync::Mutex<DynamicHubState>>>,
) -> impl IntoResponse {
    // Get services from mux mode
    let hub = service_hub.read().await;
    let mux_services = hub.list_active_services();
    
    // Get services from dynamic tunnel mode
    let dynamic_state = dynamic_hub_state.lock().await;
    let dynamic_services = dynamic_state.get_available_services();
    
    // Merge all services and deduplicate
    let mut all_services = mux_services.clone();
    for service in dynamic_services.clone() {
        if !all_services.contains(&service) {
            all_services.push(service);
        }
    }

    // More performance metrics can be added here
    // For example, DNS cache and connection pool statistics from ConnectionManager

    (
        StatusCode::OK,
        Json(json!({
            "metrics": {
                "active_services": all_services.len(),
                "mux_services": mux_services.len(),
                "dynamic_services": dynamic_services.len(),
                "total_connections": 0, // Not implemented yet
                "dns_cache_hits": 0,    // Not implemented yet
                "dns_cache_misses": 0,  // Not implemented yet
                "connection_pool_size": 0, // Not implemented yet
            },
            "services": all_services.iter().map(|service_id| {
                let service_type = if mux_services.contains(service_id) && dynamic_services.contains(service_id) {
                    "both"
                } else if mux_services.contains(service_id) {
                    "mux"
                } else {
                    "dynamic"
                };
                
                json!({
                    "service_id": service_id,
                    "status": "active",
                    "type": service_type
                })
            }).collect::<Vec<_>>()
        })),
    )
}
