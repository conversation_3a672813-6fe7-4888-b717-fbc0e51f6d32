use std::io::Result;

fn main() -> Result<()> {
    // Set the build timestamp
    println!(
        "cargo:rustc-env=BUILD_TIMESTAMP_CST={}",
        chrono::Local::now()
            .with_timezone(&chrono_tz::Asia::Shanghai)
            .format("%Y-%m-%d %H:%M:%S")
    );

    // Compile Protobuf files
    let protos = &[
        "proto/streamlit/proto/Alert.proto",
        "proto/streamlit/proto/AppPage.proto",
        "proto/streamlit/proto/Arrow.proto",
        "proto/streamlit/proto/ArrowNamedDataSet.proto",
        "proto/streamlit/proto/ArrowVegaLiteChart.proto",
        "proto/streamlit/proto/Audio.proto",
        "proto/streamlit/proto/AudioInput.proto",
        "proto/streamlit/proto/AuthRedirect.proto",
        "proto/streamlit/proto/AutoRerun.proto",
        "proto/streamlit/proto/BackMsg.proto",
        "proto/streamlit/proto/Balloons.proto",
        "proto/streamlit/proto/Block.proto",
        "proto/streamlit/proto/BokehChart.proto",
        "proto/streamlit/proto/Button.proto",
        "proto/streamlit/proto/ButtonGroup.proto",
        "proto/streamlit/proto/CameraInput.proto",
        "proto/streamlit/proto/ChatInput.proto",
        "proto/streamlit/proto/Checkbox.proto",
        "proto/streamlit/proto/ClientState.proto",
        "proto/streamlit/proto/Code.proto",
        "proto/streamlit/proto/ColorPicker.proto",
        "proto/streamlit/proto/Common.proto",
        "proto/streamlit/proto/Components.proto",
        "proto/streamlit/proto/DataFrame.proto",
        "proto/streamlit/proto/DateInput.proto",
        "proto/streamlit/proto/DeckGlJsonChart.proto",
        "proto/streamlit/proto/Delta.proto",
        "proto/streamlit/proto/DocString.proto",
        "proto/streamlit/proto/DownloadButton.proto",
        "proto/streamlit/proto/Element.proto",
        "proto/streamlit/proto/Empty.proto",
        "proto/streamlit/proto/Exception.proto",
        "proto/streamlit/proto/Favicon.proto",
        "proto/streamlit/proto/FileUploader.proto",
        "proto/streamlit/proto/ForwardMsg.proto",
        "proto/streamlit/proto/GapSize.proto",
        "proto/streamlit/proto/GitInfo.proto",
        "proto/streamlit/proto/GraphVizChart.proto",
        "proto/streamlit/proto/Heading.proto",
        "proto/streamlit/proto/HeightConfig.proto",
        "proto/streamlit/proto/Html.proto",
        "proto/streamlit/proto/IFrame.proto",
        "proto/streamlit/proto/Image.proto",
        "proto/streamlit/proto/Json.proto",
        "proto/streamlit/proto/LabelVisibilityMessage.proto",
        "proto/streamlit/proto/LinkButton.proto",
        "proto/streamlit/proto/Logo.proto",
        "proto/streamlit/proto/Markdown.proto",
        "proto/streamlit/proto/Metric.proto",
        "proto/streamlit/proto/MetricsEvent.proto",
        "proto/streamlit/proto/MultiSelect.proto",
        "proto/streamlit/proto/NamedDataSet.proto",
        "proto/streamlit/proto/Navigation.proto",
        "proto/streamlit/proto/NewSession.proto",
        "proto/streamlit/proto/NumberInput.proto",
        "proto/streamlit/proto/PageConfig.proto",
        "proto/streamlit/proto/PageInfo.proto",
        "proto/streamlit/proto/PageLink.proto",
        "proto/streamlit/proto/PageNotFound.proto",
        "proto/streamlit/proto/PageProfile.proto",
        "proto/streamlit/proto/PagesChanged.proto",
        "proto/streamlit/proto/ParentMessage.proto",
        "proto/streamlit/proto/PlotlyChart.proto",
        "proto/streamlit/proto/Progress.proto",
        "proto/streamlit/proto/Radio.proto",
        "proto/streamlit/proto/RootContainer.proto",
        "proto/streamlit/proto/Selectbox.proto",
        "proto/streamlit/proto/SessionEvent.proto",
        "proto/streamlit/proto/SessionStatus.proto",
        "proto/streamlit/proto/Skeleton.proto",
        "proto/streamlit/proto/Slider.proto",
        "proto/streamlit/proto/Snow.proto",
        "proto/streamlit/proto/Spinner.proto",
        "proto/streamlit/proto/Text.proto",
        "proto/streamlit/proto/TextArea.proto",
        "proto/streamlit/proto/TextInput.proto",
        "proto/streamlit/proto/TimeInput.proto",
        "proto/streamlit/proto/Toast.proto",
        "proto/streamlit/proto/VegaLiteChart.proto",
        "proto/streamlit/proto/Video.proto",
        "proto/streamlit/proto/WidgetStates.proto",
        "proto/streamlit/proto/WidthConfig.proto",
    ];
    let includes = &["proto"];

    prost_build::Config::new()
        .type_attribute("streamlit", "#[derive(serde::Serialize, serde::Deserialize)]")
        .compile_protos(protos, includes)?;

    Ok(())
}