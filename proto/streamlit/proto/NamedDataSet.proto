/**!
 * Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";

package streamlit;

option java_package = "com.snowflake.apps.streamlit";
option java_outer_classname = "NamedDataSetProto";

import "streamlit/proto/DataFrame.proto";

// DEPRECATED: This proto message is deprecated and unused.
// A dataset that can be referenced by name.
message NamedDataSet {
  // The dataset name.
  string name = 1;

  // True if the name field (above) was manually set. This is used to get
  // around proto3 not having a way to check whether something was set.
  bool has_name = 3;

  // The data itself.
  DataFrame data = 2;
}
