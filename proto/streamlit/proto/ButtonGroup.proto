/**!
 * Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";

package streamlit;

option java_package = "com.snowflake.apps.streamlit";
option java_outer_classname = "ButtonGroupProto";

import "streamlit/proto/LabelVisibilityMessage.proto";

message ButtonGroup {
  enum ClickMode {
      SINGLE_SELECT = 0;
      MULTI_SELECT = 1;
  }

  message Option {
    string content = 1;
    // when set, this is the content that will be displayed when the option is selected
    optional string selected_content = 2;
    // when set, this is the icon that will be displayed next to the option
    optional string content_icon = 3;
    // when set, this is the icon that will be displayed next to the option when then option is selected
    optional string selected_content_icon = 4;
  }

  string id = 1;
  repeated Option options = 2;
  // default is an array of indexes that are selected by default
  repeated uint32 default = 3;
  bool disabled = 4;
  ClickMode click_mode = 5;
  string form_id = 6;

  // value passed by the backend
  repeated uint32 value = 7;
  bool set_value = 8;

  enum SelectionVisualization {
    ONLY_SELECTED = 0;
    ALL_UP_TO_SELECTED = 1;
  }
  SelectionVisualization selection_visualization = 9;

  enum Style {
    SEGMENTED_CONTROL = 0;
    PILLS = 1;
    BORDERLESS = 2;
  }
  Style style = 10;

  string label = 11;
  LabelVisibilityMessage label_visibility = 12;
  optional string help = 13;

  // next id: 14
}
