/**!
 * Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";

package streamlit;

option java_package = "com.snowflake.apps.streamlit";
option java_outer_classname = "ClientStateProto";

import "streamlit/proto/WidgetStates.proto";

message ContextInfo {
  optional string timezone = 1;
  optional int32 timezone_offset = 2;
  optional string locale = 3;
  optional string url = 4;
  optional bool is_embedded = 5;
  optional string color_scheme = 6;
}

message ClientState {
  string query_string = 1;
  WidgetStates widget_states = 2;
  string page_script_hash = 3;
  string page_name = 4;
  string fragment_id = 5;
  bool is_auto_rerun = 6;
  // List of hashes of messages that are currently cached in
  // the frontend forward message cache.
  repeated string cached_message_hashes = 7;
  ContextInfo context_info = 8;
}
