/**!
 * Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";

package streamlit;

option java_package = "com.snowflake.apps.streamlit";
option java_outer_classname = "ExceptionProto";

import "streamlit/proto/WidthConfig.proto";

// A python exception.
//
// NOTE: This proto type is used by some external services so needs to remain
// relatively stable. While it isn't entirely set in stone, changing it
// may require a good amount of effort so should be avoided if possible.
message Exception {

  // The type of the exception. This can be any string, but is usually a valid
  // Python exception type, like 'RuntimeError'.
  string type = 1;

  // The exception's message.
  string message = 2;

  // If true, the exception message should be rendered as Markdown text.
  bool message_is_markdown = 4;

  // The stack trace to print.
  repeated string stack_trace = 3;

  // If true, this is an error that doesn't stop the execution flow. So it gets
  // rendered differently for clarity.
  bool is_warning = 5;

  // Indicates the width setting: "stetch", "content" or a pixel value.
  streamlit.WidthConfig width_config = 6;
}
