/**!
 * Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";

package streamlit;

option java_package = "com.snowflake.apps.streamlit";
option java_outer_classname = "FileUploaderProto";

import "streamlit/proto/LabelVisibilityMessage.proto";

// file_uploader widget
message FileUploader {
  // The widget id
  string id = 1;

  // Text to be displayed before the widget
  string label = 2;

  // Supported types: For example: ["png","jpg","img"]
  repeated string type = 3;

  // Max file size allowed by server config
  int32 max_upload_size_mb = 4;

  // If true, the widget accepts multiple files for upload.
  bool multiple_files = 6;

  string help = 7;

  string form_id = 8;

  bool disabled = 9;

  LabelVisibilityMessage label_visibility = 10;

  reserved 5;
}
