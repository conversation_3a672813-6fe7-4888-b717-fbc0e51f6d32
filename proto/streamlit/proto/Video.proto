/**!
 * Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";

package streamlit;

option java_package = "com.snowflake.apps.streamlit";
option java_outer_classname = "VideoProto";

import "streamlit/proto/WidthConfig.proto";

message SubtitleTrack {
    string label = 1;  // Label for the subtitle e.g. "English" or "Spanish".
    string url = 2;
}

message Video {
  // A url pointing to a video file
  string url = 6;

  enum Type {
    UNUSED = 0;  // This should always exist.
    NATIVE = 1;
    YOUTUBE_IFRAME = 2;
  }

  // The currentTime attribute of the HTML <video> tag's <source> subtag.
  int32 start_time = 3;

  // Type affects how browser wraps the video in tags: plain HTML5, YouTube...
  Type type = 5;

  // Repeated field for subtitle tracks
  repeated SubtitleTrack subtitles = 7;

  // The time at which the video should stop playing. If not specified, plays to the end.
  int32 end_time = 8;

  // Indicates whether the video should start over from the beginning once it ends.
  bool loop = 9;

  bool autoplay = 10;

  bool muted = 11;

  string id = 12;

  optional streamlit.WidthConfig width_config = 13;

  reserved 1,2,4;
  reserved "format", "data";
}
