/**!
 * Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";

package streamlit;

option java_package = "com.snowflake.apps.streamlit";
option java_outer_classname = "WidgetStatesProto";

import "streamlit/proto/Common.proto";
import "streamlit/proto/Components.proto";

// State for every widget in an app.
message WidgetStates {
  repeated WidgetState widgets = 1;
}

// State for a single widget.
message WidgetState {
  string id = 1;

  oneof value {
    // trigger_value is for buttons. A button's value needs to
    // auto-revert back to False after the script has been run with
    // the value set to True. After running the script, the server
    // will reset all trigger_values in its stored Widget state dict.
    // (Use bool_value for widgets like checkboxes, whose state persists
    // beyond a single script run.)
    bool trigger_value = 2;

    bool bool_value = 3;
    double double_value = 4;
    sint64 int_value = 5;
    string string_value = 6;
    DoubleArray double_array_value = 7;
    SInt64Array int_array_value = 8;
    StringArray string_array_value = 9;
    string json_value = 10;
    ArrowTable arrow_value = 11;
    bytes bytes_value = 12;
    FileUploaderState file_uploader_state_value = 13;

    // DEPRECATED: StringTriggerValue proto message is deprecated and unused.
    // The ChatInputValue proto message should be used instead.
    StringTriggerValue string_trigger_value = 14 [deprecated = true];
    // ChatInputValue resets itself to empty after the script has been run.
    // This is used for the chat_input widget.
    ChatInputValue chat_input_value = 15;
  }
}
