/**!
 * Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";

package streamlit;

option java_package = "com.snowflake.apps.streamlit";
option java_outer_classname = "ChatInputProto";

message ChatInput {
  string id = 1;
  string placeholder = 2;
  uint32 max_chars = 3;
  bool disabled = 4;
  string value = 5;
  bool set_value = 6;
  string default = 7;

  // DEPRECATED: position property is deprecated and unused.
  enum Position {
    BOTTOM = 0;
  }
  Position position = 8;

  enum AcceptFile {
    NONE = 0;
    SINGLE = 1;
    MULTIPLE = 2;
  }
  AcceptFile accept_file = 9;

  // Supported file types: For example: ["png","jpg","img"]
  repeated string file_type = 10;

  // Max file size allowed by server config
  int32 max_upload_size_mb = 11;
}
