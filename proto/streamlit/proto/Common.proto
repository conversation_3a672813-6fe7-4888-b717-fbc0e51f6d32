/**!
 * Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";

package streamlit;

option java_package = "com.snowflake.apps.streamlit";
option java_outer_classname = "CommonProto";

// Message types that are common to multiple protobufs.

message StringArray {
  repeated string data = 1;
}

message DoubleArray {
  repeated double data = 1;
}

message Int32Array {
  repeated int32 data = 1;
}

message Int64Array {
  repeated int64 data = 1;
}

message SInt64Array {
  repeated sint64 data = 1;
}

message UInt32Array {
  repeated uint32 data = 1;
}


// DEPRECATED: This proto message is deprecated and unused. The ChatInputValue
// proto message should be used instead.
message StringTriggerValue {
  optional string data = 1;
}

// NOTE: The FileURLsRequest, FileURLs, and FileURLsResponse message types
// must remain stable as some external services rely on them to support
// `st.file_uploader`. These types aren't completely set in stone, but changing
// them requires a good amount of effort so should be avoided if possible.
message FileURLsRequest {
  string request_id = 1;
  repeated string file_names = 2;
  string session_id = 3;
}

message FileURLs {
  string file_id = 1;
  string upload_url = 2;
  string delete_url = 3;
}

message FileURLsResponse {
  string response_id = 1;
  repeated FileURLs file_urls = 2;
  string error_msg = 3;
}

// Information on a file uploaded via the file_uploader widget.
message UploadedFileInfo {
  // DEPRECATED.
  sint64 id = 1;

  string name = 2;

  // The size of this file in bytes.
  uint64 size = 3;

  // ID that can be used to retrieve a file.
  string file_id = 4;

  // Metadata containing information about file_urls.
  FileURLs file_urls = 5;
}

message FileUploaderState {
  // DEPRECATED
  sint64 max_file_id = 1;

  repeated UploadedFileInfo uploaded_file_info = 2;
}


message ChatInputValue {
  optional string data = 1;
  optional FileUploaderState file_uploader_state = 2;

}
