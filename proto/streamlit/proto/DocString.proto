/**!
 * Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";

package streamlit;

option java_package = "com.snowflake.apps.streamlit";
option java_outer_classname = "DocStringProto";

// Formatted text
message DocString {

  // [DEPRECATED] The name of the object.
  //string name = 1;
  reserved 1;

  // [DEPRECATED] The module of the object
  //string module = 2;
  reserved 2;

  // The doc string.
  string doc_string = 3;

  // The type of the object.
  string type = 4;

  // [DEPRECATED] The signature of the object, if it's a function. Else, unset.
  //string signature = 5;
  reserved 5;

  // The name the user gave to the variable holding this object.
  string name = 6;

  // A string representation of this object's value.
  string value = 7;

  // List of this object's methods and member variables.
  repeated Member members = 8;
}


message Member {
  // The name of the object.
  string name = 1;

  // The type of the object.
  string type = 2;

  oneof contents {
    // A string representation of this member's value.
    string value = 3;

    // The doc string.
    string doc_string = 4;
  }
}
