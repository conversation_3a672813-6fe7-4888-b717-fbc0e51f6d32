/**!
 * Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";

package streamlit;

option java_package = "com.snowflake.apps.streamlit";
option java_outer_classname = "PlotlyChartProto";

message Plotly<PERSON>hart {
  // If True, will overwrite the chart width spec to fit to container.
  bool use_container_width = 5;

  // override the properties with a theme. Currently, only "streamlit" or None are accepted.
  string theme = 6;

  // The unique element ID of this chart.
  string id = 7;

  // Activate selections types on the chart.
  repeated SelectionMode selection_mode = 8;

  // Form ID, filled if selections are activated.
  string form_id = 9;

  // JSON-serialized dict containing keys from the set {data, frames, layout}.
  string spec = 10;

  // JSON-serialized dict with Plotly's config object.
  string config = 11;

  reserved 3, 4;

  // Available selection modes:
  enum SelectionMode {
      POINTS = 0; // Point selection mode
      BOX = 1; // Box selection mode
      LASSO = 2; // Lasso selection mode
  }

  // DEPRECATED and unused.
  oneof chart {
    // DEPRECATED and unused.
    string url = 1;

    // DEPRECATED and unused.
    Figure figure = 2;
  }

}


// DEPRECATED and unused.
message Figure {
  string spec = 1;
  string config = 2;
}
