/**!
 * Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";

package streamlit;

option java_package = "com.snowflake.apps.streamlit";
option java_outer_classname = "GitInfoProto";

// Message used to update page metadata.
message GitInfo {
  string repository = 1;
  string branch = 2;
  string module = 3;
  repeated string untracked_files = 4;
  repeated string uncommitted_files = 5;

  enum GitStates {
    DEFAULT = 0;
    HEAD_DETACHED = 1;
    AHEAD_OF_REMOTE = 2;
  }

  GitStates state = 6;
}
