/**!
 * Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";

package streamlit;

option java_package = "com.snowflake.apps.streamlit";
option java_outer_classname = "AlertProto";

import "streamlit/proto/WidthConfig.proto";

// NOTE: This proto type is used by some external services so needs to remain
// relatively stable. While it isn't entirely set in stone, changing it
// may require a good amount of effort so should be avoided if possible.
message Alert {
  // Content to display.
  string body = 1;
  Format format = 2;
  string icon = 3;

  // Indicates the width setting: "stetch", "content" or a pixel value.
  streamlit.WidthConfig width_config = 4;

  // Type of Alert
  enum Format {
    // Plain, fixed width text.
    UNUSED = 0;

    // Shows an error message.
    ERROR = 1;

    // Shows a warning message.
    WARNING = 2;

    // Shows an info log.
    INFO = 3;

    // Shows a success message.
    SUCCESS = 4;
  }
}
