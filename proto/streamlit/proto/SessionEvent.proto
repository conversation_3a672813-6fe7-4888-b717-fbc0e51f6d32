/**!
 * Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";

package streamlit;

option java_package = "com.snowflake.apps.streamlit";
option java_outer_classname = "SessionEventProto";

import "streamlit/proto/Exception.proto";

// A transient event sent to all browsers connected to an associated app.
message SessionEvent {
  oneof type {
    // The app's script changed on disk, but is *not* being re-run
    // automatically. The browser should prompt the user to re-run.
    bool script_changed_on_disk = 1;

    // The app's script was running, but it was manually stopped before
    // completion.
    bool script_was_manually_stopped = 2;

    // Script compilation failed with an exception.
    // We can't start running the script.
    Exception script_compilation_exception = 3;
  }
}
