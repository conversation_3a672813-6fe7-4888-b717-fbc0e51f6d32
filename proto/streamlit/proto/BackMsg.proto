/**!
 * Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";

package streamlit;

option java_package = "com.snowflake.apps.streamlit";
option java_outer_classname = "BackMsgProto";

import "streamlit/proto/ClientState.proto";
import "streamlit/proto/Common.proto";

// A message from the browser to the server.
message BackMsg {
  oneof type {
    // DEPRECATED. Set to true to ask the server to display the inline help.
    // bool help = 1;

    // DEPRECATED. Asks the server to run the script.
    //bool rerun_script = 3;

    // DEPRECATED. Asks the server to run the script with this object
    // ReRun rerun = 4;

    // Requests that the app's @st_cache be cleared
    bool clear_cache = 5;

    // Requests that the runOnSave behavior for this app be set
    // to the given value
    bool set_run_on_save = 6;

    // Requests that the script's execution be stopped
    bool stop_script = 7;

    // DEPRECATED.
    // string widget_json = 8;

    // DEPRECATED. This is now inside ClientState.
    // WidgetStates update_widgets = 9;

    // DEPRECATED. Set to true to ask the server to close the connection
    // bool close_connection = 10;

    ClientState rerun_script = 11;

    bool load_git_info = 12;

    // Test and dev-mode only field used to ask the server to disconnect the
    // client's websocket connection. This message is IGNORED unless the
    // runtime is configured with global.developmentMode = True.
    bool debug_disconnect_websocket = 14;

    // Test and dev-mode only field used to ask the server to shut down the
    // runtime. This message is IGNORED unless the runtime is configured with
    // global.developmentMode = True.
    bool debug_shutdown_runtime = 15;

    // Requests that the server generate URLs for getting/uploading/deleting
    // files for the `st.file_uploader` widget
    FileURLsRequest file_urls_request = 16;

    // Sends an app heartbeat message through the websocket
    bool app_heartbeat = 17;
  }

  // An ID used to associate this BackMsg with the corresponding ForwardMsgs
  // that are sent to the client due to it. As its name suggests, this field
  // should only be used for testing.
  string debug_last_backmsg_id = 13;

  reserved 1, 2, 3, 4, 8, 9, 10;

  // Next: 18
}
