/**!
 * Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";

package streamlit;

option java_package = "com.snowflake.apps.streamlit";
option java_outer_classname = "GraphVizChartProto";

message GraphVizChart {
  // A specification of the GraphViz graph in the "Dot" language.
  string spec = 1;

  // If True, will overwrite the chart width spec to fit to container.
  bool use_container_width = 4;

  // A unique ID of this element.
  string element_id = 5;

  // The engine used to layout and render the graph.
  string engine = 6;

  reserved 2, 3;
}
