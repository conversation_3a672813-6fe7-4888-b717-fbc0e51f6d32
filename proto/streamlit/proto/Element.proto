/**!
 * Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";

package streamlit;

option java_package = "com.snowflake.apps.streamlit";
option java_outer_classname = "ElementProto";

import "streamlit/proto/Alert.proto";
import "streamlit/proto/Arrow.proto";
import "streamlit/proto/Audio.proto";
import "streamlit/proto/AudioInput.proto";
import "streamlit/proto/Balloons.proto";
import "streamlit/proto/ArrowVegaLiteChart.proto";
import "streamlit/proto/BokehChart.proto";
import "streamlit/proto/Button.proto";
import "streamlit/proto/ButtonGroup.proto";
import "streamlit/proto/DownloadButton.proto";
import "streamlit/proto/CameraInput.proto";
import "streamlit/proto/ChatInput.proto";
import "streamlit/proto/Checkbox.proto";
import "streamlit/proto/Code.proto";
import "streamlit/proto/ColorPicker.proto";
import "streamlit/proto/DataFrame.proto";
import "streamlit/proto/DateInput.proto";
import "streamlit/proto/DeckGlJsonChart.proto";
import "streamlit/proto/DocString.proto";
import "streamlit/proto/Empty.proto";
import "streamlit/proto/Exception.proto";
import "streamlit/proto/Favicon.proto";
import "streamlit/proto/FileUploader.proto";
import "streamlit/proto/GraphVizChart.proto";
import "streamlit/proto/Html.proto";
import "streamlit/proto/IFrame.proto";
import "streamlit/proto/Image.proto";
import "streamlit/proto/Json.proto";
import "streamlit/proto/LinkButton.proto";
import "streamlit/proto/NumberInput.proto";
import "streamlit/proto/Markdown.proto";
import "streamlit/proto/Metric.proto";
import "streamlit/proto/MultiSelect.proto";
import "streamlit/proto/PageLink.proto";
import "streamlit/proto/PlotlyChart.proto";
import "streamlit/proto/Components.proto";
import "streamlit/proto/Progress.proto";
import "streamlit/proto/Snow.proto";
import "streamlit/proto/Spinner.proto";
import "streamlit/proto/Radio.proto";
import "streamlit/proto/Selectbox.proto";
import "streamlit/proto/Skeleton.proto";
import "streamlit/proto/Slider.proto";
import "streamlit/proto/Text.proto";
import "streamlit/proto/TextArea.proto";
import "streamlit/proto/TextInput.proto";
import "streamlit/proto/TimeInput.proto";
import "streamlit/proto/Toast.proto";
import "streamlit/proto/VegaLiteChart.proto";
import "streamlit/proto/Video.proto";
import "streamlit/proto/Heading.proto";
import "streamlit/proto/HeightConfig.proto";
import "streamlit/proto/WidthConfig.proto";

// An element which can be displayed on the screen.
message Element {

  // Layout configuration for elements
  optional streamlit.HeightConfig height_config = 57;
  optional streamlit.WidthConfig width_config = 58;

  // An element can be one of the following element types.
  oneof type {
    Alert alert = 30;
    Arrow arrow_data_frame = 40;
    Arrow arrow_table = 39;
    ArrowVegaLiteChart arrow_vega_lite_chart = 41;
    Audio audio = 13;
    AudioInput audio_input = 56;
    Balloons balloons = 12;
    BokehChart bokeh_chart = 17;
    Button button = 19;
    ButtonGroup button_group = 55;
    DownloadButton download_button = 43;
    CameraInput camera_input = 45;
    ChatInput chat_input = 49;
    Checkbox checkbox = 20;
    ColorPicker color_picker = 35;
    ComponentInstance component_instance = 37;
    // DEPRECATED: This element is deprecated and unused:
    DataFrame data_frame = 3;
    // DEPRECATED: This element is deprecated and unused:
    DataFrame table = 11;
    DateInput date_input = 27;
    DeckGlJsonChart deck_gl_json_chart = 34;
    DocString doc_string = 7;
    Empty empty = 2;
    Exception exception = 8;
    Favicon favicon = 36;
    FileUploader file_uploader = 33;
    GraphVizChart graphviz_chart = 18;
    Html html = 54;
    IFrame iframe = 38;
    ImageList imgs = 6;
    Json json = 31;
    LinkButton link_button = 51;
    Markdown markdown = 29;
    Metric metric = 42;
    MultiSelect multiselect = 28;
    NumberInput number_input = 32;
    PageLink page_link = 53;
    PlotlyChart plotly_chart = 16;
    Progress progress = 5;
    Radio radio = 23;
    Selectbox selectbox = 25;
    Skeleton skeleton = 52;
    Slider slider = 21;
    Snow snow = 46;
    Spinner spinner = 44;
    Text text = 1;
    TextArea text_area = 22;
    TextInput text_input = 24;
    TimeInput time_input = 26;
    Toast toast = 50;
    // DEPRECATED: This element is deprecated and unused:
    VegaLiteChart vega_lite_chart = 10;
    Video video = 14;
    Heading heading = 47;
    Code code = 48;
    // Next ID: 59
  }

  reserved 9;
}
