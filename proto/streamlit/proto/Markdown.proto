/**!
 * Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";

package streamlit;

option java_package = "com.snowflake.apps.streamlit";
option java_outer_classname = "MarkdownProto";

// Formatted text
message Markdown {
  // Content to display.
  string body = 1;

  bool allow_html = 2;
  bool is_caption = 3; // TODO [Karen]: Remove this field if favor of element_type

  enum Type {
    UNSPECIFIED = 0;  // This is recommended to be reserved for proto files backwards compatibility reasons.
    NATIVE = 1;
    CAPTION = 2;
    CODE = 3;
    LATEX = 4;
    DIVIDER = 5;
  }
  Type element_type = 4;

  string help = 5;
}
