/**!
 * Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";

package streamlit;

option java_package = "com.snowflake.apps.streamlit";
option java_outer_classname = "SliderProto";

import "streamlit/proto/LabelVisibilityMessage.proto";

message Slider {
  enum DataType {
    // What numeric type are we working with? This doesn't change how the
    // data is stored, but the frontend needs to know for input parsing.
    INT = 0;
    FLOAT = 1;
    // Note: Represented as microseconds since epoch
    DATETIME = 2;
    DATE = 3;
    TIME = 4;
  }

  enum Type {
    UNSPECIFIED = 0;
    SLIDER = 1;
    SELECT_SLIDER = 2;
  }

  string id = 1;
  string form_id = 2;
  string label = 3;
  string format = 4;
  DataType data_type = 5;

  repeated double default = 6;
  double min = 7;
  double max = 8;
  double step = 9;
  repeated double value = 10;
  bool set_value = 11;

  repeated string options = 13;
  string help = 14;
  bool disabled = 15;
  LabelVisibilityMessage label_visibility = 16;
  Type type = 17;
}
