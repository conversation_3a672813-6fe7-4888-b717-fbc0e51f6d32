/**!
 * Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";

package streamlit;

option java_package = "com.snowflake.apps.streamlit";
option java_outer_classname = "DeltaProto";

import "streamlit/proto/Block.proto";
import "streamlit/proto/Element.proto";
import "streamlit/proto/NamedDataSet.proto";
import "streamlit/proto/ArrowNamedDataSet.proto";

// A change to an element.
message Delta {
  oneof type {
    // Append a new element to the frontend.
    Element new_element = 3;

    // Append a new block to the frontend.
    Block add_block = 6;

    // Append data to a DataFrame in for current element. The element to add to
    // is identified by the ID field, above. The dataframe is identified either
    // by NamedDataSet.name or by setting NamedDataSet.has_name to false.
    // All elements that contain a DataFrame should support add_rows.
    NamedDataSet add_rows = 5;
    ArrowNamedDataSet arrow_add_rows = 7;
  }

  string fragment_id = 8;
}
