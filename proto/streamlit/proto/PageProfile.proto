/**!
 * Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";

package streamlit;

option java_package = "com.snowflake.apps.streamlit";
option java_outer_classname = "PageProfileProto";


message PageProfile {
  repeated Command commands = 1;
  int64 exec_time = 2;
  int64 prep_time = 3;
  repeated string config = 5;
  string uncaught_exception = 6;
  repeated string attributions = 7;
  string os = 8;
  string timezone = 9;
  bool headless = 10;
  bool is_fragment_run = 11;
}

// The field names are used as part of the event json sent
// to our metrics provider. we are using short names to
// optimize for the size.
message Argument {
  // The keyword of the argument:
  string k = 1;
  // The type of the argument:
  string t = 2;
  // Some metadata about the argument value:
  string m = 3;
  // Contains the position (if positional argument):
  int32 p = 5;
}

message Command {
  string name = 1;
  repeated Argument args = 2;
  int64 time = 4;
}
