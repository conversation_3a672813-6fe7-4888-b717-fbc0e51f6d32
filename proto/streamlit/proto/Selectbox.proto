/**!
 * Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";

package streamlit;

option java_package = "com.snowflake.apps.streamlit";
option java_outer_classname = "SelectboxProto";

import "streamlit/proto/LabelVisibilityMessage.proto";

message Selectbox {
  string id = 1;
  string label = 2;
  optional int32 default = 3;
  repeated string options = 4;
  string help = 5;
  string form_id = 6;

  optional int32 value = 7 [deprecated = true];
  // after we introduced accept_new_options, we send the option as a string
  // instead of an index to keep it simple.
  optional string raw_value = 13;

  bool set_value = 8;
  bool disabled = 9;
  LabelVisibilityMessage label_visibility = 10;
  string placeholder = 11;
  optional bool accept_new_options = 12;

  // Next: 15
}
