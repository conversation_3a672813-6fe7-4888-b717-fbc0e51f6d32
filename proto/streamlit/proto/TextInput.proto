/**!
 * Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";

package streamlit;

option java_package = "com.snowflake.apps.streamlit";
option java_outer_classname = "TextInputProto";

import "streamlit/proto/LabelVisibilityMessage.proto";

message TextInput {
  enum Type {
    // A normal text input.
    DEFAULT = 0;

    // A password text input. Typed values are obscured by default.
    PASSWORD = 1;
  }

  string id = 1;
  string label = 2;
  optional string default = 3;
  Type type = 4;
  uint32 max_chars = 5;
  string help = 6;
  string form_id = 7;
  optional string value = 8;
  bool set_value = 9;
  string autocomplete = 10;
  string placeholder = 11;
  bool disabled = 12;
  LabelVisibilityMessage label_visibility = 13;
  string icon = 14;
}
