/**!
 * Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";

package streamlit;

option java_package = "com.snowflake.apps.streamlit";
option java_outer_classname = "AppPageProto";

// A page in the app. Includes both the name of the page as well as the full
// path to the corresponding script file.
//
// NOTE: This proto type is used by some external services so needs to remain
// relatively stable. While it isn't entirely set in stone, changing it
// may require a good amount of effort so should be avoided if possible.
message AppPage {
  string page_script_hash = 1;
  string page_name = 2;
  string icon = 3;

  // A feature for MPA v2 to inform the frontend what's the default page
  bool is_default = 4;
  string section_header = 5;
  string url_pathname = 6;
}
