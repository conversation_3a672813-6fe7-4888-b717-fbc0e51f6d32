/**!
 * Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";

package streamlit;

option java_package = "com.snowflake.apps.streamlit";
option java_outer_classname = "ArrowProto";

message Arrow {
  // The serialized arrow dataframe
  bytes data = 1;
  // Pandas styler information
  Styler styler = 2;
  // Width in pixels
  uint32 width = 3;
  // Height in pixels
  uint32 height = 4;
  // If True, will overwrite the dataframe width to fit to container.
  bool use_container_width = 5;
  // The id of the widget, this is required if the dataframe is editable
  string id = 6;
  // Column configuration as JSON
  string columns = 7;
  // Activate table editing
  EditingMode editing_mode = 8;
  // Deactivates editing
  bool disabled = 9;
  // The form ID of the widget, this is required if the dataframe is editable
  string form_id = 10;
  // Defines the order in which columns are displayed
  repeated string column_order = 11;
  // Activated dataframe selections events
  repeated SelectionMode selection_mode = 12;
  // Row height in pixels
  optional uint32 row_height = 13;

  // Available editing modes:
  enum EditingMode {
    READ_ONLY = 0; // Read-only table.
    FIXED = 1; // Activates editing but only allow editing of existing cells.
    DYNAMIC = 2; // Activates editing and allow adding & deleting rows.
  }

  // Available editing modes:
  enum SelectionMode {
    SINGLE_ROW = 0; // Only one row can be selected at a time.
    MULTI_ROW = 1; // Multiple rows can be selected at a time.
    SINGLE_COLUMN = 2; // Only one column can be selected at a time.
    MULTI_COLUMN = 3; // Multiple columns can be selected at a time.
  }
}

message Styler {
  // The Styler's source UUID (if the user provided one), or the path-based
  // hash that we generate (if no source UUID was provided).
  string uuid = 1;

  // The table's caption.
  string caption = 2;

  // `styles` contains the CSS for the entire source table.
  string styles = 3;

  // display_values is another ArrowTable: a copy of the source table, but
  // with all the display values formatted to the user-specified rules.
  bytes display_values = 4;
}
