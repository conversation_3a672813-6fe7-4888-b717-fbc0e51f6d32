/**!
 * Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";

package streamlit;

option java_package = "com.snowflake.apps.streamlit";
option java_outer_classname = "ImageProto";

// An image which can be displayed on the screen.
message Image {
  string url = 3;
  string caption = 2;

  // DEPRECATED: markup is not used anymore.
  // SVGs are added as data uris in the url field.
  string markup = 4;

  reserved 1;
  reserved "data";
}

// A set of images.
message ImageList {
  repeated Image imgs = 1;

  // @see WidthBehavior on the backend
  // @see WidthBehavior on the frontend
  // The width of each image.
  // >0 sets the image width explicitly
  // -1 means use the image width
  // -2 means use the column width (deprecated)
  // -3 means use the smaller of image width & column width (deprecated)
  // -4 means use the smaller of image width & container width
  // -5 means use the larger of image width & container width
  int32 width = 2;
}
