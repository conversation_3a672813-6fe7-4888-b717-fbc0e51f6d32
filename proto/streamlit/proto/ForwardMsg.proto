/**!
 * Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";

package streamlit;

option java_package = "com.snowflake.apps.streamlit";
option java_outer_classname = "ForwardMsgProto";

import "streamlit/proto/AutoRerun.proto";
import "streamlit/proto/Common.proto";
import "streamlit/proto/Delta.proto";
import "streamlit/proto/GitInfo.proto";
import "streamlit/proto/Logo.proto";
import "streamlit/proto/Navigation.proto";
import "streamlit/proto/NewSession.proto";
import "streamlit/proto/PageConfig.proto";
import "streamlit/proto/PageInfo.proto";
import "streamlit/proto/PageProfile.proto";
import "streamlit/proto/PageNotFound.proto";
import "streamlit/proto/PagesChanged.proto";
import "streamlit/proto/ParentMessage.proto";
import "streamlit/proto/SessionEvent.proto";
import "streamlit/proto/SessionStatus.proto";
import "streamlit/proto/AuthRedirect.proto";

// A message sent from Proxy to the browser
message ForwardMsg {
  // A hash that uniquely identifies this ForwardMsg, for caching.
  string hash = 1;

  // Contains 'non-payload' ForwardMsg data that isn't cached for the purposes
  // of ForwardMsg de-duping.
  ForwardMsgMetadata metadata = 2;

  // Values for the 'script_finished` type
  enum ScriptFinishedStatus {
    // The script compiled and ran.
    FINISHED_SUCCESSFULLY = 0;

    // The script failed to compile
    FINISHED_WITH_COMPILE_ERROR = 1;

    // Script was interrupted by rerun
    FINISHED_EARLY_FOR_RERUN = 2;

    // A fragment of the script ran successfully.
    FINISHED_FRAGMENT_RUN_SUCCESSFULLY = 3;
  }

  oneof type {
    // App lifecycle messages.
    NewSession new_session = 4;
    Delta delta = 5;
    PageInfo page_info_changed = 12;
    PageConfig page_config_changed = 13;
    ScriptFinishedStatus script_finished = 6;
    GitInfo git_info_changed = 14;
    PageProfile page_profile = 18;

    // Status change and event messages.
    SessionStatus session_status_changed = 9;
    SessionEvent session_event = 10;

    // Other messages.
    Navigation navigation = 23;
    PageNotFound page_not_found = 15;
    PagesChanged pages_changed = 16 [deprecated = true];
    FileURLsResponse file_urls_response = 19;
    AutoRerun auto_rerun = 21;

    // App logo message
    Logo logo = 22;

    // Auth redirect message
    AuthRedirect auth_redirect = 24;

    // Platform - message to host
    ParentMessage parent_message = 20;

    // A reference to a ForwardMsg that has already been delivered
    // and cached in the frontend. The client should substitute the message
    // with the given hash for this one.
    string ref_hash = 11;
  }

  // The ID of the last BackMsg that we received before sending this
  // ForwardMsg. As its name suggests, this field should only be used for
  // testing.
  string debug_last_backmsg_id = 17;

  reserved 7, 8;
  // Next: 25
}

// ForwardMsgMetadata contains all data that does _not_ get hashed (or cached)
// in our ForwardMsgCache. (That is, when we cache a ForwardMsg, we clear its
// metadata field first.) This allows us to, e.g., have a large unchanging
// dataframe appear in different places across multiple reruns - or even appear
// multiple times in a single run - and only send its dataframe bytes once.
message ForwardMsgMetadata {
  // Marks a message as cacheable for the frontend.
  bool cacheable = 1;

  // The path that identifies a delta's location in the report tree.
  // Only set for Delta messages.
  repeated uint32 delta_path = 2;

  // DEPRECATED: This is not used anymore.
  ElementDimensionSpec element_dimension_spec = 3;

  // active_script_hash the forward message is associated from.
  // For multipage apps v1, this will always be the page file running
  // For multipage apps v2, this can be the main script or the page script
  string active_script_hash = 4;
}

// DEPRECATED: This is not used anymore.
// Specifies the dimensions for the element
message ElementDimensionSpec {
  // width in pixels
  uint32 width = 1;

  // height in pixels
  uint32 height = 2;
}

// This is a list of ForwardMessages used to have a single protobuf message
// that encapsulates multiple ForwardMessages. This is used in static streamlit app
// generation in replaying all of the protobuf messages of a streamlit app. The
// ForwardMsgList allows us to leverage the built-ins of protobuf in delimiting the ForwardMsgs
// instead of needing to do that ourselves.
message ForwardMsgList {
  repeated ForwardMsg messages = 1;
}
