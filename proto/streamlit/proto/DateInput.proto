/**!
 * Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";

package streamlit;

option java_package = "com.snowflake.apps.streamlit";
option java_outer_classname = "DateInputProto";

import "streamlit/proto/LabelVisibilityMessage.proto";

message DateInput {
  string id = 1;
  string label = 2;
  repeated string default = 3;
  string min = 4;
  string max = 5;
  bool is_range = 6;
  string help = 7;
  string form_id = 8;
  repeated string value = 9;
  bool set_value = 10;
  bool disabled = 11;
  LabelVisibilityMessage label_visibility = 12;
  string format = 13;
}
