/**!
 * Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";

package streamlit;

option java_package = "com.snowflake.apps.streamlit";
option java_outer_classname = "BlockProto";

import "streamlit/proto/WidthConfig.proto";
import "streamlit/proto/HeightConfig.proto";
import "streamlit/proto/GapSize.proto";

message Block {
  oneof type {
    Vertical vertical = 1;
    Horizontal horizontal = 2;
    Column column = 3;
    Expandable expandable = 4;
    Form form = 5;
    TabContainer tab_container = 6;
    Tab tab = 7;
    ChatMessage chat_message = 9;
    Popover popover = 10;
    Dialog dialog = 11;
    FlexContainer flex_container = 13;
  }

  bool allow_empty = 8;
  optional string id = 12;
  optional streamlit.HeightConfig height_config = 14;
  optional streamlit.WidthConfig width_config = 15;

  message Vertical {
    bool border = 1;
    // Height of the container, activates scrolling
    uint32 height = 2;
  }

  message Horizontal {
    string gap = 1;
  }

  message FlexContainer {
    bool border = 1;

    streamlit.GapConfig gap_config = 2;
    float scale = 3;

    enum Direction {
      DIRECTION_UNDEFINED = 0;
      VERTICAL = 1;
      HORIZONTAL = 2;
    }
    Direction direction = 4;
    bool wrap = 5;
  }

  message Column {
    double weight = 1;

    // Deprecated. Use gap_size
    string gap = 2 [deprecated = true];

    enum VerticalAlignment {
      TOP = 0;
      CENTER = 1;
      BOTTOM = 2;
    }
    VerticalAlignment vertical_alignment = 3;
    bool show_border = 4;

    optional streamlit.GapConfig gap_config = 5;
  }

  message Expandable {
    string label = 1;
    optional bool expanded = 2;
    string icon = 3;
  }

  message Dialog {
    enum DialogWidth {
      SMALL = 0;
      LARGE = 1;
    }

    string title = 1;
    bool dismissible = 2;
    DialogWidth width = 3;
    optional bool is_open = 4;
  }

  message Form {
    string form_id = 1;
    bool clear_on_submit = 2;
    bool border = 3;
    bool enter_to_submit = 4;
  }

  message TabContainer {
  }

  message Tab {
    string label = 1;
  }

  message Popover {
    string label = 1;
    bool use_container_width = 2;
    string help = 3;
    bool disabled = 4;
    string icon = 5;
  }


  message ChatMessage {
    enum AvatarType {
      IMAGE = 0;
      EMOJI = 1;
      ICON = 2;
    }

    string name = 1;
    string avatar = 2;
    AvatarType avatar_type = 3;
  }

  // Next ID: 14
}
