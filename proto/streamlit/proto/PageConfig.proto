/**!
 * Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";

package streamlit;

option java_package = "com.snowflake.apps.streamlit";
option java_outer_classname = "PageConfigProto";

// Properties of the page that the app creator may configure.
message PageConfig {
  string title = 1;
  string favicon = 2;
  Layout layout = 3;
  SidebarState initial_sidebar_state = 4;
  MenuItems menu_items = 5;

  message MenuItems {
    string get_help_url = 1;
    bool hide_get_help = 2;
    string report_a_bug_url = 3;
    bool hide_report_a_bug = 4;
    string about_section_md = 5;
    // For multiple calls to set_page_config, clears set about markdown
    bool clear_about_md = 6;
  }

  enum Layout {
    // Render page elements in a centered block, roughly 730px wide.
    CENTERED = 0;

    // Use the full browser width to render elements.
    WIDE = 1;
  }

  enum SidebarState {
    // Expand the sidebar if page width exceeds ~1000px; otherwise, collapse it
    AUTO = 0;

    // Force the sidebar to be shown.
    EXPANDED = 1;

    // Force the sidebar to be hidden.
    COLLAPSED = 2;
  }
}
