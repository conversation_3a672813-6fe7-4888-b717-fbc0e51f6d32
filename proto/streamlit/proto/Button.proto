/**!
 * Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";

package streamlit;

option java_package = "com.snowflake.apps.streamlit";
option java_outer_classname = "ButtonProto";

message Button {
  string id = 1;
  string label = 2;
  bool default = 3;
  string help = 4;
  string form_id = 5;

  // If true, this is a form submission button. The frontend will defer
  // sending updates for all widgets inside the button's form until
  // the button is pressed.
  bool is_form_submitter = 6;
  string type = 7;
  bool disabled = 8;
  bool use_container_width = 9;
  string icon = 10;
}
