/**!
 * Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";

package streamlit;

option java_package = "com.snowflake.apps.streamlit";
option java_outer_classname = "AudioProto";

import "streamlit/proto/WidthConfig.proto";

message Audio {
  string url = 5;

  // The currentTime attribute of the HTML <audio> tag's <source> subtag.
  int32 start_time = 3;

  // The time at which the audio should stop playing. If not specified, plays to the end.
  int32 end_time = 6;

  // Indicates whether the audio should start over from the beginning once it ends.
  bool loop = 7;

  bool autoplay = 8;

  string id = 9;

  optional streamlit.WidthConfig width_config = 10;

  reserved 1, 2, 4;
  reserved "data", "format";
}
