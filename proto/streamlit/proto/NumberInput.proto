/**!
 * Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";

package streamlit;

option java_package = "com.snowflake.apps.streamlit";
option java_outer_classname = "NumberInputProto";

import "streamlit/proto/LabelVisibilityMessage.proto";

message NumberInput {
  enum DataType {
    // Does the input operate on ints or floats? This doesn't change how the
    // data is stored, but the frontend needs to know for input parsing.
    INT = 0;
    FLOAT = 1;
  }

  string id = 1;
  string label = 2;
  string form_id = 3;

  string format = 8;
  bool has_min = 11;
  bool has_max = 12;

  DataType data_type = 13;
  optional double default = 14;
  double step = 15;
  double min = 16;
  double max = 17;
  string help = 18;
  optional double value = 19;
  bool set_value = 20;
  bool disabled = 21;
  LabelVisibilityMessage label_visibility = 22;
  string placeholder = 23;
  string icon = 24;

  reserved 4, 5, 6, 7, 9, 10;
}
