# WSSTun 数据流和交互时序文档

本文档详细说明WSSTun四种工作模式的数据流向和交互时序，包括可视化的流程图和时序图。

## 目录
- [1. 直接端口转发 (Direct Forward)](#1-直接端口转发-direct-forward)
- [2. 直接代理 (Direct Proxy)](#2-直接代理-direct-proxy)  
- [3. Hub端口转发 (Hub Forward)](#3-hub端口转发-hub-forward)
- [4. Hub代理 (Hub Proxy)](#4-hub代理-hub-proxy)
- [5. 模式对比总结](#5-模式对比总结)

---

## 1. 直接端口转发 (Direct Forward)

### 1.1 数据流图

**数据流**：客户端应用 → forward client → /forward → server_forward → 指定目标服务

**特点**：
- 客户端指定固定的目标地址
- 服务器直接转发TCP数据流
- 无需服务发现或动态路由
- 适合固定端点的端口映射场景

```mermaid
graph LR
    A["客户端应用<br/>(浏览器/SSH等)"] --> B["wsstun forward<br/>--listen 127.0.0.1:8080<br/>--server wss://server.com<br/>--target *************:80"]
    B --> C["WebSocket连接<br/>wss://server.com/forward"]
    C --> D["WSSTun Server<br/>server_forward.rs"]
    D --> E["目标服务器<br/>*************:80"]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec
```

### 1.2 交互时序图

```mermaid
sequenceDiagram
    participant App as 客户端应用
    participant Client as wsstun forward
    participant Server as WSSTun Server
    participant Target as 目标服务器
    
    App->>Client: TCP连接 (127.0.0.1:8080)
    Client->>Server: WebSocket握手 (/forward)
    Server-->>Client: 握手成功
    Server->>Target: TCP连接 (*************:80)
    Target-->>Server: 连接确认
    
    loop 数据传输
        App->>Client: 发送数据
        Client->>Server: WebSocket消息
        Server->>Target: TCP数据
        Target->>Server: 响应数据
        Server->>Client: WebSocket消息
        Client->>App: TCP响应
    end
    
    App->>Client: 关闭连接
    Client->>Server: 关闭WebSocket
    Server->>Target: 关闭TCP连接
```

### 1.3 使用示例

```bash
# 服务器端
wsstun server --listen 0.0.0.0:8080

# 客户端
wsstun forward --listen 127.0.0.1:8080 --server wss://server.com --target *************:80
```

### 1.4 技术特点

- **简单直接**：最简单的转发模式，延迟最低
- **固定目标**：目标地址在启动时确定，不可动态变更
- **高性能**：无协议解析开销，纯TCP数据转发
- **适用场景**：SSH隧道、数据库连接、固定服务端口映射

---

## 2. 直接代理 (Direct Proxy)

### 2.1 数据流图

**数据流**：客户端应用 → proxy client → /proxy → server_proxy → 动态目标服务

**特点**：
- 客户端通过代理协议(SOCKS5/HTTP)指定目标
- 服务器解析代理协议后连接目标
- 支持动态目标地址
- 适合需要代理功能的场景

```mermaid
graph LR
    A["客户端应用<br/>(浏览器等)"] --> B["本地代理监听<br/>127.0.0.1:1080"]
    B --> C["wsstun proxy<br/>--listen 127.0.0.1:1080<br/>--server wss://server.com"]
    C --> D["WebSocket连接<br/>wss://server.com/proxy"]
    D --> E["WSSTun Server<br/>server_proxy.rs"]
    E --> F["解析代理协议<br/>(SOCKS5/HTTP)"]
    F --> G["动态目标服务器<br/>(example.com:443等)"]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#f3e5f5
    style D fill:#e8f5e8
    style E fill:#fff3e0
    style F fill:#fff3e0
    style G fill:#fce4ec
```

### 2.2 交互时序图

```mermaid
sequenceDiagram
    participant App as 客户端应用
    participant Client as wsstun proxy
    participant Server as WSSTun Server
    participant Target as 目标服务器
    
    App->>Client: SOCKS5/HTTP代理请求
    Client->>Server: WebSocket握手 (/proxy)
    Server-->>Client: 握手成功
    Client->>Server: 转发代理请求
    Server->>Server: 解析SOCKS5/HTTP协议
    Server->>Target: TCP连接 (解析出的目标)
    Target-->>Server: 连接确认
    Server->>Client: 代理连接成功响应
    Client->>App: SOCKS5/HTTP成功响应
    
    loop 数据传输
        App->>Client: 发送数据
        Client->>Server: WebSocket消息
        Server->>Target: TCP数据
        Target->>Server: 响应数据
        Server->>Client: WebSocket消息
        Client->>App: TCP响应
    end
    
    App->>Client: 关闭连接
    Client->>Server: 关闭WebSocket
    Server->>Target: 关闭TCP连接
```

### 2.3 使用示例

```bash
# 服务器端
wsstun server --listen 0.0.0.0:8080

# 客户端
wsstun proxy --listen 127.0.0.1:1080 --server wss://server.com

# 浏览器代理设置：SOCKS5 127.0.0.1:1080
```

### 2.4 技术特点

- **动态目标**：支持任意目标地址，由代理协议动态指定
- **协议支持**：兼容SOCKS5和HTTP CONNECT代理协议
- **服务器解析**：代理协议在服务器端解析，客户端逻辑简单
- **适用场景**：网页浏览、通用代理服务、绕过网络限制

---

## 3. Hub端口转发 (Hub Forward)

### 3.1 数据流图

**数据流**：客户端应用 → forward client → /hub/forward → hub_service_forward → hub → hub_service → 指定目标服务

**特点**：
- 通过service-id路由到特定的Hub Service
- Hub Service可部署在不同的网络环境
- 支持分布式端口转发
- 适合内网穿透和多节点部署场景

```mermaid
graph LR
    A["客户端应用<br/>(浏览器/SSH等)"] --> B["wsstun forward<br/>--listen 127.0.0.1:8080<br/>--server wss://server.com<br/>--target *************:80<br/>--service-id my-service"]
    B --> C["WebSocket连接<br/>wss://server.com/hub/forward"]
    C --> D["WSSTun Server<br/>hub_service_forward.rs"]
    D --> E["路由到Hub Service<br/>根据service-id匹配"]
    E --> F["Hub Service<br/>wsstun hub-service<br/>--service-id my-service"]
    F --> G["目标服务器<br/>*************:80"]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#ffecb3
    style F fill:#e1bee7
    style G fill:#fce4ec
```

### 3.2 交互时序图

```mermaid
sequenceDiagram
    participant App as 客户端应用
    participant Client as wsstun forward
    participant Server as WSSTun Server
    participant HubSvc as Hub Service
    participant Target as 目标服务器
    
    Note over HubSvc: 预先启动并注册
    HubSvc->>Server: WebSocket连接注册服务
    Server-->>HubSvc: 注册成功 (service-id)
    
    App->>Client: TCP连接 (127.0.0.1:8080)
    Client->>Server: WebSocket握手 (/hub/forward)
    Server-->>Client: 握手成功
    Client->>Server: HubForwardRequest (service-id + target)
    Server->>Server: 根据service-id路由
    Server->>HubSvc: ServiceForwardInstruction
    HubSvc->>Target: TCP连接 (*************:80)
    Target-->>HubSvc: 连接确认
    HubSvc-->>Server: ServiceInstructionAck
    Server-->>Client: 连接就绪确认
    
    loop 数据传输
        App->>Client: 发送数据
        Client->>Server: WebSocket消息
        Server->>HubSvc: 路由数据
        HubSvc->>Target: TCP数据
        Target->>HubSvc: 响应数据
        HubSvc->>Server: 返回数据
        Server->>Client: WebSocket消息
        Client->>App: TCP响应
    end
    
    App->>Client: 关闭连接
    Client->>Server: 关闭WebSocket
    Server->>HubSvc: 通知连接结束
    HubSvc->>Target: 关闭TCP连接
```

### 3.3 使用示例

```bash
# 服务器端
wsstun server --listen 0.0.0.0:8080

# Hub Service端 (内网机器)
wsstun hub-service --server wss://server.com --service-id my-service

# 客户端
wsstun forward --listen 127.0.0.1:8080 --server wss://server.com --target *************:80 --service-id my-service
```

### 3.4 技术特点

- **分布式架构**：Hub Service可部署在任意网络环境
- **服务发现**：通过service-id进行服务注册和路由
- **内网穿透**：Hub Service在内网，通过WebSocket连接到外网服务器
- **适用场景**：内网服务暴露、多地域部署、动态服务发现

---

## 4. Hub代理 (Hub Proxy)

### 4.1 数据流图

**数据流**：客户端应用 → proxy client → /hub/proxy → hub_service_proxy → hub → hub_service → 动态目标服务

**特点**：
- 结合Hub路由和代理功能
- Hub Service负责代理协议解析
- 支持分布式代理网络
- 适合多地域代理节点部署

```mermaid
graph LR
    A["客户端应用<br/>(浏览器等)"] --> B["本地代理监听<br/>127.0.0.1:1080"]
    B --> C["wsstun proxy<br/>--listen 127.0.0.1:1080<br/>--server wss://server.com<br/>--service-id my-proxy"]
    C --> D["WebSocket连接<br/>wss://server.com/hub/proxy"]
    D --> E["WSSTun Server<br/>hub_service_proxy.rs"]
    E --> F["路由到Hub Service<br/>根据service-id匹配"]
    F --> G["Hub Service<br/>wsstun hub-service<br/>--service-id my-proxy"]
    G --> H["解析代理协议<br/>(SOCKS5/HTTP)"]
    H --> I["动态目标服务器<br/>(example.com:443等)"]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#f3e5f5
    style D fill:#e8f5e8
    style E fill:#fff3e0
    style F fill:#ffecb3
    style G fill:#e1bee7
    style H fill:#e1bee7
    style I fill:#fce4ec
```

### 4.2 交互时序图

```mermaid
sequenceDiagram
    participant App as 客户端应用
    participant Client as wsstun proxy
    participant Server as WSSTun Server
    participant HubSvc as Hub Service
    participant Target as 目标服务器
    
    Note over HubSvc: 预先启动并注册
    HubSvc->>Server: WebSocket连接注册服务
    Server-->>HubSvc: 注册成功 (service-id)
    
    App->>Client: SOCKS5/HTTP代理请求
    Client->>Server: WebSocket握手 (/hub/proxy)
    Server-->>Client: 握手成功
    Client->>Server: HubProxyRequest (service-id)
    Server->>Server: 建立与Hub Service连接
    Client->>Server: 转发代理协议数据
    Server->>HubSvc: 转发代理协议
    HubSvc->>HubSvc: 解析SOCKS5/HTTP协议
    HubSvc->>Target: TCP连接 (解析出的目标)
    Target-->>HubSvc: 连接确认
    HubSvc->>Server: 代理连接成功
    Server->>Client: 连接成功响应
    Client->>App: SOCKS5/HTTP成功响应
    
    loop 数据传输
        App->>Client: 发送数据
        Client->>Server: WebSocket消息
        Server->>HubSvc: 路由数据
        HubSvc->>Target: TCP数据
        Target->>HubSvc: 响应数据
        HubSvc->>Server: 返回数据
        Server->>Client: WebSocket消息
        Client->>App: TCP响应
    end
    
    App->>Client: 关闭连接
    Client->>Server: 关闭WebSocket
    Server->>HubSvc: 通知连接结束
    HubSvc->>Target: 关闭TCP连接
```

### 4.3 使用示例

```bash
# 服务器端
wsstun server --listen 0.0.0.0:8080

# Hub Service端 (美国节点)
wsstun hub-service --server wss://server.com --service-id us-proxy

# 客户端
wsstun proxy --listen 127.0.0.1:1080 --server wss://server.com --service-id us-proxy

# 浏览器代理设置：SOCKS5 127.0.0.1:1080
```

### 4.4 技术特点

- **分布式代理**：Hub Service作为代理节点分布在不同地域
- **灵活路由**：通过service-id选择不同的代理节点
- **协议兼容**：支持标准的SOCKS5/HTTP代理协议
- **适用场景**：多地域代理网络、CDN加速、负载均衡代理

---

## 5. 模式对比总结

### 5.1 功能对比表

| 模式 | 目标地址 | 协议解析 | 路由方式 | 部署复杂度 | 适用场景 |
|------|----------|----------|----------|------------|----------|
| Direct Forward | 客户端指定 | 无需解析 | 直接连接 | 低 | 固定端口映射 |
| Direct Proxy | 动态解析 | 服务器端 | 直接连接 | 低 | 简单代理服务 |
| Hub Forward | 客户端指定 | 无需解析 | Hub路由 | 中 | 分布式端口转发 |
| Hub Proxy | 动态解析 | Hub Service端 | Hub路由 | 高 | 分布式代理网络 |

### 5.2 性能对比

| 指标 | Direct Forward | Direct Proxy | Hub Forward | Hub Proxy |
|------|----------------|--------------|-------------|-----------|
| 延迟 | 最低 | 低 | 中 | 高 |
| 吞吐量 | 最高 | 高 | 中 | 中 |
| 可扩展性 | 低 | 低 | 高 | 最高 |
| 容错能力 | 低 | 低 | 中 | 高 |

### 5.3 架构复杂度

```
简单 ────────────────────────────────────── 复杂
  │                │              │         │
Direct Forward  Direct Proxy  Hub Forward  Hub Proxy
```

### 5.4 选择建议

**选择Direct Forward模式，当你需要**：
- 简单的端口转发
- 最低的延迟和最高的性能
- 固定的目标服务器
- 最简单的部署和维护

**选择Direct Proxy模式，当你需要**：
- 基本的代理功能
- 兼容标准代理协议
- 中等的性能要求
- 相对简单的部署

**选择Hub Forward模式，当你需要**：
- 内网穿透功能
- 分布式端口转发
- 服务发现和路由
- 多节点部署

**选择Hub Proxy模式，当你需要**：
- 分布式代理网络
- 多地域代理节点
- 高可用和负载均衡
- 最大的灵活性和可扩展性 