#!/usr/bin/env python3

import asyncio
import websockets
import websockets.exceptions
import base64

async def test_auth_detailed():
    """Test authentication with detailed debugging"""
    
    # Test credentials (same as in the test)
    username = "testuser"
    password = "testpassword"
    
    # Create correct auth header
    correct_auth_header = base64.b64encode(f"{username}:{password}".encode()).decode()
    headers = {"Authorization": f"Basic {correct_auth_header}"}
    
    print("=== Testing Authentication ===")
    print(f"Username: {username}")
    print(f"Password: {password}")
    print(f"Auth Header: {headers}")
    print(f"Target URI: ws://localhost:8060/hub")
    
    try:
        async with websockets.connect("ws://localhost:8060/hub", additional_headers=headers) as ws:
            print("✅ SUCCESS: Connected with correct credentials")
            
    except websockets.exceptions.InvalidHandshake as e:
        print(f"❌ InvalidHandshake: {e}")
        if hasattr(e, 'response') and hasattr(e.response, 'status_code'):
            print(f"   Status Code: {e.response.status_code}")
            print(f"   Response Headers: {dict(e.response.headers)}")
        elif hasattr(e, 'status_code'):
            print(f"   Status Code: {e.status_code}")
        
    except websockets.exceptions.InvalidStatus as e:
        print(f"❌ InvalidStatus: {e}")
        if hasattr(e, 'response') and hasattr(e.response, 'status_code'):
            print(f"   Status Code: {e.response.status_code}")
            print(f"   Response Headers: {dict(e.response.headers)}")
        
    except Exception as e:
        print(f"❌ Other Exception: {type(e).__name__}: {e}")

if __name__ == "__main__":
    # First start the server manually with: wsstun server --listen 127.0.0.1:8060 --username testuser --password testpassword --log-level debug
    print("Please start the server manually first with:")
    print("wsstun server --listen 127.0.0.1:8060 --username testuser --password testpassword --log-level debug")
    print("Assuming server is already running...")
    
    asyncio.run(test_auth_detailed())