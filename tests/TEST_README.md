# WSSTun Comprehensive Test Suite

This is a comprehensive test suite for verifying all major functionality modes of the WSSTun application, including various communication scenarios for client/server/hub-service.

**📁 Directory Structure**: All test-related files are located in the `tests/` directory, separated from project source code.

## 🎯 Test Coverage

### Basic Functionality Tests
- ✅ Binary file existence and executability check
- ✅ Help command output verification
- ✅ Command line argument parsing tests

### Server Mode Tests
- ✅ Basic WebSocket server startup
- ✅ Port listening verification
- ✅ WebSocket connection establishment tests

### Client Forward Mode Tests
- ✅ Basic port forwarding functionality
- ✅ Mux mode port forwarding
- ✅ Data transmission verification
- ✅ Concurrent connection tests
- 🔄 Authentication functionality tests (to be implemented)

### Client Proxy Mode Tests
- ✅ Basic proxy server startup
- ✅ Mux mode proxy server
- ✅ SOCKS5 protocol support tests
- ✅ HTTP proxy protocol tests

### Hub Service Mode Tests
- ✅ Hub Service registration and startup
- ✅ Heartbeat mechanism verification
- ✅ Service discovery functionality

### Comprehensive Scenario Tests
- ✅ Forward functionality through Hub
- ✅ Proxy functionality through Hub
- ✅ Multi-client concurrent connections
- ✅ High concurrency stress tests

### Error Handling Tests
- ✅ Connection failure handling
- ✅ Invalid configuration detection
- ✅ Reconnection mechanism verification

## 📋 Dependencies

### Python Dependencies
```bash
pip install -r requirements.txt
```

### System Requirements
- Python 3.7+
- Rust toolchain (if compilation needed)
- Available network ports (8000-9000 range)

## 🚀 Quick Start

**Important**: Run all test commands in the `tests/` directory.

### Step 1: Enter test directory
```bash
cd tests
```

### Step 2: Run tests (automatically installs dependencies and compiles)

#### Linux/macOS
```bash
# Run all tests (including compilation and auto dependency installation)
./run_tests.sh

# Verbose output mode
./run_tests.sh --verbose

# Skip compilation, run tests only
./run_tests.sh --no-build

# Use release mode
./run_tests.sh --release
```

#### Windows
```cmd
# Run all tests (including compilation and auto dependency installation)
run_tests.bat

# Verbose output mode
run_tests.bat --verbose

# Skip compilation, run tests only
run_tests.bat --no-build

# Use release mode
run_tests.bat --release
```

#### Direct Python Script Usage
```bash
# Basic run
python test_wsstun_comprehensive.py

# Specify binary file path
python test_wsstun_comprehensive.py --binary ../target/release/wsstun

# Verbose output and keep logs
python test_wsstun_comprehensive.py --verbose --keep-logs
```

## ⚙️ Configuration Options

### Command Line Arguments

| Parameter | Description | Default Value |
|-----------|-------------|---------------|
| `--binary` | wsstun binary file path | `../target/debug/wsstun` |
| `--server-port` | Test server port | `8060` |
| `--timeout` | Test timeout (seconds) | `30` |
| `--verbose` | Verbose output mode | `false` |
| `--keep-logs` | Keep test log files | `false` |
| `--no-build` | Skip build step | `false` |
| `--clean` | Clean before build | `false` |
| `--release` | Use release mode | `false` |

### Environment Variables
Test scripts will automatically detect and use the following environment variables:
- `RUST_LOG`: Rust log level
- `PYTHONPATH`: Python module search path

## 📊 Test Output

### Success Example
```
=== WSSTun Comprehensive Test Configuration ===
Binary file: ../target/debug/wsstun
Log directory: ./test_logs

✓ Binary file existence check
✓ Help command test
✓ Server basic functionality
✓ WebSocket connection test
✓ Forward basic functionality
✓ Forward Mux mode (success 3/3)
⊘ Forward authentication functionality (skipped)
✓ Proxy basic functionality
✓ Proxy Mux mode
✓ SOCKS5 proxy test
✓ HTTP proxy test
✓ Hub Service basic functionality
⊘ Hub Service heartbeat mechanism (skipped)
✓ Hub Forward comprehensive test
✓ Hub Proxy comprehensive test
✓ Multi-client concurrent test (success 2/2)
✓ Connection failure handling test
✓ Invalid configuration handling test
✓ High concurrency test (success 8/10)
⊘ Reconnection mechanism test (skipped)

=== Test Results Summary ===
Total: 18, Passed: 15, Failed: 0, Skipped: 3
```

### Failure Example
```
✗ Forward basic functionality
✗ Proxy SOCKS5 test

=== Test Results Summary ===
Total: 18, Passed: 13, Failed: 2, Skipped: 3

Error Details:
  - Forward basic functionality: Data forwarding test failed
  - Proxy SOCKS5 test: SOCKS5 connection test failed
```

## 📁 Log Files

Detailed log files are generated during testing and saved in the `tests/test_logs/` directory:

```
tests/test_logs/
├── server.log                 # Server logs
├── forward_client.log         # Forward client logs
├── forward_mux_client.log     # Forward Mux client logs
├── proxy_client.log           # Proxy client logs
├── proxy_mux_client.log       # Proxy Mux client logs
├── hub_service.log            # Hub Service logs
├── hub_forward_client.log     # Hub Forward client logs
├── hub_proxy_client.log       # Hub Proxy client logs
└── ...                        # Other test process logs
```

Use the `--keep-logs` parameter to retain these log files after testing for troubleshooting.

## 🔧 Troubleshooting

### Common Issues

#### 1. Build failure
```bash
# Check Rust toolchain
rustc --version
cargo --version

# Clean and rebuild in project root directory
cd ..
cargo clean
cargo build
cd tests
```

#### 2. Python dependencies missing
```bash
# Ensure in tests directory
cd tests

# Install required Python packages (test scripts will auto-install)
pip install -r requirements.txt

# Check Python version
python3 --version  # Requires 3.7+
```

#### 3. Port conflicts
```bash
# Check port usage
netstat -tulpn | grep :8060

# Use different port for tests
./run_tests.sh --port 8061
```

#### 4. Permission issues (Linux/macOS)
```bash
# Add execute permissions in tests directory
chmod +x run_tests.sh

# Check binary file permissions
ls -la ../target/debug/wsstun
```

### Debug Mode

Enable verbose output for more debugging information:
```bash
# Verbose output mode
./run_tests.sh --verbose --keep-logs

# View specific logs
tail -f ./test_logs/server.log
```

### Manual Testing

If automated tests fail, you can manually run each component:

```bash
# 1. Start server (in project root directory)
cd ..
./target/debug/wsstun server --listen 127.0.0.1:8060 --log-level debug

# 2. Start forward client (new terminal, in project root directory)
./target/debug/wsstun forward \
    --server ws://127.0.0.1:8060 \
    --listen 127.0.0.1:8001 \
    --target 127.0.0.1:22 \
    --log-level debug

# 3. Test connection (new terminal)
telnet 127.0.0.1 8001
```

## 🤝 Contributing

If you find missing test cases or need improvements, welcome to submit PRs:

1. Add new test cases to `tests/test_wsstun_comprehensive.py`
2. Update corresponding documentation
3. Ensure all existing tests still pass

## 📝 Test Case Addition Guide

### Adding New Test Cases

```python
async def test_your_new_feature(self):
    """Test your new feature"""
    test_name = "Your new feature test"
    self.log(f"Starting test: {test_name}")
    
    try:
        # Test logic
        if await your_test_logic():
            self.result.add_pass()
            print(f"{Colors.GREEN}✓{Colors.END} {test_name}")
        else:
            self.result.add_fail(f"{test_name}: Test failure reason")
            print(f"{Colors.RED}✗{Colors.END} {test_name}")
            
    except Exception as e:
        self.result.add_fail(f"{test_name}: {str(e)}")
        print(f"{Colors.RED}✗{Colors.END} {test_name}")
```

### Call in Main Test Flow

```python
async def run_all_tests(self):
    # ... existing tests ...
    await self.test_your_new_feature()
    # ... other tests ...
```

## 📞 Support

If you encounter problems or need help:

1. Check log files for detailed error information
2. Review the [Troubleshooting](#troubleshooting) section
3. Submit an Issue with relevant logs

---

**Note**: 
- This test suite is designed to establish baseline tests before code refactoring
- It's recommended to run the complete test suite before making any major code changes to ensure all functionality works properly
- **All test commands should be run in the `tests/` directory** 