"""
Test helper utilities and enhanced process management for wsstun testing.

This module provides enhanced ProcessManager with better error handling,
logging, and process monitoring capabilities.
"""

import asyncio
import subprocess
import time
import socket
import threading
import json
import logging
import sys
import os
import signal
import tempfile
import atexit
import psutil
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any, Callable
from dataclasses import dataclass
from contextlib import asynccontextmanager
from enum import Enum


class TestExecutionStatus(Enum):
    """Test execution status"""
    NOT_STARTED = "not_started"
    RUNNING = "running"
    PASSED = "passed"
    FAILED = "failed"
    SKIPPED = "skipped"
    ERROR = "error"


class ProcessStatus(Enum):
    """Process status enumeration"""
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    STOPPED = "stopped"
    FAILED = "failed"
    UNKNOWN = "unknown"


@dataclass
class TestConfig:
    """Enhanced test configuration"""
    wsstun_binary: str = "../target/debug/wsstun"
    server_host: str = "127.0.0.1"
    server_port: int = 8060
    test_timeout: int = 30
    verbose: bool = False
    keep_logs: bool = False
    log_dir: str = "./test_logs"
    
    # Enhanced configuration options
    max_process_startup_time: int = 10
    process_cleanup_timeout: int = 5
    log_rotation_size: int = 10 * 1024 * 1024  # 10MB
    enable_process_monitoring: bool = True
    heartbeat_check_interval: int = 5


@dataclass
class ProcessInfo:
    """Information about a managed process"""
    name: str
    process: subprocess.Popen
    cmd: List[str]
    log_file: str
    start_time: float
    status: ProcessStatus
    pid: int
    cpu_percent: float = 0.0
    memory_mb: float = 0.0
    restart_count: int = 0
    last_heartbeat: Optional[float] = None


class Colors:
    """Terminal color codes"""
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'
    RESET = '\033[0m'  # Alias for END


class DiagnosticHandler(logging.Handler):
    """Custom logging handler for diagnostic data collection"""
    
    def __init__(self, process_manager):
        super().__init__()
        self.process_manager = process_manager
    
    def emit(self, record):
        """Collect diagnostic information from log records"""
        try:
            if record.levelno >= logging.WARNING:
                error_info = {
                    "timestamp": time.time(),
                    "level": record.levelname,
                    "message": record.getMessage(),
                    "module": record.module,
                    "function": record.funcName,
                    "line": record.lineno
                }
                
                # Add exception info if available
                if record.exc_info:
                    error_info["exception"] = str(record.exc_info[1])
                
                self.process_manager.diagnostic_data["error_history"].append(error_info)
                
                # Keep only last 100 errors to prevent memory bloat
                if len(self.process_manager.diagnostic_data["error_history"]) > 100:
                    self.process_manager.diagnostic_data["error_history"] = \
                        self.process_manager.diagnostic_data["error_history"][-100:]
        except Exception:
            # Silently ignore errors in diagnostic handler to prevent recursion
            pass


class TestResult:
    """Enhanced test result tracking"""
    def __init__(self):
        self.passed = 0
        self.failed = 0
        self.skipped = 0
        self.errors: List[str] = []
        self.test_details: Dict[str, Dict[str, Any]] = {}
        self.start_time = time.time()
        self.end_time: Optional[float] = None
    
    def add_pass(self, test_name: str = "", details: Optional[Dict[str, Any]] = None):
        self.passed += 1
        if test_name:
            self.test_details[test_name] = {
                "status": TestExecutionStatus.PASSED,
                "details": details or {},
                "timestamp": time.time()
            }
    
    def add_fail(self, error: str, test_name: str = "", details: Optional[Dict[str, Any]] = None):
        self.failed += 1
        self.errors.append(error)
        if test_name:
            self.test_details[test_name] = {
                "status": TestExecutionStatus.FAILED,
                "error": error,
                "details": details or {},
                "timestamp": time.time()
            }
    
    def add_skip(self, reason: str, test_name: str = "", details: Optional[Dict[str, Any]] = None):
        self.skipped += 1
        self.errors.append(f"SKIPPED: {reason}")
        if test_name:
            self.test_details[test_name] = {
                "status": TestExecutionStatus.SKIPPED,
                "reason": reason,
                "details": details or {},
                "timestamp": time.time()
            }
    
    def finalize(self):
        """Finalize test results"""
        self.end_time = time.time()
    
    def get_duration(self) -> float:
        """Get total test duration"""
        end = self.end_time or time.time()
        return end - self.start_time
    
    def summary(self) -> str:
        total = self.passed + self.failed + self.skipped
        duration = self.get_duration()
        return (f"Total: {total}, Passed: {Colors.GREEN}{self.passed}{Colors.END}, "
                f"Failed: {Colors.RED}{self.failed}{Colors.END}, "
                f"Skipped: {Colors.YELLOW}{self.skipped}{Colors.END}, "
                f"Duration: {duration:.2f}s")
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate detailed test report"""
        return {
            "summary": {
                "total": self.passed + self.failed + self.skipped,
                "passed": self.passed,
                "failed": self.failed,
                "skipped": self.skipped,
                "duration": self.get_duration(),
                "success_rate": self.passed / (self.passed + self.failed) if (self.passed + self.failed) > 0 else 0
            },
            "test_details": self.test_details,
            "errors": self.errors,
            "timestamp": time.time()
        }


class EnhancedProcessManager:
    """
    Enhanced process manager with improved error handling, logging, and monitoring.
    
    Features:
    - Process status monitoring
    - Resource usage tracking
    - Automatic restart capabilities
    - Enhanced logging and error handling
    - Process health checks
    - Graceful shutdown handling
    - Detailed diagnostic information
    - Process lifecycle management
    - Resource leak prevention
    """
    
    def __init__(self, config: "TestConfig"):
        self.config = config
        self.processes: Dict[str, ProcessInfo] = {}
        self.logger = self._setup_logger()
        self.monitoring_active = False
        self.monitoring_task: Optional[asyncio.Task] = None
        self.cleanup_callbacks: List[Callable] = []
        self.diagnostic_data: Dict[str, Any] = {}
        
        # Create log directory
        os.makedirs(config.log_dir, exist_ok=True)
        
        # Initialize diagnostic tracking
        self._init_diagnostic_tracking()
        
        # Register cleanup function
        atexit.register(self.cleanup_all_processes)
        
        # Register signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        # Start process monitoring if enabled
        if config.enable_process_monitoring:
            self.start_monitoring()
    
    def _init_diagnostic_tracking(self):
        """Initialize diagnostic data tracking"""
        self.diagnostic_data = {
            "startup_time": time.time(),
            "total_processes_started": 0,
            "total_processes_failed": 0,
            "total_restarts": 0,
            "cleanup_operations": 0,
            "resource_warnings": [],
            "error_history": [],
            "performance_metrics": {
                "avg_startup_time": 0.0,
                "max_memory_usage": 0.0,
                "max_cpu_usage": 0.0
            }
        }
    
    def _setup_logger(self) -> logging.Logger:
        """Setup enhanced logger for process manager with detailed formatting"""
        logger = logging.getLogger(f"{__name__}.ProcessManager")
        logger.setLevel(logging.DEBUG if self.config.verbose else logging.INFO)
        
        # Clear any existing handlers
        logger.handlers.clear()
        
        # Create enhanced formatter with more context
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - [PID:%(process)d] - %(message)s'
        )
        
        # Console handler with color support
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # File handler with rotation (only if log directory exists)
        try:
            log_file = os.path.join(self.config.log_dir, "process_manager.log")
            
            # Ensure log directory exists
            os.makedirs(os.path.dirname(log_file), exist_ok=True)
            
            try:
                from logging.handlers import RotatingFileHandler
                file_handler = RotatingFileHandler(
                    log_file, 
                    maxBytes=self.config.log_rotation_size,
                    backupCount=3
                )
            except ImportError:
                file_handler = logging.FileHandler(log_file)
            
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
            
        except Exception as e:
            # If file logging fails, just use console logging
            logger.warning(f"Could not setup file logging: {e}")
        
        # Add diagnostic handler for error tracking
        try:
            diagnostic_handler = DiagnosticHandler(self)
            diagnostic_handler.setLevel(logging.WARNING)
            logger.addHandler(diagnostic_handler)
        except Exception as e:
            logger.warning(f"Could not setup diagnostic handler: {e}")
        
        return logger
    
    def _signal_handler(self, signum, frame):
        """Enhanced signal handler for graceful shutdown"""
        self.logger.warning(f"Received signal {signum}, initiating graceful shutdown...")
        self.stop_monitoring()
        self.cleanup_all_processes()
        sys.exit(0)
    
    def start_monitoring(self):
        """Start process monitoring"""
        if not self.monitoring_active:
            self.monitoring_active = True
            try:
                self.monitoring_task = asyncio.create_task(self._monitoring_loop())
                self.logger.info("Process monitoring started")
            except RuntimeError as e:
                if "no running event loop" in str(e):
                    self.logger.debug("No event loop available for monitoring, will start later")
                    self.monitoring_task = None
                else:
                    raise
    
    def stop_monitoring(self):
        """Stop process monitoring"""
        if self.monitoring_active:
            self.monitoring_active = False
            if self.monitoring_task:
                self.monitoring_task.cancel()
            self.logger.info("Process monitoring stopped")
    
    async def _monitoring_loop(self):
        """Process monitoring loop"""
        while self.monitoring_active:
            try:
                await self._update_process_stats()
                await self._check_process_health()
                await asyncio.sleep(self.config.heartbeat_check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(1)
    
    async def _update_process_stats(self):
        """Update process statistics with enhanced monitoring"""
        for name, proc_info in self.processes.items():
            try:
                if proc_info.process.poll() is None:  # Process is running
                    psutil_proc = psutil.Process(proc_info.pid)
                    
                    # Update resource usage
                    cpu_percent = psutil_proc.cpu_percent()
                    memory_info = psutil_proc.memory_info()
                    memory_mb = memory_info.rss / 1024 / 1024
                    
                    proc_info.cpu_percent = cpu_percent
                    proc_info.memory_mb = memory_mb
                    proc_info.last_heartbeat = time.time()
                    
                    # Update global performance metrics
                    metrics = self.diagnostic_data["performance_metrics"]
                    if memory_mb > metrics["max_memory_usage"]:
                        metrics["max_memory_usage"] = memory_mb
                    if cpu_percent > metrics["max_cpu_usage"]:
                        metrics["max_cpu_usage"] = cpu_percent
                    
                    # Check for resource warnings
                    self._check_resource_warnings(name, proc_info, cpu_percent, memory_mb)
                    
                    if proc_info.status != ProcessStatus.RUNNING:
                        proc_info.status = ProcessStatus.RUNNING
                        self.logger.info(f"Process {name} is now running")
                else:
                    # Process has exited
                    if proc_info.status == ProcessStatus.RUNNING:
                        exit_code = proc_info.process.returncode
                        proc_info.status = ProcessStatus.STOPPED
                        self.logger.warning(f"Process {name} has stopped unexpectedly with exit code {exit_code}")
                        self._capture_failure_diagnostics(name, f"unexpected_exit_{exit_code}")
                        
            except psutil.NoSuchProcess:
                if proc_info.status != ProcessStatus.STOPPED:
                    proc_info.status = ProcessStatus.STOPPED
                    self.logger.warning(f"Process {name} no longer exists")
                    self._capture_failure_diagnostics(name, "process_disappeared")
            except Exception as e:
                self.logger.error(f"Error updating stats for {name}: {e}", exc_info=True)
    
    def _check_resource_warnings(self, name: str, proc_info: ProcessInfo, 
                                cpu_percent: float, memory_mb: float):
        """Check for resource usage warnings and log them"""
        current_time = time.time()
        
        # CPU usage warning
        if cpu_percent > 90:
            warning = {
                "timestamp": current_time,
                "process": name,
                "type": "high_cpu",
                "value": cpu_percent,
                "threshold": 90
            }
            self.diagnostic_data["resource_warnings"].append(warning)
            self.logger.warning(f"Process {name} has high CPU usage: {cpu_percent:.1f}%")
        
        # Memory usage warning
        if memory_mb > 1000:  # 1GB threshold
            warning = {
                "timestamp": current_time,
                "process": name,
                "type": "high_memory",
                "value": memory_mb,
                "threshold": 1000
            }
            self.diagnostic_data["resource_warnings"].append(warning)
            self.logger.warning(f"Process {name} has high memory usage: {memory_mb:.1f}MB")
        
        # Keep only recent warnings (last hour)
        one_hour_ago = current_time - 3600
        self.diagnostic_data["resource_warnings"] = [
            w for w in self.diagnostic_data["resource_warnings"] 
            if w["timestamp"] > one_hour_ago
        ]
    
    async def _check_process_health(self):
        """Check process health and restart if needed"""
        for name, proc_info in self.processes.items():
            try:
                # Check if process is responsive
                if proc_info.status == ProcessStatus.RUNNING:
                    # Check for high resource usage
                    if proc_info.cpu_percent > 90:
                        self.logger.warning(f"Process {name} has high CPU usage: {proc_info.cpu_percent}%")
                    
                    if proc_info.memory_mb > 1000:  # 1GB threshold
                        self.logger.warning(f"Process {name} has high memory usage: {proc_info.memory_mb:.1f}MB")
                
                # Check for zombie processes
                elif proc_info.status == ProcessStatus.STOPPED and proc_info.process.poll() is not None:
                    self.logger.info(f"Cleaning up stopped process {name}")
                    await self._cleanup_process(name)
                    
            except Exception as e:
                self.logger.error(f"Error checking health for {name}: {e}")
    
    async def start_process(self, name: str, cmd: List[str], 
                          wait_for_ready: Optional[str] = None,
                          auto_restart: bool = False,
                          env: Optional[Dict[str, str]] = None) -> bool:
        """
        Enhanced process startup with better error handling and monitoring.
        
        Args:
            name: Process name
            cmd: Command to execute
            wait_for_ready: String to wait for in logs
            auto_restart: Whether to automatically restart on failure
            env: Environment variables
            
        Returns:
            True if process started successfully
        """
        start_time = time.time()
        self.logger.info(f"Starting process {name} with command: {' '.join(cmd)}")
        
        # Update diagnostic data
        self.diagnostic_data["total_processes_started"] += 1
        
        # Stop existing process if running
        if name in self.processes:
            self.logger.info(f"Stopping existing process {name} before restart")
            await self.stop_process(name)
        
        log_file = os.path.join(self.config.log_dir, f"{name}.log")
        
        try:
            # Validate command exists
            if not self._validate_command(cmd[0]):
                error_msg = f"Command not found: {cmd[0]}"
                self.logger.error(error_msg)
                self.diagnostic_data["total_processes_failed"] += 1
                return False
            
            # Prepare environment
            process_env = os.environ.copy()
            if env:
                process_env.update(env)
                self.logger.debug(f"Added environment variables for {name}: {list(env.keys())}")
            
            # Start process with enhanced error handling
            with open(log_file, 'w') as f:
                process = subprocess.Popen(
                    cmd,
                    stdout=f,
                    stderr=subprocess.STDOUT,
                    text=True,
                    env=process_env,
                    preexec_fn=os.setsid if os.name != 'nt' else None
                )
            
            # Create process info
            proc_info = ProcessInfo(
                name=name,
                process=process,
                cmd=cmd,
                log_file=log_file,
                start_time=time.time(),
                status=ProcessStatus.STARTING,
                pid=process.pid
            )
            
            self.processes[name] = proc_info
            self.logger.info(f"Process {name} started with PID {process.pid}")
            
            # Wait for process to be ready
            if wait_for_ready:
                success = await self._wait_for_ready(name, wait_for_ready)
                if success:
                    proc_info.status = ProcessStatus.RUNNING
                    startup_duration = time.time() - start_time
                    self._update_performance_metrics(startup_duration)
                    self.logger.info(f"Process {name} is ready (startup took {startup_duration:.2f}s)")
                    return True
                else:
                    self.logger.error(f"Process {name} failed to become ready")
                    self._capture_failure_diagnostics(name, "ready_timeout")
                    await self.stop_process(name)
                    self.diagnostic_data["total_processes_failed"] += 1
                    return False
            else:
                # Give process some startup time
                await asyncio.sleep(1)
                if process.poll() is None:
                    proc_info.status = ProcessStatus.RUNNING
                    startup_duration = time.time() - start_time
                    self._update_performance_metrics(startup_duration)
                    return True
                else:
                    self.logger.error(f"Process {name} exited immediately with code {process.returncode}")
                    self._capture_failure_diagnostics(name, "immediate_exit")
                    proc_info.status = ProcessStatus.FAILED
                    self.diagnostic_data["total_processes_failed"] += 1
                    return False
                    
        except Exception as e:
            self.logger.error(f"Failed to start process {name}: {e}", exc_info=True)
            self._capture_failure_diagnostics(name, f"startup_exception: {e}")
            if name in self.processes:
                del self.processes[name]
            self.diagnostic_data["total_processes_failed"] += 1
            return False
    
    def _validate_command(self, command: str) -> bool:
        """Validate that a command exists and is executable"""
        try:
            # Check if command exists in PATH
            import shutil
            return shutil.which(command) is not None or os.path.isfile(command)
        except Exception:
            return False
    
    def _update_performance_metrics(self, startup_time: float):
        """Update performance metrics with new data"""
        metrics = self.diagnostic_data["performance_metrics"]
        
        # Update average startup time
        total_started = self.diagnostic_data["total_processes_started"]
        if total_started > 0:
            current_avg = metrics["avg_startup_time"]
            metrics["avg_startup_time"] = ((current_avg * (total_started - 1)) + startup_time) / total_started
    
    def _capture_failure_diagnostics(self, name: str, failure_type: str):
        """Capture detailed diagnostic information for process failures"""
        diagnostic_info = {
            "timestamp": time.time(),
            "process_name": name,
            "failure_type": failure_type,
            "system_info": {
                "cpu_count": os.cpu_count(),
                "load_average": os.getloadavg() if hasattr(os, 'getloadavg') else None,
                "available_memory": self._get_available_memory()
            }
        }
        
        # Capture process info if available
        if name in self.processes:
            proc_info = self.processes[name]
            diagnostic_info["process_info"] = {
                "pid": proc_info.pid,
                "command": " ".join(proc_info.cmd),
                "uptime": time.time() - proc_info.start_time,
                "status": proc_info.status.value
            }
            
            # Capture recent log output
            try:
                recent_logs = self.get_log_content(name, tail_lines=20)
                diagnostic_info["recent_logs"] = recent_logs
            except Exception:
                pass
        
        # Store diagnostic info
        if "failure_diagnostics" not in self.diagnostic_data:
            self.diagnostic_data["failure_diagnostics"] = []
        
        self.diagnostic_data["failure_diagnostics"].append(diagnostic_info)
        
        # Keep only last 50 failure diagnostics
        if len(self.diagnostic_data["failure_diagnostics"]) > 50:
            self.diagnostic_data["failure_diagnostics"] = \
                self.diagnostic_data["failure_diagnostics"][-50:]
    
    def _get_available_memory(self) -> Optional[float]:
        """Get available system memory in MB"""
        try:
            import psutil
            return psutil.virtual_memory().available / 1024 / 1024
        except Exception:
            return None
    
    async def _wait_for_ready(self, name: str, ready_string: str) -> bool:
        """Wait for process to be ready by monitoring log output"""
        start_time = time.time()
        timeout = self.config.max_process_startup_time
        
        while time.time() - start_time < timeout:
            proc_info = self.processes.get(name)
            if not proc_info:
                return False
            
            # Check if process is still running
            if proc_info.process.poll() is not None:
                self.logger.error(f"Process {name} exited while waiting for ready signal")
                return False
            
            # Check log file for ready string
            try:
                with open(proc_info.log_file, 'r') as f:
                    content = f.read()
                    if ready_string in content:
                        await asyncio.sleep(0.2)  # Extra stabilization time
                        return True
            except Exception as e:
                self.logger.debug(f"Error reading log file for {name}: {e}")
            
            await asyncio.sleep(0.1)
        
        self.logger.error(f"Timeout waiting for {name} to be ready")
        return False
    
    async def stop_process(self, name: str, force: bool = False) -> bool:
        """
        Enhanced process stopping with graceful shutdown.
        
        Args:
            name: Process name
            force: Whether to force kill immediately
            
        Returns:
            True if process was stopped successfully
        """
        if name not in self.processes:
            self.logger.warning(f"Process {name} not found")
            return False
        
        proc_info = self.processes[name]
        self.logger.info(f"Stopping process {name} (PID: {proc_info.pid})")
        
        proc_info.status = ProcessStatus.STOPPING
        
        try:
            process = proc_info.process
            
            if force:
                # Force kill immediately
                if os.name == 'nt':
                    process.kill()
                else:
                    os.killpg(os.getpgid(process.pid), signal.SIGKILL)
                
                try:
                    process.wait(timeout=2)
                except subprocess.TimeoutExpired:
                    pass
            else:
                # Graceful shutdown
                if os.name == 'nt':
                    # Windows
                    try:
                        process.terminate()
                        process.wait(timeout=self.config.process_cleanup_timeout)
                    except subprocess.TimeoutExpired:
                        self.logger.warning(f"Process {name} did not terminate gracefully, killing...")
                        process.kill()
                        try:
                            process.wait(timeout=2)
                        except subprocess.TimeoutExpired:
                            self.logger.error(f"Failed to kill process {name}")
                else:
                    # Unix-like
                    try:
                        os.killpg(os.getpgid(process.pid), signal.SIGTERM)
                        process.wait(timeout=self.config.process_cleanup_timeout)
                    except subprocess.TimeoutExpired:
                        self.logger.warning(f"Process {name} did not terminate gracefully, killing...")
                        os.killpg(os.getpgid(process.pid), signal.SIGKILL)
                        process.wait()
            
            proc_info.status = ProcessStatus.STOPPED
            self.logger.info(f"Process {name} stopped successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Error stopping process {name}: {e}")
            proc_info.status = ProcessStatus.FAILED
            return False
    
    async def _cleanup_process(self, name: str):
        """Clean up process resources"""
        if name in self.processes:
            proc_info = self.processes[name]
            
            # Close log file handles if needed
            try:
                if hasattr(proc_info.process, 'stdout') and proc_info.process.stdout:
                    proc_info.process.stdout.close()
                if hasattr(proc_info.process, 'stderr') and proc_info.process.stderr:
                    proc_info.process.stderr.close()
            except:
                pass
            
            del self.processes[name]
            self.logger.info(f"Cleaned up process {name}")
    
    def cleanup_all_processes(self):
        """Enhanced cleanup of all processes with detailed logging and error handling"""
        if not self.processes:
            self.logger.debug("No processes to clean up")
            return
        
        cleanup_start = time.time()
        process_count = len(self.processes)
        self.logger.info(f"Starting cleanup of {process_count} processes...")
        
        # Update diagnostic data
        self.diagnostic_data["cleanup_operations"] += 1
        
        # Stop monitoring first
        self.stop_monitoring()
        
        # Execute cleanup callbacks
        for callback in self.cleanup_callbacks:
            try:
                callback()
            except Exception as e:
                self.logger.error(f"Error in cleanup callback: {e}")
        
        # Stop all processes with detailed tracking
        cleanup_results = {}
        for name in list(self.processes.keys()):
            cleanup_results[name] = self._cleanup_single_process(name)
        
        # Generate cleanup report
        successful_cleanups = sum(1 for result in cleanup_results.values() if result)
        cleanup_duration = time.time() - cleanup_start
        
        self.logger.info(f"Cleanup completed: {successful_cleanups}/{process_count} processes cleaned up successfully in {cleanup_duration:.2f}s")
        
        # Update diagnostic data
        self.diagnostic_data["last_cleanup"] = {
            "timestamp": time.time(),
            "duration": cleanup_duration,
            "processes_cleaned": process_count,
            "successful_cleanups": successful_cleanups,
            "results": cleanup_results
        }
    
    def _cleanup_single_process(self, name: str) -> bool:
        """Clean up a single process with detailed tracking"""
        try:
            if name not in self.processes:
                return True
            
            proc_info = self.processes[name]
            self.logger.debug(f"Cleaning up process {name} (PID: {proc_info.pid})")
            
            # Try graceful shutdown first
            try:
                if proc_info.process.poll() is None:  # Process is still running
                    if os.name == 'nt':
                        proc_info.process.terminate()
                        try:
                            proc_info.process.wait(timeout=3)
                        except subprocess.TimeoutExpired:
                            proc_info.process.kill()
                            proc_info.process.wait(timeout=2)
                    else:
                        os.killpg(os.getpgid(proc_info.pid), signal.SIGTERM)
                        try:
                            proc_info.process.wait(timeout=3)
                        except subprocess.TimeoutExpired:
                            os.killpg(os.getpgid(proc_info.pid), signal.SIGKILL)
                            proc_info.process.wait()
            except Exception as e:
                self.logger.warning(f"Error during graceful shutdown of {name}: {e}")
            
            # Clean up resources
            try:
                if hasattr(proc_info.process, 'stdout') and proc_info.process.stdout:
                    proc_info.process.stdout.close()
                if hasattr(proc_info.process, 'stderr') and proc_info.process.stderr:
                    proc_info.process.stderr.close()
            except:
                pass
            
            # Remove from tracking
            del self.processes[name]
            self.logger.debug(f"Successfully cleaned up process {name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to clean up process {name}: {e}")
            return False
    
    def get_process_status(self, name: str) -> ProcessStatus:
        """Get current status of a process"""
        if name not in self.processes:
            return ProcessStatus.UNKNOWN
        
        proc_info = self.processes[name]
        
        # Update status based on actual process state
        if proc_info.process.poll() is None:
            proc_info.status = ProcessStatus.RUNNING
        else:
            proc_info.status = ProcessStatus.STOPPED
        
        return proc_info.status
    
    def is_process_running(self, name: str) -> bool:
        """Check if a process is currently running"""
        return self.get_process_status(name) == ProcessStatus.RUNNING
    
    def get_process_health_check(self, name: str) -> Dict[str, Any]:
        """Get comprehensive health check for a process"""
        if name not in self.processes:
            return {
                "healthy": False,
                "checks": {"exists": False},
                "recommendations": ["Process does not exist"]
            }
        
        proc_info = self.processes[name]
        checks = {}
        recommendations = []
        
        # Basic existence check
        checks["exists"] = True
        
        # Running status check
        is_running = proc_info.process.poll() is None
        checks["running"] = is_running
        
        if not is_running:
            recommendations.append("Process is not running")
        
        # Resource usage checks
        if is_running:
            try:
                if proc_info.cpu_percent > 90:
                    checks["cpu_usage"] = False
                    recommendations.append(f"High CPU usage: {proc_info.cpu_percent:.1f}%")
                else:
                    checks["cpu_usage"] = True
                
                if proc_info.memory_mb > 1000:
                    checks["memory_usage"] = False
                    recommendations.append(f"High memory usage: {proc_info.memory_mb:.1f}MB")
                else:
                    checks["memory_usage"] = True
                
                # Heartbeat check
                if proc_info.last_heartbeat:
                    time_since_heartbeat = time.time() - proc_info.last_heartbeat
                    if time_since_heartbeat > 30:  # 30 seconds threshold
                        checks["heartbeat"] = False
                        recommendations.append(f"No heartbeat for {time_since_heartbeat:.1f}s")
                    else:
                        checks["heartbeat"] = True
                
            except Exception as e:
                checks["resource_monitoring"] = False
                recommendations.append(f"Resource monitoring error: {e}")
        
        # Overall health assessment
        healthy = all(checks.values()) and is_running
        
        return {
            "healthy": healthy,
            "checks": checks,
            "recommendations": recommendations,
            "last_updated": time.time()
        }
    
    def get_process_logs_summary(self, name: str) -> Dict[str, Any]:
        """Get summary of process logs"""
        if name not in self.processes:
            return {
                "error": "Process not found",
                "total_lines": 0,
                "log_file": None
            }
        
        proc_info = self.processes[name]
        log_file = proc_info.log_file
        
        try:
            if os.path.exists(log_file):
                with open(log_file, 'r') as f:
                    lines = f.readlines()
                
                return {
                    "total_lines": len(lines),
                    "log_file": log_file,
                    "file_size": os.path.getsize(log_file),
                    "last_modified": os.path.getmtime(log_file),
                    "recent_lines": lines[-10:] if lines else []
                }
            else:
                return {
                    "total_lines": 0,
                    "log_file": log_file,
                    "error": "Log file does not exist"
                }
        except Exception as e:
            return {
                "error": f"Failed to read log file: {e}",
                "log_file": log_file,
                "total_lines": 0
            }
    
    def get_log_content(self, name: str, tail_lines: int = 50) -> List[str]:
        """Get recent log content for a process"""
        if name not in self.processes:
            return []
        
        proc_info = self.processes[name]
        log_file = proc_info.log_file
        
        try:
            if os.path.exists(log_file):
                with open(log_file, 'r') as f:
                    lines = f.readlines()
                return lines[-tail_lines:] if lines else []
            else:
                return []
        except Exception as e:
            self.logger.error(f"Failed to read log content for {name}: {e}")
            return []
    
    def get_diagnostic_summary(self) -> Dict[str, Any]:
        """Get comprehensive diagnostic summary"""
        current_time = time.time()
        uptime = current_time - self.diagnostic_data["startup_time"]
        
        # Calculate success rate
        total_started = self.diagnostic_data["total_processes_started"]
        total_failed = self.diagnostic_data["total_processes_failed"]
        success_rate = (total_started - total_failed) / total_started if total_started > 0 else 1.0
        
        # Get current process count
        running_processes = sum(1 for p in self.processes.values() if p.process.poll() is None)
        
        return {
            "uptime": uptime,
            "total_processes_started": total_started,
            "total_processes_failed": total_failed,
            "success_rate": success_rate,
            "currently_running": running_processes,
            "total_restarts": self.diagnostic_data["total_restarts"],
            "cleanup_operations": self.diagnostic_data["cleanup_operations"],
            "performance_metrics": self.diagnostic_data["performance_metrics"].copy(),
            "resource_warnings_count": len(self.diagnostic_data["resource_warnings"]),
            "error_history_count": len(self.diagnostic_data["error_history"]),
            "monitoring_active": self.monitoring_active
        }
    
    def _get_system_info(self) -> Dict[str, Any]:
        """Get current system information"""
        try:
            import psutil
            memory = psutil.virtual_memory()
            cpu_count = psutil.cpu_count()
            load_avg = psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None
            
            return {
                "cpu_count": cpu_count,
                "memory": {
                    "total": memory.total,
                    "available": memory.available,
                    "percent": memory.percent
                },
                "load_average": load_avg,
                "platform": os.name
            }
        except ImportError:
            return {
                "cpu_count": os.cpu_count(),
                "memory": {"error": "psutil not available"},
                "platform": os.name
            }
    
    def generate_process_report(self) -> Dict[str, Any]:
        """Generate comprehensive process report"""
        return {
            "timestamp": time.time(),
            "diagnostics": self.get_diagnostic_summary(),
            "system_info": self._get_system_info(),
            "processes": {
                name: {
                    "status": info.status.value,
                    "pid": info.pid,
                    "uptime": time.time() - info.start_time,
                    "cpu_percent": info.cpu_percent,
                    "memory_mb": info.memory_mb,
                    "restart_count": info.restart_count,
                    "command": " ".join(info.cmd),
                    "log_file": info.log_file
                }
                for name, info in self.processes.items()
            },
            "resource_warnings": self.diagnostic_data["resource_warnings"][-10:],  # Last 10 warnings
            "recent_errors": self.diagnostic_data["error_history"][-10:]  # Last 10 errors
        }
    
    async def restart_process(self, name: str) -> bool:
        """Restart a process with the same configuration"""
        if name not in self.processes:
            self.logger.error(f"Cannot restart process {name}: not found")
            return False
        
        proc_info = self.processes[name]
        cmd = proc_info.cmd.copy()
        
        self.logger.info(f"Restarting process {name}")
        
        # Stop the current process
        await self.stop_process(name)
        
        # Wait a moment for cleanup
        await asyncio.sleep(0.5)
        
        # Start the process again
        success = await self.start_process(name, cmd)
        
        if success:
            # Update restart count
            if name in self.processes:
                self.processes[name].restart_count += 1
            self.diagnostic_data["total_restarts"] += 1
            self.logger.info(f"Process {name} restarted successfully")
        else:
            self.logger.error(f"Failed to restart process {name}")
        
        return success
    
    def export_diagnostics(self, filename: str):
        """Export diagnostic data to JSON file"""
        try:
            report = self.generate_process_report()
            
            with open(filename, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            
            self.logger.info(f"Diagnostics exported to {filename}")
        except Exception as e:
            self.logger.error(f"Failed to export diagnostics: {e}")
    
    def add_cleanup_callback(self, callback: Callable):
        """Add a callback to be executed during cleanup"""
        self.cleanup_callbacks.append(callback)
    
    def _cleanup_single_process(self, name: str) -> bool:
        """Clean up a single process with detailed error handling"""
        try:
            proc_info = self.processes[name]
            process = proc_info.process
            
            self.logger.debug(f"Cleaning up process {name} (PID: {proc_info.pid})")
            
            if process.poll() is None:  # Process is still running
                # Try graceful termination first
                try:
                    if os.name == 'nt':
                        process.terminate()
                        process.wait(timeout=self.config.process_cleanup_timeout)
                    else:
                        os.killpg(os.getpgid(process.pid), signal.SIGTERM)
                        process.wait(timeout=self.config.process_cleanup_timeout)
                    
                    self.logger.debug(f"Process {name} terminated gracefully")
                    
                except subprocess.TimeoutExpired:
                    # Force kill if graceful termination failed
                    self.logger.warning(f"Process {name} did not terminate gracefully, force killing...")
                    try:
                        if os.name == 'nt':
                            process.kill()
                            process.wait(timeout=2)
                        else:
                            os.killpg(os.getpgid(process.pid), signal.SIGKILL)
                            process.wait(timeout=2)
                        
                        self.logger.debug(f"Process {name} force killed")
                        
                    except Exception as kill_error:
                        self.logger.error(f"Failed to force kill process {name}: {kill_error}")
                        return False
                        
            else:
                self.logger.debug(f"Process {name} was already stopped")
            
            # Clean up process resources
            try:
                if hasattr(process, 'stdout') and process.stdout:
                    process.stdout.close()
                if hasattr(process, 'stderr') and process.stderr:
                    process.stderr.close()
            except Exception as resource_error:
                self.logger.debug(f"Error cleaning up resources for {name}: {resource_error}")
            
            # Remove from processes dict
            del self.processes[name]
            self.logger.debug(f"Process {name} cleanup completed")
            return True
            
        except Exception as e:
            self.logger.error(f"Error cleaning up process {name}: {e}", exc_info=True)
            # Still try to remove from dict to prevent memory leaks
            try:
                del self.processes[name]
            except KeyError:
                pass
            return False
    
    def add_cleanup_callback(self, callback: Callable):
        """Add a callback to be executed during cleanup"""
        self.cleanup_callbacks.append(callback)
    
    def remove_cleanup_callback(self, callback: Callable):
        """Remove a cleanup callback"""
        try:
            self.cleanup_callbacks.remove(callback)
        except ValueError:
            pass
    
    def get_process_status(self, name: str) -> Optional[ProcessStatus]:
        """Get process status"""
        proc_info = self.processes.get(name)
        return proc_info.status if proc_info else None
    
    def get_process_info(self, name: str) -> Optional[ProcessInfo]:
        """Get detailed process information"""
        return self.processes.get(name)
    
    def get_all_process_info(self) -> Dict[str, ProcessInfo]:
        """Get information for all processes"""
        return self.processes.copy()
    
    def get_log_content(self, name: str, tail_lines: Optional[int] = None) -> str:
        """
        Enhanced log content retrieval.
        
        Args:
            name: Process name
            tail_lines: Number of lines from end to return (None for all)
            
        Returns:
            Log content as string
        """
        proc_info = self.processes.get(name)
        if not proc_info:
            return ""
        
        try:
            with open(proc_info.log_file, 'r') as f:
                if tail_lines:
                    lines = f.readlines()
                    return ''.join(lines[-tail_lines:])
                else:
                    return f.read()
        except Exception as e:
            self.logger.error(f"Error reading log file for {name}: {e}")
            return ""
    
    def is_process_running(self, name: str) -> bool:
        """Check if process is running"""
        proc_info = self.processes.get(name)
        if not proc_info:
            return False
        
        return (proc_info.status == ProcessStatus.RUNNING and 
                proc_info.process.poll() is None)
    
    def get_process_uptime(self, name: str) -> Optional[float]:
        """Get process uptime in seconds"""
        proc_info = self.processes.get(name)
        if not proc_info:
            return None
        
        return time.time() - proc_info.start_time
    
    async def restart_process(self, name: str, wait_for_ready: Optional[str] = None) -> bool:
        """
        Restart a process with enhanced error handling and monitoring.
        
        Args:
            name: Process name to restart
            wait_for_ready: String to wait for in logs after restart
            
        Returns:
            True if restart was successful
        """
        if name not in self.processes:
            self.logger.error(f"Cannot restart process {name}: not found")
            return False
        
        proc_info = self.processes[name]
        original_cmd = proc_info.cmd.copy()
        
        self.logger.info(f"Restarting process {name}")
        
        # Update restart count
        proc_info.restart_count += 1
        self.diagnostic_data["total_restarts"] += 1
        
        # Stop the current process
        stop_success = await self.stop_process(name)
        if not stop_success:
            self.logger.warning(f"Failed to cleanly stop {name} before restart")
        
        # Wait a moment for cleanup
        await asyncio.sleep(0.5)
        
        # Start the process again
        return await self.start_process(name, original_cmd, wait_for_ready)
    
    async def ensure_process_running(self, name: str, max_restart_attempts: int = 3) -> bool:
        """
        Ensure a process is running, restarting if necessary.
        
        Args:
            name: Process name
            max_restart_attempts: Maximum number of restart attempts
            
        Returns:
            True if process is running or was successfully restarted
        """
        if name not in self.processes:
            self.logger.error(f"Process {name} not found")
            return False
        
        proc_info = self.processes[name]
        
        # Check if process is already running
        if self.is_process_running(name):
            return True
        
        # Process is not running, attempt to restart
        self.logger.info(f"Process {name} is not running, attempting restart")
        
        for attempt in range(max_restart_attempts):
            self.logger.info(f"Restart attempt {attempt + 1}/{max_restart_attempts} for {name}")
            
            if await self.restart_process(name):
                self.logger.info(f"Successfully restarted {name} on attempt {attempt + 1}")
                return True
            
            # Wait before next attempt
            if attempt < max_restart_attempts - 1:
                await asyncio.sleep(2 ** attempt)  # Exponential backoff
        
        self.logger.error(f"Failed to restart {name} after {max_restart_attempts} attempts")
        self._capture_failure_diagnostics(name, f"restart_failed_after_{max_restart_attempts}_attempts")
        return False
    
    def get_process_health_check(self, name: str) -> Dict[str, Any]:
        """
        Perform a comprehensive health check on a process.
        
        Args:
            name: Process name
            
        Returns:
            Dictionary with health check results
        """
        if name not in self.processes:
            return {"status": "not_found", "healthy": False}
        
        proc_info = self.processes[name]
        current_time = time.time()
        
        health_check = {
            "status": proc_info.status.value,
            "healthy": False,
            "checks": {},
            "recommendations": []
        }
        
        # Check if process is running
        health_check["checks"]["process_running"] = self.is_process_running(name)
        
        # Check resource usage
        health_check["checks"]["cpu_usage"] = {
            "value": proc_info.cpu_percent,
            "healthy": proc_info.cpu_percent < 80,
            "threshold": 80
        }
        
        health_check["checks"]["memory_usage"] = {
            "value": proc_info.memory_mb,
            "healthy": proc_info.memory_mb < 500,
            "threshold": 500
        }
        
        # Check responsiveness
        if proc_info.last_heartbeat:
            time_since_heartbeat = current_time - proc_info.last_heartbeat
            health_check["checks"]["responsiveness"] = {
                "seconds_since_heartbeat": time_since_heartbeat,
                "healthy": time_since_heartbeat < 30,
                "threshold": 30
            }
        
        # Check uptime
        uptime = current_time - proc_info.start_time
        health_check["checks"]["uptime"] = {
            "seconds": uptime,
            "healthy": uptime > 5,  # Process should be stable for at least 5 seconds
            "threshold": 5
        }
        
        # Check restart count
        health_check["checks"]["restart_count"] = {
            "value": proc_info.restart_count,
            "healthy": proc_info.restart_count < 5,
            "threshold": 5
        }
        
        # Overall health assessment
        all_checks_healthy = all(
            check.get("healthy", True) if isinstance(check, dict) else check
            for check in health_check["checks"].values()
        )
        health_check["healthy"] = all_checks_healthy
        
        # Generate recommendations
        if not health_check["checks"]["process_running"]:
            health_check["recommendations"].append("Process is not running - consider restarting")
        
        if not health_check["checks"]["cpu_usage"]["healthy"]:
            health_check["recommendations"].append("High CPU usage detected - investigate process load")
        
        if not health_check["checks"]["memory_usage"]["healthy"]:
            health_check["recommendations"].append("High memory usage detected - check for memory leaks")
        
        if "responsiveness" in health_check["checks"] and not health_check["checks"]["responsiveness"]["healthy"]:
            health_check["recommendations"].append("Process appears unresponsive - consider restarting")
        
        if not health_check["checks"]["restart_count"]["healthy"]:
            health_check["recommendations"].append("High restart count - investigate underlying issues")
        
        return health_check
    
    def generate_process_report(self) -> Dict[str, Any]:
        """Generate comprehensive process report with enhanced diagnostics"""
        current_time = time.time()
        uptime = current_time - self.diagnostic_data["startup_time"]
        
        report = {
            "timestamp": current_time,
            "manager_uptime": uptime,
            "total_processes": len(self.processes),
            "running_processes": sum(1 for p in self.processes.values() 
                                   if p.status == ProcessStatus.RUNNING),
            "stopped_processes": sum(1 for p in self.processes.values() 
                                   if p.status == ProcessStatus.STOPPED),
            "failed_processes": sum(1 for p in self.processes.values() 
                                  if p.status == ProcessStatus.FAILED),
            "processes": {},
            "diagnostics": self.diagnostic_data.copy(),
            "system_info": self._get_system_info()
        }
        
        for name, proc_info in self.processes.items():
            process_uptime = current_time - proc_info.start_time
            report["processes"][name] = {
                "status": proc_info.status.value,
                "pid": proc_info.pid,
                "uptime": process_uptime,
                "cpu_percent": proc_info.cpu_percent,
                "memory_mb": proc_info.memory_mb,
                "restart_count": proc_info.restart_count,
                "command": " ".join(proc_info.cmd),
                "log_file": proc_info.log_file,
                "last_heartbeat": proc_info.last_heartbeat,
                "health_status": self._get_process_health_status(proc_info)
            }
        
        return report
    
    def _get_system_info(self) -> Dict[str, Any]:
        """Get current system information"""
        try:
            import psutil
            return {
                "cpu_count": os.cpu_count(),
                "cpu_percent": psutil.cpu_percent(interval=1),
                "memory": {
                    "total_mb": psutil.virtual_memory().total / 1024 / 1024,
                    "available_mb": psutil.virtual_memory().available / 1024 / 1024,
                    "percent": psutil.virtual_memory().percent
                },
                "disk": {
                    "total_gb": psutil.disk_usage('/').total / 1024 / 1024 / 1024,
                    "free_gb": psutil.disk_usage('/').free / 1024 / 1024 / 1024,
                    "percent": psutil.disk_usage('/').percent
                },
                "load_average": os.getloadavg() if hasattr(os, 'getloadavg') else None
            }
        except Exception as e:
            self.logger.debug(f"Error getting system info: {e}")
            return {"error": str(e)}
    
    def _get_process_health_status(self, proc_info: ProcessInfo) -> str:
        """Determine process health status"""
        if proc_info.status != ProcessStatus.RUNNING:
            return "unhealthy"
        
        current_time = time.time()
        
        # Check if process is responsive (has recent heartbeat)
        if proc_info.last_heartbeat and (current_time - proc_info.last_heartbeat) > 30:
            return "unresponsive"
        
        # Check resource usage
        if proc_info.cpu_percent > 90 or proc_info.memory_mb > 1000:
            return "stressed"
        
        return "healthy"
    
    def get_diagnostic_summary(self) -> Dict[str, Any]:
        """Get a summary of diagnostic information"""
        current_time = time.time()
        uptime = current_time - self.diagnostic_data["startup_time"]
        
        # Calculate success rate
        total_attempts = (self.diagnostic_data["total_processes_started"] + 
                         self.diagnostic_data["total_processes_failed"])
        success_rate = (self.diagnostic_data["total_processes_started"] / total_attempts 
                       if total_attempts > 0 else 0)
        
        return {
            "uptime": uptime,
            "total_processes_started": self.diagnostic_data["total_processes_started"],
            "total_processes_failed": self.diagnostic_data["total_processes_failed"],
            "success_rate": success_rate,
            "total_restarts": self.diagnostic_data["total_restarts"],
            "cleanup_operations": self.diagnostic_data["cleanup_operations"],
            "recent_errors": len([e for e in self.diagnostic_data["error_history"] 
                                if current_time - e["timestamp"] < 3600]),  # Last hour
            "resource_warnings": len(self.diagnostic_data["resource_warnings"]),
            "performance_metrics": self.diagnostic_data["performance_metrics"]
        }
    
    def export_diagnostics(self, filepath: str):
        """Export diagnostic data to a file"""
        try:
            report = self.generate_process_report()
            with open(filepath, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            self.logger.info(f"Diagnostic data exported to {filepath}")
        except Exception as e:
            self.logger.error(f"Failed to export diagnostics: {e}")
    
    def get_process_logs_summary(self, name: str, include_errors_only: bool = False) -> Dict[str, Any]:
        """Get a summary of process logs with error analysis"""
        if name not in self.processes:
            return {"error": "Process not found"}
        
        try:
            log_content = self.get_log_content(name)
            lines = log_content.split('\n')
            
            summary = {
                "total_lines": len(lines),
                "log_file": self.processes[name].log_file,
                "file_size_bytes": os.path.getsize(self.processes[name].log_file),
                "errors": [],
                "warnings": [],
                "recent_activity": []
            }
            
            # Analyze log content
            for i, line in enumerate(lines):
                line_lower = line.lower()
                
                if 'error' in line_lower or 'exception' in line_lower:
                    summary["errors"].append({"line": i + 1, "content": line.strip()})
                elif 'warning' in line_lower or 'warn' in line_lower:
                    summary["warnings"].append({"line": i + 1, "content": line.strip()})
                
                # Get recent activity (last 20 lines)
                if i >= len(lines) - 20:
                    summary["recent_activity"].append(line.strip())
            
            return summary
            
        except Exception as e:
            return {"error": f"Failed to analyze logs: {e}"}
    
    async def cleanup_all(self):
        """Async cleanup method for compatibility with test expectations"""
        self.cleanup_all_processes()