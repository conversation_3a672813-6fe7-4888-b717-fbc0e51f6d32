"""
Edge case testing utilities for wsstun.

This module provides tools for testing edge cases and abnormal scenarios,
including network interruption simulation and Hub-Service reconnection blocking detection.
"""

import asyncio
import logging
import subprocess
import time
import socket
import threading
import psutil
from dataclasses import dataclass
from enum import Enum
from typing import Dict, List, Optional, Any, Callable
import os
import signal


class NetworkCondition(Enum):
    """Network condition simulation types"""
    NORMAL = "normal"
    HIGH_LATENCY = "high_latency"
    PACKET_LOSS = "packet_loss"
    BANDWIDTH_LIMIT = "bandwidth_limit"
    CONNECTION_DROP = "connection_drop"
    PORT_UNAVAILABLE = "port_unavailable"


class ReconnectBlockingType(Enum):
    """Types of reconnection blocking scenarios"""
    SERVER_QUICK_RESTART = "server_quick_restart"
    SERVER_LONG_DOWNTIME = "server_long_downtime"
    NETWORK_STATE_CHANGE = "network_state_change"
    PORT_REUSE_CONFLICT = "port_reuse_conflict"
    MULTIPLE_INSTANCE_COMPETITION = "multiple_instance_competition"


@dataclass
class ReconnectBlockingTestResult:
    """Results from Hub-Service reconnection blocking tests"""
    test_type: ReconnectBlockingType
    blocking_detected: bool
    reconnect_successful: bool
    time_to_reconnect: Optional[float]
    max_reconnect_attempts: int
    error_messages: List[str]
    server_restart_time: float
    hub_service_logs: List[str]
    server_logs: List[str]
    recommendations: List[str]


@dataclass
class InterruptionTestResult:
    """Results from connection interruption tests"""
    interruption_type: str
    connection_recovered: bool
    recovery_time: Optional[float]
    data_loss_detected: bool
    error_handling_correct: bool
    logs: List[str]


@dataclass
class MalformedMessageTestResult:
    """Results from malformed message tests"""
    message_type: str
    server_handled_gracefully: bool
    connection_terminated_properly: bool
    error_logged: bool
    security_issue_detected: bool
    logs: List[str]


class EdgeCaseTester:
    """
    Edge case tester for wsstun abnormal scenarios.
    
    Provides functionality to test connection interruptions, network conditions,
    malformed messages, and specifically Hub-Service reconnection blocking scenarios.
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.active_processes: Dict[str, subprocess.Popen] = {}
        self.network_simulators: List[Any] = []
        self.test_results: List[Any] = []
    
    async def test_hub_service_reconnect_blocking(self, 
                                                wsstun_binary: str,
                                                server_host: str = "127.0.0.1",
                                                server_port: int = 8060) -> List[ReconnectBlockingTestResult]:
        """
        Test Hub-Service reconnection blocking scenarios.
        
        This is the core functionality for detecting reconnection blocking issues
        when the server restarts and Hub-Service attempts to reconnect.
        
        Args:
            wsstun_binary: Path to wsstun binary
            server_host: Server host address
            server_port: Server port
            
        Returns:
            List of test results for different blocking scenarios
        """
        self.logger.info("Starting Hub-Service reconnection blocking tests")
        results = []
        
        # Test different reconnection blocking scenarios
        blocking_scenarios = [
            ReconnectBlockingType.SERVER_QUICK_RESTART,
            ReconnectBlockingType.SERVER_LONG_DOWNTIME,
            ReconnectBlockingType.NETWORK_STATE_CHANGE,
            ReconnectBlockingType.PORT_REUSE_CONFLICT,
            ReconnectBlockingType.MULTIPLE_INSTANCE_COMPETITION
        ]
        
        for scenario in blocking_scenarios:
            self.logger.info(f"Testing reconnection blocking scenario: {scenario.value}")
            result = await self._test_specific_blocking_scenario(
                scenario, wsstun_binary, server_host, server_port
            )
            results.append(result)
            
            # Clean up between tests
            await self._cleanup_processes()
            await asyncio.sleep(2)
        
        return results
    
    async def _test_specific_blocking_scenario(self, 
                                             scenario: ReconnectBlockingType,
                                             wsstun_binary: str,
                                             server_host: str,
                                             server_port: int) -> ReconnectBlockingTestResult:
        """Test a specific reconnection blocking scenario"""
        
        result = ReconnectBlockingTestResult(
            test_type=scenario,
            blocking_detected=False,
            reconnect_successful=False,
            time_to_reconnect=None,
            max_reconnect_attempts=0,
            error_messages=[],
            server_restart_time=0.0,
            hub_service_logs=[],
            server_logs=[],
            recommendations=[]
        )
        
        try:
            if scenario == ReconnectBlockingType.SERVER_QUICK_RESTART:
                await self._test_server_quick_restart(result, wsstun_binary, server_host, server_port)
            elif scenario == ReconnectBlockingType.SERVER_LONG_DOWNTIME:
                await self._test_server_long_downtime(result, wsstun_binary, server_host, server_port)
            elif scenario == ReconnectBlockingType.NETWORK_STATE_CHANGE:
                await self._test_network_state_change(result, wsstun_binary, server_host, server_port)
            elif scenario == ReconnectBlockingType.PORT_REUSE_CONFLICT:
                await self._test_port_reuse_conflict(result, wsstun_binary, server_host, server_port)
            elif scenario == ReconnectBlockingType.MULTIPLE_INSTANCE_COMPETITION:
                await self._test_multiple_instance_competition(result, wsstun_binary, server_host, server_port)
                
        except Exception as e:
            result.error_messages.append(f"Test execution error: {str(e)}")
            self.logger.error(f"Error in blocking scenario {scenario.value}: {e}")
        
        return result
    
    async def _test_server_quick_restart(self, result: ReconnectBlockingTestResult,
                                       wsstun_binary: str, server_host: str, server_port: int):
        """Test server quick restart scenario - Enhanced implementation"""
        self.logger.info("Testing server quick restart scenario")
        
        # 1. Start server with enhanced monitoring
        server_cmd = [
            wsstun_binary, "server",
            "--listen", f"{server_host}:{server_port}",
            "--log-level", "debug"
        ]
        
        server_process = await self._start_process_with_logs("server", server_cmd)
        if not server_process:
            result.error_messages.append("Failed to start server")
            return
        
        # Wait for server to be fully ready
        await asyncio.sleep(2)
        
        # Verify server is listening
        if not await self._verify_server_listening(server_host, server_port):
            result.error_messages.append("Server not listening on expected port")
            return
        
        # 2. Start hub-service with connection verification
        hub_cmd = [
            wsstun_binary, "hub-service",
            "--server", f"ws://{server_host}:{server_port}",
            "--service-id", "test-quick-restart-service",
            "--heartbeat-interval", "3",  # Shorter interval for faster detection
            "--log-level", "debug"
        ]
        
        hub_process = await self._start_process_with_logs("hub_service", hub_cmd)
        if not hub_process:
            result.error_messages.append("Failed to start hub-service")
            return
        
        # Wait for initial connection and verify
        await asyncio.sleep(4)
        initial_logs = await self._get_process_logs("hub_service")
        if not any("connected" in log.lower() or "registered" in log.lower() for log in initial_logs):
            result.error_messages.append("Hub-service failed to establish initial connection")
            return
        
        self.logger.info("Initial hub-service connection established")
        
        # 3. Perform quick server restart with precise timing
        restart_start = time.time()
        self.logger.info("Stopping server for quick restart")
        
        # Gracefully stop server
        await self._stop_process("server")
        
        # Very short downtime (simulate quick restart - critical timing)
        quick_restart_delay = 0.3  # 300ms - very quick restart
        await asyncio.sleep(quick_restart_delay)
        
        self.logger.info("Restarting server immediately")
        server_process = await self._start_process_with_logs("server", server_cmd)
        restart_end = time.time()
        result.server_restart_time = restart_end - restart_start
        
        if not server_process:
            result.error_messages.append("Failed to restart server")
            return
        
        # Wait for server to be ready again
        await asyncio.sleep(1)
        if not await self._verify_server_listening(server_host, server_port):
            result.error_messages.append("Server not listening after restart")
            return
        
        self.logger.info(f"Server restarted in {result.server_restart_time:.3f}s")
        
        # 4. Monitor hub-service reconnection with detailed analysis
        reconnect_start = time.time()
        reconnect_timeout = 25  # 25 seconds timeout
        reconnect_attempts = 0
        last_log_count = 0
        
        while time.time() - reconnect_start < reconnect_timeout:
            # Get current logs
            hub_logs = await self._get_process_logs("hub_service")
            result.hub_service_logs = hub_logs
            
            # Check for new log entries
            new_logs = hub_logs[last_log_count:]
            last_log_count = len(hub_logs)
            
            # Look for reconnection success indicators
            success_indicators = ["connected", "registered", "established", "ready"]
            if any(indicator in log.lower() for log in new_logs for indicator in success_indicators):
                result.reconnect_successful = True
                result.time_to_reconnect = time.time() - reconnect_start
                self.logger.info(f"Hub-service reconnected successfully in {result.time_to_reconnect:.2f}s")
                break
            
            # Count reconnection attempts
            attempt_indicators = ["connecting", "attempting", "retry", "reconnect"]
            for log in new_logs:
                if any(indicator in log.lower() for indicator in attempt_indicators):
                    reconnect_attempts += 1
            
            # Look for blocking indicators
            blocking_indicators = ["timeout", "failed to connect", "connection refused", "unreachable"]
            blocking_logs = [log for log in new_logs if any(indicator in log.lower() for indicator in blocking_indicators)]
            
            if blocking_logs:
                self.logger.warning(f"Potential blocking detected: {blocking_logs[-1]}")
            
            await asyncio.sleep(0.5)  # Check every 500ms for responsiveness
        
        result.max_reconnect_attempts = reconnect_attempts
        
        # 5. Analyze reconnection behavior
        if not result.reconnect_successful:
            result.blocking_detected = True
            result.recommendations.extend([
                "Hub-Service failed to reconnect after quick server restart",
                f"Server restart time was {result.server_restart_time:.3f}s - very quick",
                "Consider implementing exponential backoff for reconnection",
                "Check if WebSocket connection state is properly handled during quick restarts",
                f"Hub-service made {reconnect_attempts} reconnection attempts"
            ])
            
            # Additional analysis for quick restart scenario
            if result.server_restart_time < 1.0:
                result.recommendations.append("Quick restart may cause race conditions in connection handling")
            
            if reconnect_attempts == 0:
                result.recommendations.append("Hub-service may not be detecting server disconnection")
            elif reconnect_attempts > 10:
                result.recommendations.append("Excessive reconnection attempts detected - may indicate retry loop")
        
        # Collect final logs with timestamps
        result.server_logs = await self._get_process_logs("server")
        
        # Log summary
        self.logger.info(f"Quick restart test summary:")
        self.logger.info(f"  - Server restart time: {result.server_restart_time:.3f}s")
        self.logger.info(f"  - Reconnection attempts: {reconnect_attempts}")
        self.logger.info(f"  - Reconnection successful: {result.reconnect_successful}")
        if result.time_to_reconnect:
            self.logger.info(f"  - Time to reconnect: {result.time_to_reconnect:.2f}s")
    
    async def _test_server_long_downtime(self, result: ReconnectBlockingTestResult,
                                       wsstun_binary: str, server_host: str, server_port: int):
        """Test server long downtime scenario - Enhanced implementation"""
        self.logger.info("Testing server long downtime scenario")
        
        # Server command with enhanced logging
        server_cmd = [
            wsstun_binary, "server",
            "--listen", f"{server_host}:{server_port}",
            "--log-level", "debug"
        ]
        
        # 1. Start server and verify it's ready
        server_process = await self._start_process_with_logs("server", server_cmd)
        if not server_process:
            result.error_messages.append("Failed to start server")
            return
        
        await asyncio.sleep(2)
        
        if not await self._verify_server_listening(server_host, server_port):
            result.error_messages.append("Server not listening on expected port")
            return
        
        # 2. Start hub-service with connection verification
        hub_cmd = [
            wsstun_binary, "hub-service",
            "--server", f"ws://{server_host}:{server_port}",
            "--service-id", "test-long-downtime-service",
            "--heartbeat-interval", "5",
            "--log-level", "debug"
        ]
        
        hub_process = await self._start_process_with_logs("hub_service", hub_cmd)
        if not hub_process:
            result.error_messages.append("Failed to start hub-service")
            return
        
        # Wait for initial connection and verify
        await asyncio.sleep(4)
        initial_logs = await self._get_process_logs("hub_service")
        if not any("connected" in log.lower() or "registered" in log.lower() for log in initial_logs):
            result.error_messages.append("Hub-service failed to establish initial connection")
            return
        
        self.logger.info("Initial hub-service connection established")
        
        # 3. Long server downtime simulation
        restart_start = time.time()
        self.logger.info("Stopping server for extended downtime")
        
        # Stop server
        await self._stop_process("server")
        
        # Extended downtime (simulate maintenance, network issues, etc.)
        long_downtime_duration = 20.0  # 20 seconds - significant downtime
        self.logger.info(f"Simulating {long_downtime_duration}s server downtime...")
        
        # Monitor hub-service behavior during downtime
        downtime_start = time.time()
        downtime_logs = []
        reconnect_attempts_during_downtime = 0
        
        while time.time() - downtime_start < long_downtime_duration:
            # Check hub-service logs during downtime
            current_logs = await self._get_process_logs("hub_service")
            new_logs = current_logs[len(downtime_logs):]
            downtime_logs.extend(new_logs)
            
            # Count reconnection attempts during downtime
            for log in new_logs:
                if any(indicator in log.lower() for indicator in ["connecting", "attempting", "retry"]):
                    reconnect_attempts_during_downtime += 1
            
            await asyncio.sleep(1)
        
        self.logger.info(f"Hub-service made {reconnect_attempts_during_downtime} reconnection attempts during downtime")
        
        # 4. Restart server after long downtime
        self.logger.info("Restarting server after long downtime")
        server_process = await self._start_process_with_logs("server", server_cmd)
        restart_end = time.time()
        result.server_restart_time = restart_end - restart_start
        
        if not server_process:
            result.error_messages.append("Failed to restart server after long downtime")
            return
        
        # Wait for server to be ready
        await asyncio.sleep(2)
        if not await self._verify_server_listening(server_host, server_port):
            result.error_messages.append("Server not listening after restart")
            return
        
        self.logger.info(f"Server restarted after {result.server_restart_time:.1f}s total downtime")
        
        # 5. Monitor hub-service reconnection after long downtime
        reconnect_start = time.time()
        reconnect_timeout = 45  # 45 seconds timeout for long downtime scenario
        reconnect_attempts_after_restart = 0
        last_log_count = len(await self._get_process_logs("hub_service"))
        
        while time.time() - reconnect_start < reconnect_timeout:
            # Get current logs
            hub_logs = await self._get_process_logs("hub_service")
            result.hub_service_logs = hub_logs
            
            # Check for new log entries since restart
            new_logs = hub_logs[last_log_count:]
            last_log_count = len(hub_logs)
            
            # Look for reconnection success indicators
            success_indicators = ["connected", "registered", "established", "ready"]
            if any(indicator in log.lower() for log in new_logs for indicator in success_indicators):
                result.reconnect_successful = True
                result.time_to_reconnect = time.time() - reconnect_start
                self.logger.info(f"Hub-service reconnected successfully in {result.time_to_reconnect:.2f}s after restart")
                break
            
            # Count reconnection attempts after restart
            attempt_indicators = ["connecting", "attempting", "retry", "reconnect"]
            for log in new_logs:
                if any(indicator in log.lower() for indicator in attempt_indicators):
                    reconnect_attempts_after_restart += 1
            
            # Look for persistent blocking indicators
            blocking_indicators = ["timeout", "failed to connect", "connection refused", "unreachable", "giving up"]
            blocking_logs = [log for log in new_logs if any(indicator in log.lower() for indicator in blocking_indicators)]
            
            if blocking_logs:
                self.logger.warning(f"Potential blocking after long downtime: {blocking_logs[-1]}")
            
            await asyncio.sleep(1)  # Check every second
        
        result.max_reconnect_attempts = reconnect_attempts_during_downtime + reconnect_attempts_after_restart
        
        # 6. Analyze long downtime reconnection behavior
        if not result.reconnect_successful:
            result.blocking_detected = True
            result.recommendations.extend([
                "Hub-Service failed to reconnect after long server downtime",
                f"Server was down for {result.server_restart_time:.1f}s",
                f"Hub-service made {reconnect_attempts_during_downtime} attempts during downtime",
                f"Hub-service made {reconnect_attempts_after_restart} attempts after restart",
                "Check reconnection timeout and retry logic for extended outages",
                "Consider implementing connection state persistence across long outages"
            ])
            
            # Specific analysis for long downtime
            if reconnect_attempts_during_downtime == 0:
                result.recommendations.append("Hub-service may have stopped trying to reconnect during long downtime")
            elif reconnect_attempts_during_downtime > 50:
                result.recommendations.append("Excessive reconnection attempts during downtime - consider backoff strategy")
            
            if reconnect_attempts_after_restart == 0:
                result.recommendations.append("Hub-service did not attempt to reconnect after server came back online")
            
            # Check for timeout issues
            if result.server_restart_time > 30:
                result.recommendations.append("Very long downtime may have exceeded hub-service timeout limits")
        else:
            # Successful reconnection analysis
            if result.time_to_reconnect > 30:
                result.recommendations.append(f"Reconnection took {result.time_to_reconnect:.1f}s - consider optimizing reconnection detection")
        
        # Collect final logs
        result.server_logs = await self._get_process_logs("server")
        
        # Log comprehensive summary
        self.logger.info(f"Long downtime test summary:")
        self.logger.info(f"  - Total server downtime: {result.server_restart_time:.1f}s")
        self.logger.info(f"  - Reconnection attempts during downtime: {reconnect_attempts_during_downtime}")
        self.logger.info(f"  - Reconnection attempts after restart: {reconnect_attempts_after_restart}")
        self.logger.info(f"  - Reconnection successful: {result.reconnect_successful}")
        if result.time_to_reconnect:
            self.logger.info(f"  - Time to reconnect after restart: {result.time_to_reconnect:.2f}s")
    
    async def _test_network_state_change(self, result: ReconnectBlockingTestResult,
                                       wsstun_binary: str, server_host: str, server_port: int):
        """Test network state change during server restart - Enhanced implementation"""
        self.logger.info("Testing network state change scenario")
        
        # This test simulates various network state changes during server restart
        original_port = server_port
        new_port = server_port + 1
        
        # 1. Start server with original configuration
        server_cmd = [
            wsstun_binary, "server",
            "--listen", f"{server_host}:{original_port}",
            "--log-level", "debug"
        ]
        
        server_process = await self._start_process_with_logs("server", server_cmd)
        if not server_process:
            result.error_messages.append("Failed to start server")
            return
        
        await asyncio.sleep(2)
        
        if not await self._verify_server_listening(server_host, original_port):
            result.error_messages.append("Server not listening on original port")
            return
        
        # 2. Start hub-service connected to original port
        hub_cmd = [
            wsstun_binary, "hub-service",
            "--server", f"ws://{server_host}:{original_port}",
            "--service-id", "test-network-change-service",
            "--heartbeat-interval", "4",
            "--log-level", "debug"
        ]
        
        hub_process = await self._start_process_with_logs("hub_service", hub_cmd)
        if not hub_process:
            result.error_messages.append("Failed to start hub-service")
            return
        
        # Verify initial connection
        await asyncio.sleep(4)
        initial_logs = await self._get_process_logs("hub_service")
        if not any("connected" in log.lower() or "registered" in log.lower() for log in initial_logs):
            result.error_messages.append("Hub-service failed to establish initial connection")
            return
        
        self.logger.info(f"Initial connection established on port {original_port}")
        
        # 3. Simulate network state change scenarios
        await self._test_port_change_scenario(result, wsstun_binary, server_host, original_port, new_port)
        await asyncio.sleep(2)
        
        await self._test_interface_binding_change(result, wsstun_binary, server_host, original_port)
        await asyncio.sleep(2)
        
        await self._test_network_interruption_during_restart(result, wsstun_binary, server_host, original_port)
    
    async def _test_port_change_scenario(self, result: ReconnectBlockingTestResult,
                                       wsstun_binary: str, server_host: str, 
                                       original_port: int, new_port: int):
        """Test server port change scenario"""
        self.logger.info("Testing port change scenario")
        
        # Stop server
        restart_start = time.time()
        await self._stop_process("server")
        
        # Brief pause to simulate network state change
        await asyncio.sleep(1)
        
        # Restart server on different port (simulating network reconfiguration)
        new_server_cmd = [
            wsstun_binary, "server",
            "--listen", f"{server_host}:{new_port}",
            "--log-level", "debug"
        ]
        
        server_process = await self._start_process_with_logs("server", new_server_cmd)
        restart_end = time.time()
        result.server_restart_time = restart_end - restart_start
        
        if not server_process:
            result.error_messages.append("Failed to restart server on new port")
            return
        
        # Verify server is listening on new port
        if not await self._verify_server_listening(server_host, new_port):
            result.error_messages.append("Server not listening on new port")
            return
        
        self.logger.info(f"Server restarted on new port {new_port}")
        
        # Monitor hub-service behavior - it should fail to reconnect to original port
        reconnect_start = time.time()
        reconnect_timeout = 20
        connection_failures = 0
        
        while time.time() - reconnect_start < reconnect_timeout:
            hub_logs = await self._get_process_logs("hub_service")
            result.hub_service_logs = hub_logs
            
            # Look for connection failures due to port change
            recent_logs = hub_logs[-3:]
            failure_indicators = ["connection refused", "failed to connect", "unreachable", "timeout"]
            
            for log in recent_logs:
                if any(indicator in log.lower() for indicator in failure_indicators):
                    connection_failures += 1
                    self.logger.info(f"Connection failure detected: {log}")
            
            # Check if hub-service is persistently trying the wrong port
            if connection_failures >= 3:
                result.blocking_detected = True
                result.recommendations.extend([
                    "Hub-Service cannot handle server port changes",
                    f"Server moved from port {original_port} to {new_port}",
                    "Hub-service continues trying original port after network change",
                    "Consider implementing service discovery or configuration update mechanism",
                    "Network state changes require manual reconfiguration"
                ])
                break
            
            await asyncio.sleep(1)
        
        result.max_reconnect_attempts += connection_failures
    
    async def _test_interface_binding_change(self, result: ReconnectBlockingTestResult,
                                           wsstun_binary: str, server_host: str, port: int):
        """Test server interface binding change scenario"""
        self.logger.info("Testing interface binding change scenario")
        
        # Stop current server
        await self._stop_process("server")
        await asyncio.sleep(1)
        
        # Restart server with different binding (simulate interface change)
        # We'll use 0.0.0.0 instead of 127.0.0.1 to simulate interface change
        interface_change_cmd = [
            wsstun_binary, "server",
            "--listen", f"0.0.0.0:{port}",
            "--log-level", "debug"
        ]
        
        server_process = await self._start_process_with_logs("server", interface_change_cmd)
        if not server_process:
            result.error_messages.append("Failed to restart server with interface change")
            return
        
        await asyncio.sleep(2)
        
        # Verify server is listening on new interface
        if not await self._verify_server_listening("127.0.0.1", port):
            result.error_messages.append("Server not accessible after interface change")
            return
        
        self.logger.info("Server restarted with interface binding change")
        
        # Monitor hub-service reconnection - should still work since we're using localhost
        reconnect_start = time.time()
        reconnect_timeout = 15
        
        while time.time() - reconnect_start < reconnect_timeout:
            hub_logs = await self._get_process_logs("hub_service")
            
            # Look for successful reconnection
            recent_logs = hub_logs[-5:]
            if any("connected" in log.lower() or "registered" in log.lower() for log in recent_logs):
                self.logger.info("Hub-service successfully reconnected after interface change")
                result.reconnect_successful = True
                result.time_to_reconnect = time.time() - reconnect_start
                break
            
            await asyncio.sleep(1)
        
        if not result.reconnect_successful:
            result.recommendations.append("Hub-service failed to reconnect after server interface binding change")
    
    async def _test_network_interruption_during_restart(self, result: ReconnectBlockingTestResult,
                                                      wsstun_binary: str, server_host: str, port: int):
        """Test network interruption during server restart"""
        self.logger.info("Testing network interruption during restart scenario")
        
        # Stop server
        await self._stop_process("server")
        
        # Simulate network interruption by creating a temporary port conflict
        conflict_socket = None
        try:
            conflict_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            conflict_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            conflict_socket.bind((server_host, port))
            conflict_socket.listen(1)
            
            self.logger.info(f"Created temporary port conflict on {server_host}:{port}")
            
            # Try to restart server while port is blocked
            server_cmd = [
                wsstun_binary, "server",
                "--listen", f"{server_host}:{port}",
                "--log-level", "debug"
            ]
            
            # This should fail due to port conflict
            server_process = await self._start_process_with_logs("server", server_cmd)
            await asyncio.sleep(2)
            
            # Release the port conflict
            conflict_socket.close()
            conflict_socket = None
            
            self.logger.info("Released port conflict")
            await asyncio.sleep(1)
            
            # Now restart server properly
            server_process = await self._start_process_with_logs("server", server_cmd)
            if not server_process:
                result.error_messages.append("Failed to restart server after network interruption")
                return
            
            await asyncio.sleep(2)
            
            # Monitor hub-service behavior during this complex scenario
            reconnect_start = time.time()
            reconnect_timeout = 25
            
            while time.time() - reconnect_start < reconnect_timeout:
                hub_logs = await self._get_process_logs("hub_service")
                result.hub_service_logs = hub_logs
                
                # Look for successful reconnection after network interruption
                recent_logs = hub_logs[-5:]
                if any("connected" in log.lower() or "registered" in log.lower() for log in recent_logs):
                    self.logger.info("Hub-service reconnected after network interruption scenario")
                    if not result.reconnect_successful:  # Don't overwrite previous success
                        result.reconnect_successful = True
                        result.time_to_reconnect = time.time() - reconnect_start
                    break
                
                await asyncio.sleep(1)
            
            if not result.reconnect_successful:
                result.blocking_detected = True
                result.recommendations.extend([
                    "Hub-service failed to reconnect after network interruption during restart",
                    "Complex network state changes may cause persistent connection issues",
                    "Consider implementing more robust network error handling"
                ])
        
        except Exception as e:
            result.error_messages.append(f"Network interruption test error: {str(e)}")
            self.logger.error(f"Network interruption test failed: {e}")
        
        finally:
            if conflict_socket:
                try:
                    conflict_socket.close()
                except:
                    pass
        
        # Collect final logs
        result.server_logs = await self._get_process_logs("server")
        
        # Log network state change test summary
        self.logger.info("Network state change test summary:")
        self.logger.info(f"  - Port change scenario completed")
        self.logger.info(f"  - Interface binding change scenario completed")
        self.logger.info(f"  - Network interruption scenario completed")
        self.logger.info(f"  - Overall reconnection successful: {result.reconnect_successful}")
        self.logger.info(f"  - Blocking detected: {result.blocking_detected}")
    
    async def _test_port_reuse_conflict(self, result: ReconnectBlockingTestResult,
                                      wsstun_binary: str, server_host: str, server_port: int):
        """Test port reuse conflict scenario"""
        self.logger.info("Testing port reuse conflict scenario")
        
        # Start server
        server_cmd = [
            wsstun_binary, "server",
            "--listen", f"{server_host}:{server_port}",
            "--log-level", "debug"
        ]
        
        server_process = await self._start_process_with_logs("server", server_cmd)
        await asyncio.sleep(2)
        
        # Start hub-service
        hub_cmd = [
            wsstun_binary, "hub-service",
            "--server", f"ws://{server_host}:{server_port}",
            "--service-id", "test-port-conflict-service",
            "--heartbeat-interval", "5",
            "--log-level", "debug"
        ]
        
        hub_process = await self._start_process_with_logs("hub_service", hub_cmd)
        await asyncio.sleep(3)
        
        # Stop server
        restart_start = time.time()
        await self._stop_process("server")
        
        # Create port conflict by binding to the port with a dummy socket
        conflict_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        conflict_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        
        try:
            conflict_socket.bind((server_host, server_port))
            conflict_socket.listen(1)
            self.logger.info(f"Created port conflict on {server_host}:{server_port}")
            
            await asyncio.sleep(2)
            
            # Try to restart server (should fail due to port conflict)
            try:
                server_process = await self._start_process_with_logs("server", server_cmd)
                await asyncio.sleep(1)
                
                # Check if server actually started (it shouldn't)
                if server_process and server_process.poll() is not None:
                    result.blocking_detected = True
                    result.recommendations.append("Server failed to start due to port conflict")
                
            finally:
                conflict_socket.close()
            
            # Now restart server properly
            await asyncio.sleep(1)
            server_process = await self._start_process_with_logs("server", server_cmd)
            restart_end = time.time()
            result.server_restart_time = restart_end - restart_start
            
        except Exception as e:
            result.error_messages.append(f"Port conflict test error: {str(e)}")
        finally:
            try:
                conflict_socket.close()
            except:
                pass
        
        # Monitor hub-service reconnection
        reconnect_start = time.time()
        reconnect_timeout = 30
        
        while time.time() - reconnect_start < reconnect_timeout:
            hub_logs = await self._get_process_logs("hub_service")
            result.hub_service_logs = hub_logs
            
            if any("connected" in log.lower() or "registered" in log.lower() for log in hub_logs[-10:]):
                result.reconnect_successful = True
                result.time_to_reconnect = time.time() - reconnect_start
                break
            
            await asyncio.sleep(1)
        
        result.server_logs = await self._get_process_logs("server")
    
    async def _test_multiple_instance_competition(self, result: ReconnectBlockingTestResult,
                                                wsstun_binary: str, server_host: str, server_port: int):
        """Test multiple hub-service instances competing for reconnection - Enhanced implementation"""
        self.logger.info("Testing multiple instance competition scenario")
        
        # Configuration for multiple instances
        instance_count = 4  # Test with 4 instances for better competition detection
        
        # 1. Start server
        server_cmd = [
            wsstun_binary, "server",
            "--listen", f"{server_host}:{server_port}",
            "--log-level", "debug"
        ]
        
        server_process = await self._start_process_with_logs("server", server_cmd)
        if not server_process:
            result.error_messages.append("Failed to start server")
            return
        
        await asyncio.sleep(2)
        
        if not await self._verify_server_listening(server_host, server_port):
            result.error_messages.append("Server not listening")
            return
        
        # 2. Start multiple hub-service instances with different configurations
        hub_instances = []
        initial_connections = {}
        
        for i in range(instance_count):
            hub_cmd = [
                wsstun_binary, "hub-service",
                "--server", f"ws://{server_host}:{server_port}",
                "--service-id", f"test-competition-service-{i}",
                "--heartbeat-interval", str(3 + i),  # Vary heartbeat intervals
                "--log-level", "debug"
            ]
            
            hub_name = f"hub_service_{i}"
            hub_process = await self._start_process_with_logs(hub_name, hub_cmd)
            
            if hub_process:
                hub_instances.append(hub_name)
                initial_connections[hub_name] = False
                self.logger.info(f"Started hub-service instance {i}")
            else:
                result.error_messages.append(f"Failed to start hub-service instance {i}")
        
        if len(hub_instances) < 2:
            result.error_messages.append("Insufficient hub-service instances for competition test")
            return
        
        # 3. Verify initial connections for all instances
        await asyncio.sleep(5)  # Allow time for all instances to connect
        
        for hub_name in hub_instances:
            logs = await self._get_process_logs(hub_name)
            if any("connected" in log.lower() or "registered" in log.lower() for log in logs):
                initial_connections[hub_name] = True
                self.logger.info(f"{hub_name} established initial connection")
        
        connected_instances = sum(initial_connections.values())
        self.logger.info(f"{connected_instances}/{len(hub_instances)} instances initially connected")
        
        if connected_instances == 0:
            result.error_messages.append("No hub-service instances established initial connections")
            return
        
        # 4. Restart server to trigger competition
        restart_start = time.time()
        self.logger.info("Stopping server to trigger reconnection competition")
        await self._stop_process("server")
        
        # Brief downtime to ensure all instances detect disconnection
        await asyncio.sleep(3)
        
        self.logger.info("Restarting server")
        server_process = await self._start_process_with_logs("server", server_cmd)
        restart_end = time.time()
        result.server_restart_time = restart_end - restart_start
        
        if not server_process:
            result.error_messages.append("Failed to restart server")
            return
        
        await asyncio.sleep(1)
        
        if not await self._verify_server_listening(server_host, server_port):
            result.error_messages.append("Server not listening after restart")
            return
        
        # 5. Monitor competition and reconnection behavior
        reconnect_start = time.time()
        reconnect_timeout = 40  # Longer timeout for multiple instances
        
        # Track detailed reconnection behavior
        instance_status = {name: {
            'reconnected': False,
            'reconnect_time': None,
            'attempts': 0,
            'errors': [],
            'last_activity': None
        } for name in hub_instances}
        
        competition_events = []
        
        while time.time() - reconnect_start < reconnect_timeout:
            current_time = time.time()
            
            # Check each instance
            for hub_name in hub_instances:
                if instance_status[hub_name]['reconnected']:
                    continue  # Already reconnected
                
                hub_logs = await self._get_process_logs(hub_name)
                
                # Look for new log entries since last check
                recent_logs = hub_logs[-10:]  # Check last 10 log entries
                
                # Check for reconnection success
                success_indicators = ["connected", "registered", "established"]
                for log in recent_logs:
                    if any(indicator in log.lower() for indicator in success_indicators):
                        if not instance_status[hub_name]['reconnected']:
                            instance_status[hub_name]['reconnected'] = True
                            instance_status[hub_name]['reconnect_time'] = current_time - reconnect_start
                            self.logger.info(f"{hub_name} reconnected in {instance_status[hub_name]['reconnect_time']:.2f}s")
                            
                            competition_events.append({
                                'time': current_time - reconnect_start,
                                'event': 'reconnection_success',
                                'instance': hub_name
                            })
                        break
                
                # Count reconnection attempts
                attempt_indicators = ["connecting", "attempting", "retry"]
                for log in recent_logs:
                    if any(indicator in log.lower() for indicator in attempt_indicators):
                        instance_status[hub_name]['attempts'] += 1
                        instance_status[hub_name]['last_activity'] = current_time - reconnect_start
                
                # Track errors and competition issues
                error_indicators = ["failed", "timeout", "refused", "conflict", "busy"]
                for log in recent_logs:
                    if any(indicator in log.lower() for indicator in error_indicators):
                        instance_status[hub_name]['errors'].append(log)
                        
                        # Detect potential competition conflicts
                        if any(comp_indicator in log.lower() for comp_indicator in ["conflict", "busy", "already"]):
                            competition_events.append({
                                'time': current_time - reconnect_start,
                                'event': 'competition_conflict',
                                'instance': hub_name,
                                'details': log
                            })
            
            # Check if all instances have reconnected
            reconnected_count = sum(1 for status in instance_status.values() if status['reconnected'])
            if reconnected_count == len(hub_instances):
                result.reconnect_successful = True
                result.time_to_reconnect = time.time() - reconnect_start
                self.logger.info(f"All {len(hub_instances)} instances reconnected successfully")
                break
            
            await asyncio.sleep(0.5)  # Check every 500ms for detailed monitoring
        
        # 6. Analyze competition results
        reconnected_count = sum(1 for status in instance_status.values() if status['reconnected'])
        total_attempts = sum(status['attempts'] for status in instance_status.values())
        total_errors = sum(len(status['errors']) for status in instance_status.values())
        
        result.max_reconnect_attempts = total_attempts
        
        # Determine if blocking occurred
        if reconnected_count < len(hub_instances):
            result.blocking_detected = True
            failed_instances = len(hub_instances) - reconnected_count
            
            result.recommendations.extend([
                f"Only {reconnected_count}/{len(hub_instances)} instances reconnected successfully",
                f"{failed_instances} instances failed to reconnect",
                f"Total reconnection attempts across all instances: {total_attempts}",
                f"Total errors detected: {total_errors}",
                "Multiple instances may be interfering with each other during reconnection"
            ])
            
            # Analyze specific competition issues
            competition_conflicts = [e for e in competition_events if e['event'] == 'competition_conflict']
            if competition_conflicts:
                result.recommendations.append(f"Detected {len(competition_conflicts)} competition conflicts")
                result.recommendations.append("Consider implementing connection coordination or backoff strategies")
            
            # Check for timing issues
            reconnect_times = [status['reconnect_time'] for status in instance_status.values() if status['reconnect_time']]
            if reconnect_times:
                avg_reconnect_time = sum(reconnect_times) / len(reconnect_times)
                max_reconnect_time = max(reconnect_times)
                result.recommendations.append(f"Average reconnection time: {avg_reconnect_time:.2f}s")
                result.recommendations.append(f"Maximum reconnection time: {max_reconnect_time:.2f}s")
                
                if max_reconnect_time > 20:
                    result.recommendations.append("Some instances took very long to reconnect - possible blocking")
            
            # Analyze failed instances
            failed_instances_details = []
            for hub_name, status in instance_status.items():
                if not status['reconnected']:
                    failed_instances_details.append({
                        'instance': hub_name,
                        'attempts': status['attempts'],
                        'errors': len(status['errors']),
                        'last_activity': status['last_activity']
                    })
            
            if failed_instances_details:
                result.recommendations.append("Failed instances analysis:")
                for details in failed_instances_details:
                    result.recommendations.append(
                        f"  - {details['instance']}: {details['attempts']} attempts, "
                        f"{details['errors']} errors, last activity at {details['last_activity']:.1f}s"
                    )
        
        else:
            # All instances reconnected successfully
            reconnect_times = [status['reconnect_time'] for status in instance_status.values()]
            avg_reconnect_time = sum(reconnect_times) / len(reconnect_times)
            
            result.recommendations.extend([
                f"All {len(hub_instances)} instances reconnected successfully",
                f"Average reconnection time: {avg_reconnect_time:.2f}s",
                f"Total reconnection attempts: {total_attempts}",
                "No significant competition interference detected"
            ])
            
            # Check for potential optimization opportunities
            if avg_reconnect_time > 10:
                result.recommendations.append("Reconnection time could be optimized")
            
            if total_attempts > len(hub_instances) * 3:
                result.recommendations.append("High number of reconnection attempts - consider optimizing retry logic")
        
        # 7. Collect comprehensive logs
        all_logs = []
        for hub_name in hub_instances:
            logs = await self._get_process_logs(hub_name)
            all_logs.extend([f"[{hub_name}] {log}" for log in logs])
        
        result.hub_service_logs = all_logs
        result.server_logs = await self._get_process_logs("server")
        
        # 8. Log detailed competition test summary
        self.logger.info("Multiple instance competition test summary:")
        self.logger.info(f"  - Total instances: {len(hub_instances)}")
        self.logger.info(f"  - Initially connected: {connected_instances}")
        self.logger.info(f"  - Successfully reconnected: {reconnected_count}")
        self.logger.info(f"  - Total reconnection attempts: {total_attempts}")
        self.logger.info(f"  - Total errors: {total_errors}")
        self.logger.info(f"  - Competition conflicts detected: {len([e for e in competition_events if e['event'] == 'competition_conflict'])}")
        self.logger.info(f"  - Server restart time: {result.server_restart_time:.2f}s")
        
        if result.time_to_reconnect:
            self.logger.info(f"  - Time for all instances to reconnect: {result.time_to_reconnect:.2f}s")
        
        # Store detailed competition analysis
        result.competition_analysis = {
            'total_instances': len(hub_instances),
            'initially_connected': connected_instances,
            'successfully_reconnected': reconnected_count,
            'instance_details': instance_status,
            'competition_events': competition_events,
            'total_attempts': total_attempts,
            'total_errors': total_errors
        }
    
    async def test_connection_interruption(self, interruption_type: str) -> InterruptionTestResult:
        """Test connection interruption scenarios"""
        self.logger.info(f"Testing connection interruption: {interruption_type}")
        
        result = InterruptionTestResult(
            interruption_type=interruption_type,
            connection_recovered=False,
            recovery_time=None,
            data_loss_detected=False,
            error_handling_correct=False,
            logs=[]
        )
        
        # Implementation would depend on specific interruption type
        # For now, return a placeholder result
        result.logs.append(f"Connection interruption test for {interruption_type} not yet implemented")
        
        return result
    
    async def test_malformed_messages(self, message_types: List[str]) -> List[MalformedMessageTestResult]:
        """Test server response to malformed messages"""
        self.logger.info("Testing malformed message handling")
        
        results = []
        for msg_type in message_types:
            result = MalformedMessageTestResult(
                message_type=msg_type,
                server_handled_gracefully=False,
                connection_terminated_properly=False,
                error_logged=False,
                security_issue_detected=False,
                logs=[]
            )
            
            # Implementation would test specific malformed message types
            result.logs.append(f"Malformed message test for {msg_type} not yet implemented")
            results.append(result)
        
        return results
    
    def simulate_network_conditions(self, condition: NetworkCondition) -> None:
        """Simulate various network conditions"""
        self.logger.info(f"Simulating network condition: {condition.value}")
        
        # Network condition simulation would require system-level tools
        # For now, this is a placeholder
        if condition == NetworkCondition.HIGH_LATENCY:
            self.logger.info("High latency simulation not implemented")
        elif condition == NetworkCondition.PACKET_LOSS:
            self.logger.info("Packet loss simulation not implemented")
        # Add other conditions as needed
    
    async def _start_process_with_logs(self, name: str, cmd: List[str]) -> Optional[subprocess.Popen]:
        """Start a process and capture its logs"""
        try:
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            self.active_processes[name] = process
            self.logger.info(f"Started process {name} with PID {process.pid}")
            return process
            
        except Exception as e:
            self.logger.error(f"Failed to start process {name}: {e}")
            return None
    
    async def _stop_process(self, name: str):
        """Stop a named process"""
        if name in self.active_processes:
            process = self.active_processes[name]
            try:
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
                    process.wait()
                
                del self.active_processes[name]
                self.logger.info(f"Stopped process {name}")
                
            except Exception as e:
                self.logger.error(f"Error stopping process {name}: {e}")
    
    async def _get_process_logs(self, name: str) -> List[str]:
        """Get logs from a named process"""
        if name in self.active_processes:
            process = self.active_processes[name]
            try:
                # Read available output without blocking
                output = []
                if process.stdout:
                    while True:
                        line = process.stdout.readline()
                        if not line:
                            break
                        output.append(line.strip())
                return output
            except Exception as e:
                self.logger.error(f"Error reading logs from {name}: {e}")
        
        return []
    
    async def _verify_server_listening(self, host: str, port: int, timeout: float = 5.0) -> bool:
        """Verify that server is listening on the specified host and port"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                # Try to connect to the server port
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1.0)
                result = sock.connect_ex((host, port))
                sock.close()
                
                if result == 0:  # Connection successful
                    return True
                    
            except Exception as e:
                self.logger.debug(f"Server connection check failed: {e}")
            
            await asyncio.sleep(0.2)  # Wait 200ms before retry
        
        return False
    
    async def _cleanup_processes(self):
        """Clean up all active processes"""
        for name in list(self.active_processes.keys()):
            await self._stop_process(name)
    
    def __del__(self):
        """Cleanup on destruction"""
        try:
            # Synchronous cleanup for destructor
            for name, process in self.active_processes.items():
                try:
                    process.terminate()
                    process.wait(timeout=2)
                except:
                    try:
                        process.kill()
                        process.wait(timeout=1)
                    except:
                        pass
        except:
            pass