"""
Authentication utilities for wsstun testing.

This module provides tools for managing server authentication configurations,
generating authentication headers, and implementing authentication test scenarios.
"""

import base64
import hashlib
import json
import logging
from dataclasses import dataclass
from enum import Enum
from typing import Dict, List, Optional, Any
import asyncio
import subprocess
import os


class AuthResult(Enum):
    """Authentication test result enumeration"""
    SUCCESS = "success"
    FAILURE = "failure"
    ERROR = "error"
    TIMEOUT = "timeout"


class ClientMode(Enum):
    """Client mode enumeration"""
    FORWARD = "forward"
    PROXY = "proxy"
    HUB_SERVICE = "hub-service"


@dataclass
class AuthScenario:
    """Authentication test scenario configuration"""
    name: str
    description: str
    server_auth_enabled: bool
    client_username: Optional[str]
    client_password: Optional[str]
    expected_result: AuthResult
    client_mode: ClientMode
    malformed_header: bool = False
    custom_headers: Optional[Dict[str, str]] = None


@dataclass
class AuthTestDetails:
    """Detailed authentication test results"""
    scenario_name: str
    auth_headers_sent: bool
    server_response_code: Optional[int]
    connection_established: bool
    error_message: Optional[str]
    server_logs: List[str]
    client_logs: List[str]


class AuthManager:
    """
    Authentication manager for wsstun server authentication testing.
    
    Manages server authentication configurations, generates authentication headers,
    and provides framework for testing various authentication scenarios.
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.auth_scenarios: List[AuthScenario] = []
        self._setup_default_scenarios()
    
    def _setup_default_scenarios(self):
        """Setup default authentication test scenarios"""
        self.auth_scenarios = [
            AuthScenario(
                name="valid_credentials_forward",
                description="Test forward mode with valid credentials",
                server_auth_enabled=True,
                client_username="testuser",
                client_password="testpass",
                expected_result=AuthResult.SUCCESS,
                client_mode=ClientMode.FORWARD
            ),
            AuthScenario(
                name="invalid_credentials_forward",
                description="Test forward mode with invalid credentials",
                server_auth_enabled=True,
                client_username="testuser",
                client_password="wrongpass",
                expected_result=AuthResult.FAILURE,
                client_mode=ClientMode.FORWARD
            ),
            AuthScenario(
                name="no_credentials_forward",
                description="Test forward mode without credentials",
                server_auth_enabled=True,
                client_username=None,
                client_password=None,
                expected_result=AuthResult.FAILURE,
                client_mode=ClientMode.FORWARD
            ),
            AuthScenario(
                name="malformed_auth_forward",
                description="Test forward mode with malformed auth header",
                server_auth_enabled=True,
                client_username="testuser",
                client_password="testpass",
                expected_result=AuthResult.FAILURE,
                client_mode=ClientMode.FORWARD,
                malformed_header=True
            ),
            AuthScenario(
                name="valid_credentials_proxy",
                description="Test proxy mode with valid credentials",
                server_auth_enabled=True,
                client_username="testuser",
                client_password="testpass",
                expected_result=AuthResult.SUCCESS,
                client_mode=ClientMode.PROXY
            ),
            AuthScenario(
                name="valid_credentials_hub_service",
                description="Test hub-service mode with valid credentials",
                server_auth_enabled=True,
                client_username="testuser",
                client_password="testpass",
                expected_result=AuthResult.SUCCESS,
                client_mode=ClientMode.HUB_SERVICE
            ),
        ]
    
    def create_auth_server_config(self, username: str, password: str, 
                                config_file: Optional[str] = None) -> Dict[str, Any]:
        """
        Create server configuration with authentication enabled.
        
        Args:
            username: Authentication username
            password: Authentication password
            config_file: Optional config file path
            
        Returns:
            Dictionary containing server configuration
        """
        config = {
            "auth": {
                "enabled": True,
                "username": username,
                "password": password,
                "method": "basic"  # Assuming basic auth for now
            },
            "server": {
                "host": "127.0.0.1",
                "port": 8060,
                "log_level": "debug"
            }
        }
        
        if config_file:
            try:
                with open(config_file, 'w') as f:
                    json.dump(config, f, indent=2)
                self.logger.info(f"Auth server config written to {config_file}")
            except Exception as e:
                self.logger.error(f"Failed to write auth config: {e}")
        
        return config
    
    def generate_auth_headers(self, username: str, password: str, 
                            malformed: bool = False) -> Dict[str, str]:
        """
        Generate authentication headers for WebSocket connection.
        
        Args:
            username: Authentication username
            password: Authentication password
            malformed: Whether to generate malformed headers for testing
            
        Returns:
            Dictionary of authentication headers
        """
        if malformed:
            # Generate intentionally malformed headers for testing
            return {
                "Authorization": "Basic invalid_base64_content",
                "X-Auth-User": username,
                "X-Auth-Pass": ""  # Empty password
            }
        
        # Generate proper Basic Auth header
        credentials = f"{username}:{password}"
        encoded_credentials = base64.b64encode(credentials.encode()).decode()
        
        return {
            "Authorization": f"Basic {encoded_credentials}",
            "X-Auth-User": username,
            "X-Auth-Hash": hashlib.sha256(password.encode()).hexdigest()
        }
    
    def get_auth_command_args(self, username: Optional[str], password: Optional[str]) -> List[str]:
        """
        Get command line arguments for authentication.
        
        Args:
            username: Authentication username (None for no auth)
            password: Authentication password (None for no auth)
            
        Returns:
            List of command line arguments
        """
        args = []
        if username and password:
            args.extend(["--username", username, "--password", password])
        return args
    
    async def test_auth_scenario(self, scenario: AuthScenario, 
                               wsstun_binary: str,
                               server_host: str = "127.0.0.1",
                               server_port: int = 8060) -> AuthTestDetails:
        """
        Test a specific authentication scenario.
        
        Args:
            scenario: Authentication scenario to test
            wsstun_binary: Path to wsstun binary
            server_host: Server host
            server_port: Server port
            
        Returns:
            Detailed test results
        """
        self.logger.info(f"Testing auth scenario: {scenario.name}")
        
        # Prepare client command based on scenario
        cmd = [wsstun_binary]
        
        if scenario.client_mode == ClientMode.FORWARD:
            cmd.extend([
                "forward",
                "--server", f"ws://{server_host}:{server_port}",
                "--listen", "127.0.0.1:8001",
                "--target", "127.0.0.1:9001"
            ])
        elif scenario.client_mode == ClientMode.PROXY:
            cmd.extend([
                "proxy",
                "--server", f"ws://{server_host}:{server_port}",
                "--listen", "127.0.0.1:8002"
            ])
        elif scenario.client_mode == ClientMode.HUB_SERVICE:
            cmd.extend([
                "hub-service",
                "--server", f"ws://{server_host}:{server_port}",
                "--service-id", "test-auth-service"
            ])
        
        # Add authentication arguments if provided
        if scenario.client_username and scenario.client_password:
            auth_args = self.get_auth_command_args(
                scenario.client_username, 
                scenario.client_password
            )
            cmd.extend(auth_args)
        
        cmd.extend(["--log-level", "debug"])
        
        # Execute test
        result = AuthTestDetails(
            scenario_name=scenario.name,
            auth_headers_sent=bool(scenario.client_username and scenario.client_password),
            server_response_code=None,
            connection_established=False,
            error_message=None,
            server_logs=[],
            client_logs=[]
        )
        
        try:
            # Start client process
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True
            )
            
            # Wait for process to start and capture initial output
            await asyncio.sleep(2)
            
            if process.poll() is None:
                result.connection_established = True
                self.logger.info(f"Auth scenario {scenario.name}: Connection established")
            else:
                # Process exited, capture output
                stdout, _ = process.communicate()
                result.client_logs.append(stdout)
                result.error_message = "Client process exited unexpectedly"
                self.logger.warning(f"Auth scenario {scenario.name}: Process exited")
            
            # Cleanup
            if process.poll() is None:
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
                    process.wait()
            
        except Exception as e:
            result.error_message = str(e)
            self.logger.error(f"Auth scenario {scenario.name} failed: {e}")
        
        return result
    
    def validate_auth_response(self, response_headers: Dict[str, str]) -> bool:
        """
        Validate authentication response from server.
        
        Args:
            response_headers: Response headers from server
            
        Returns:
            True if authentication was successful
        """
        # Check for authentication success indicators
        auth_status = response_headers.get("X-Auth-Status", "").lower()
        www_authenticate = response_headers.get("WWW-Authenticate", "")
        
        if auth_status == "success":
            return True
        elif auth_status == "failure" or www_authenticate:
            return False
        
        # If no explicit auth headers, assume success for now
        # (server may not implement auth validation yet)
        return True
    
    def get_scenario_by_name(self, name: str) -> Optional[AuthScenario]:
        """Get authentication scenario by name"""
        for scenario in self.auth_scenarios:
            if scenario.name == name:
                return scenario
        return None
    
    def get_scenarios_by_mode(self, mode: ClientMode) -> List[AuthScenario]:
        """Get all authentication scenarios for a specific client mode"""
        return [s for s in self.auth_scenarios if s.client_mode == mode]
    
    def add_custom_scenario(self, scenario: AuthScenario):
        """Add a custom authentication scenario"""
        self.auth_scenarios.append(scenario)
        self.logger.info(f"Added custom auth scenario: {scenario.name}")
    
    def generate_auth_test_report(self, results: List[AuthTestDetails]) -> Dict[str, Any]:
        """
        Generate comprehensive authentication test report.
        
        Args:
            results: List of authentication test results
            
        Returns:
            Dictionary containing test report
        """
        total_tests = len(results)
        successful_connections = sum(1 for r in results if r.connection_established)
        failed_connections = total_tests - successful_connections
        
        report = {
            "summary": {
                "total_tests": total_tests,
                "successful_connections": successful_connections,
                "failed_connections": failed_connections,
                "success_rate": successful_connections / total_tests if total_tests > 0 else 0
            },
            "test_details": [],
            "recommendations": []
        }
        
        for result in results:
            report["test_details"].append({
                "scenario": result.scenario_name,
                "connection_established": result.connection_established,
                "auth_headers_sent": result.auth_headers_sent,
                "error_message": result.error_message,
                "client_logs_count": len(result.client_logs)
            })
        
        # Add recommendations based on results
        if failed_connections > 0:
            report["recommendations"].append(
                "Some authentication tests failed. Check server authentication implementation."
            )
        
        if successful_connections == total_tests:
            report["recommendations"].append(
                "All authentication tests passed. Consider adding more edge cases."
            )
        
        return report