# Test Utilities Documentation

This directory contains enhanced test utilities for the wsstun comprehensive test suite.

## Overview

The test utilities provide three main components:

1. **AuthManager** - Authentication testing and management
2. **EdgeCaseTester** - Edge case and abnormal scenario testing
3. **EnhancedProcessManager** - Enhanced process management with monitoring

## Components

### AuthManager (`auth_utils.py`)

Manages server authentication configurations and provides framework for testing authentication scenarios.

**Key Features:**
- Generate authentication headers (Basic Auth)
- Create authentication test scenarios
- Test different client modes (Forward, Proxy, Hub-Service)
- Support for malformed authentication testing
- Comprehensive authentication test reporting

**Usage Example:**
```python
from utils import AuthManager, AuthScenario, ClientMode, AuthResult

# Create auth manager
auth_manager = AuthManager()

# Generate auth headers
headers = auth_manager.generate_auth_headers("username", "password")

# Test authentication scenario
scenario = AuthScenario(
    name="test_valid_auth",
    description="Test valid authentication",
    server_auth_enabled=True,
    client_username="testuser",
    client_password="testpass",
    expected_result=AuthResult.SUCCESS,
    client_mode=ClientMode.FORWARD
)

result = await auth_manager.test_auth_scenario(scenario, "/path/to/wsstun")
```

### EdgeCaseTester (`edge_case_utils.py`)

Provides tools for testing edge cases, network interruptions, and Hub-Service reconnection blocking scenarios.

**Key Features:**
- Hub-Service reconnection blocking detection
- Network condition simulation
- Connection interruption testing
- Malformed message testing
- Multiple reconnection scenario testing

**Usage Example:**
```python
from utils import EdgeCaseTester, NetworkCondition

# Create edge case tester
edge_tester = EdgeCaseTester()

# Test Hub-Service reconnection blocking
results = await edge_tester.test_hub_service_reconnect_blocking("/path/to/wsstun")

# Simulate network conditions
edge_tester.simulate_network_conditions(NetworkCondition.HIGH_LATENCY)
```

### EnhancedProcessManager (`test_helpers.py`)

Enhanced process management with monitoring, resource tracking, and improved error handling.

**Key Features:**
- Process status monitoring
- Resource usage tracking (CPU, memory)
- Automatic process health checks
- Enhanced logging and error handling
- Graceful shutdown handling
- Process restart capabilities

**Usage Example:**
```python
from utils import EnhancedProcessManager, TestConfig

# Create enhanced process manager
config = TestConfig(verbose=True, enable_process_monitoring=True)
pm = EnhancedProcessManager(config)

# Start a process
success = await pm.start_process(
    name="server",
    cmd=["wsstun", "server", "--listen", "127.0.0.1:8060"],
    wait_for_ready="Starting server mode"
)

# Monitor process
if pm.is_process_running("server"):
    info = pm.get_process_info("server")
    print(f"Process CPU: {info.cpu_percent}%")
    print(f"Process Memory: {info.memory_mb}MB")

# Stop process
await pm.stop_process("server")
```

## Integration with Existing Tests

These utilities are designed to enhance the existing test suite. Here's how to integrate them:

### 1. Replace ProcessManager

Replace the existing `ProcessManager` with `EnhancedProcessManager`:

```python
# Old way
from test_wsstun_comprehensive import ProcessManager

# New way
from utils import EnhancedProcessManager, TestConfig

config = TestConfig()
pm = EnhancedProcessManager(config)
```

### 2. Add Authentication Testing

Add authentication tests using `AuthManager`:

```python
from utils import AuthManager

auth_manager = AuthManager()
scenarios = auth_manager.auth_scenarios

for scenario in scenarios:
    result = await auth_manager.test_auth_scenario(scenario, wsstun_binary)
    # Process result...
```

### 3. Add Edge Case Testing

Add edge case testing using `EdgeCaseTester`:

```python
from utils import EdgeCaseTester

edge_tester = EdgeCaseTester()

# Test Hub-Service reconnection blocking
blocking_results = await edge_tester.test_hub_service_reconnect_blocking(wsstun_binary)

for result in blocking_results:
    if result.blocking_detected:
        print(f"Blocking detected in {result.test_type.value}")
        for recommendation in result.recommendations:
            print(f"  - {recommendation}")
```

## Configuration

The utilities use the enhanced `TestConfig` class:

```python
@dataclass
class TestConfig:
    wsstun_binary: str = "../target/debug/wsstun"
    server_host: str = "127.0.0.1"
    server_port: int = 8060
    test_timeout: int = 30
    verbose: bool = False
    keep_logs: bool = False
    log_dir: str = "./test_logs"
    
    # Enhanced options
    max_process_startup_time: int = 10
    process_cleanup_timeout: int = 5
    log_rotation_size: int = 10 * 1024 * 1024  # 10MB
    enable_process_monitoring: bool = True
    heartbeat_check_interval: int = 5
```

## Requirements

The utilities require the following Python packages:
- `asyncio` (built-in)
- `subprocess` (built-in)
- `psutil` (for process monitoring)
- `logging` (built-in)

Install additional requirements:
```bash
pip install psutil
```

## Testing

Run the verification test to ensure utilities work correctly:

```bash
cd tests
python test_utils_verification.py
```

## Next Steps

These utilities are ready to be integrated into the main test suite. The next tasks in the implementation plan will use these utilities to:

1. Implement authentication test scenarios
2. Add Hub-Service reconnection blocking tests
3. Enhance existing tests with better process management
4. Add comprehensive edge case testing

## Architecture

```
tests/utils/
├── __init__.py              # Package initialization and exports
├── auth_utils.py            # Authentication management utilities
├── edge_case_utils.py       # Edge case testing utilities
├── test_helpers.py          # Enhanced process management and helpers
└── README.md               # This documentation
```

Each utility module is designed to be:
- **Modular**: Can be used independently
- **Extensible**: Easy to add new functionality
- **Testable**: Includes comprehensive error handling
- **Documented**: Clear interfaces and usage examples