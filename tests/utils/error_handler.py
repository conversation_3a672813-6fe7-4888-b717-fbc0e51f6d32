"""
Enhanced error handling and logging infrastructure for wsstun testing.

This module provides unified error handling, detailed logging, and diagnostic
capabilities to improve test reliability and debugging.
"""

import logging
import traceback
import time
import json
import os
import sys
import threading
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass, asdict
from enum import Enum
from contextlib import contextmanager
from pathlib import Path


class ErrorSeverity(Enum):
    """Error severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """Error categories for classification"""
    CONFIGURATION = "configuration"
    CONNECTION = "connection"
    AUTHENTICATION = "authentication"
    PROTOCOL = "protocol"
    PROCESS = "process"
    RESOURCE = "resource"
    TIMEOUT = "timeout"
    SYSTEM = "system"
    TEST_FRAMEWORK = "test_framework"
    UNKNOWN = "unknown"


@dataclass
class ErrorContext:
    """Context information for errors"""
    test_name: Optional[str] = None
    test_id: Optional[str] = None
    process_name: Optional[str] = None
    operation: Optional[str] = None
    timestamp: float = 0.0
    thread_id: Optional[int] = None
    additional_data: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.timestamp == 0.0:
            self.timestamp = time.time()
        if self.thread_id is None:
            self.thread_id = threading.get_ident()


@dataclass
class ErrorRecord:
    """Detailed error record for tracking and analysis"""
    error_id: str
    category: ErrorCategory
    severity: ErrorSeverity
    message: str
    exception_type: Optional[str] = None
    exception_message: Optional[str] = None
    traceback_str: Optional[str] = None
    context: Optional[ErrorContext] = None
    timestamp: float = 0.0
    resolved: bool = False
    resolution_notes: Optional[str] = None
    
    def __post_init__(self):
        if self.timestamp == 0.0:
            self.timestamp = time.time()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert error record to dictionary"""
        result = asdict(self)
        result['category'] = self.category.value
        result['severity'] = self.severity.value
        return result


class EnhancedLogger:
    """Enhanced logger with structured logging and context tracking"""
    
    def __init__(self, name: str, log_dir: str = "./test_logs", 
                 enable_console: bool = True, enable_file: bool = True):
        self.name = name
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # Create logger
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)
        
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Setup formatters
        self.detailed_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - '
            '[PID:%(process)d] [TID:%(thread)d] - %(message)s'
        )
        
        self.simple_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        
        # Setup handlers
        if enable_console:
            self._setup_console_handler()
        
        if enable_file:
            self._setup_file_handler()
        
        # Context tracking
        self._context_stack: List[ErrorContext] = []
        self._context_lock = threading.Lock()
    
    def _setup_console_handler(self):
        """Setup console handler with color support"""
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        
        # Create colored formatter with the format string, not the formatter object
        colored_formatter = ColoredFormatter(self.simple_formatter._fmt)
        console_handler.setFormatter(colored_formatter)
        self.logger.addHandler(console_handler)
    
    def _setup_file_handler(self):
        """Setup file handler with rotation"""
        log_file = self.log_dir / f"{self.name}.log"
        
        try:
            from logging.handlers import RotatingFileHandler
            file_handler = RotatingFileHandler(
                log_file, maxBytes=10*1024*1024, backupCount=5
            )
        except ImportError:
            file_handler = logging.FileHandler(log_file)
        
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(self.detailed_formatter)
        self.logger.addHandler(file_handler)
    
    @contextmanager
    def context(self, **kwargs):
        """Context manager for adding context to log messages"""
        ctx = ErrorContext(**kwargs)
        with self._context_lock:
            self._context_stack.append(ctx)
        
        try:
            yield ctx
        finally:
            with self._context_lock:
                if self._context_stack and self._context_stack[-1] == ctx:
                    self._context_stack.pop()
    
    def _get_current_context(self) -> Optional[ErrorContext]:
        """Get current context from stack"""
        with self._context_lock:
            return self._context_stack[-1] if self._context_stack else None
    
    def _format_message_with_context(self, message: str) -> str:
        """Format message with current context"""
        ctx = self._get_current_context()
        if not ctx:
            return message
        
        context_parts = []
        if ctx.test_name:
            context_parts.append(f"test={ctx.test_name}")
        if ctx.process_name:
            context_parts.append(f"process={ctx.process_name}")
        if ctx.operation:
            context_parts.append(f"op={ctx.operation}")
        
        if context_parts:
            return f"[{', '.join(context_parts)}] {message}"
        return message
    
    def debug(self, message: str, **kwargs):
        """Log debug message with context"""
        self.logger.debug(self._format_message_with_context(message), **kwargs)
    
    def info(self, message: str, **kwargs):
        """Log info message with context"""
        self.logger.info(self._format_message_with_context(message), **kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning message with context"""
        self.logger.warning(self._format_message_with_context(message), **kwargs)
    
    def error(self, message: str, **kwargs):
        """Log error message with context"""
        self.logger.error(self._format_message_with_context(message), **kwargs)
    
    def critical(self, message: str, **kwargs):
        """Log critical message with context"""
        self.logger.critical(self._format_message_with_context(message), **kwargs)


class ColoredFormatter(logging.Formatter):
    """Colored formatter for console output"""
    
    COLORS = {
        'DEBUG': '\033[36m',    # Cyan
        'INFO': '\033[32m',     # Green
        'WARNING': '\033[33m',  # Yellow
        'ERROR': '\033[31m',    # Red
        'CRITICAL': '\033[35m', # Magenta
    }
    RESET = '\033[0m'
    
    def format(self, record):
        log_color = self.COLORS.get(record.levelname, '')
        record.levelname = f"{log_color}{record.levelname}{self.RESET}"
        return super().format(record)


class ErrorHandler:
    """Unified error handling system with categorization and analysis"""
    
    def __init__(self, log_dir: str = "./test_logs"):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # Error tracking
        self.error_records: List[ErrorRecord] = []
        self.error_counts: Dict[ErrorCategory, int] = {cat: 0 for cat in ErrorCategory}
        self.severity_counts: Dict[ErrorSeverity, int] = {sev: 0 for sev in ErrorSeverity}
        
        # Logger
        self.logger = EnhancedLogger("ErrorHandler", str(log_dir))
        
        # Error handlers by category
        self.category_handlers: Dict[ErrorCategory, List[Callable]] = {}
        
        # Lock for thread safety
        self._lock = threading.Lock()
        
        # Setup default handlers
        self._setup_default_handlers()
    
    def _setup_default_handlers(self):
        """Setup default error handlers for each category"""
        self.register_handler(ErrorCategory.CONNECTION, self._handle_connection_error)
        self.register_handler(ErrorCategory.TIMEOUT, self._handle_timeout_error)
        self.register_handler(ErrorCategory.PROCESS, self._handle_process_error)
        self.register_handler(ErrorCategory.AUTHENTICATION, self._handle_auth_error)
    
    def register_handler(self, category: ErrorCategory, handler: Callable[[ErrorRecord], None]):
        """Register error handler for specific category"""
        if category not in self.category_handlers:
            self.category_handlers[category] = []
        self.category_handlers[category].append(handler)
    
    def handle_error(self, 
                    message: str,
                    category: ErrorCategory = ErrorCategory.UNKNOWN,
                    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                    exception: Optional[Exception] = None,
                    context: Optional[ErrorContext] = None,
                    auto_resolve: bool = False) -> str:
        """
        Handle an error with full categorization and logging.
        
        Returns:
            Error ID for tracking
        """
        # Generate unique error ID
        error_id = f"ERR_{int(time.time() * 1000)}_{len(self.error_records)}"
        
        # Create error record
        error_record = ErrorRecord(
            error_id=error_id,
            category=category,
            severity=severity,
            message=message,
            context=context or ErrorContext()
        )
        
        # Add exception details if provided
        if exception:
            error_record.exception_type = type(exception).__name__
            error_record.exception_message = str(exception)
            error_record.traceback_str = traceback.format_exc()
        
        # Thread-safe recording
        with self._lock:
            self.error_records.append(error_record)
            self.error_counts[category] += 1
            self.severity_counts[severity] += 1
        
        # Log the error
        self._log_error(error_record)
        
        # Execute category-specific handlers
        self._execute_handlers(error_record)
        
        # Auto-resolve if requested
        if auto_resolve:
            self.resolve_error(error_id, "Auto-resolved")
        
        return error_id
    
    def _log_error(self, error_record: ErrorRecord):
        """Log error record with appropriate level"""
        log_message = f"[{error_record.error_id}] {error_record.message}"
        
        if error_record.context:
            ctx = error_record.context
            with self.logger.context(
                test_name=ctx.test_name,
                process_name=ctx.process_name,
                operation=ctx.operation
            ):
                if error_record.severity == ErrorSeverity.CRITICAL:
                    self.logger.critical(log_message)
                elif error_record.severity == ErrorSeverity.HIGH:
                    self.logger.error(log_message)
                elif error_record.severity == ErrorSeverity.MEDIUM:
                    self.logger.warning(log_message)
                else:
                    self.logger.info(log_message)
        else:
            if error_record.severity == ErrorSeverity.CRITICAL:
                self.logger.critical(log_message)
            elif error_record.severity == ErrorSeverity.HIGH:
                self.logger.error(log_message)
            elif error_record.severity == ErrorSeverity.MEDIUM:
                self.logger.warning(log_message)
            else:
                self.logger.info(log_message)
        
        # Log exception details if available
        if error_record.traceback_str:
            self.logger.debug(f"Exception traceback for {error_record.error_id}:\n{error_record.traceback_str}")
    
    def _execute_handlers(self, error_record: ErrorRecord):
        """Execute registered handlers for error category"""
        handlers = self.category_handlers.get(error_record.category, [])
        for handler in handlers:
            try:
                handler(error_record)
            except Exception as e:
                self.logger.error(f"Error in handler for {error_record.category}: {e}")
    
    def resolve_error(self, error_id: str, resolution_notes: str = ""):
        """Mark error as resolved"""
        with self._lock:
            for record in self.error_records:
                if record.error_id == error_id:
                    record.resolved = True
                    record.resolution_notes = resolution_notes
                    self.logger.info(f"Error {error_id} resolved: {resolution_notes}")
                    return True
        return False
    
    def get_error_summary(self) -> Dict[str, Any]:
        """Get comprehensive error summary"""
        with self._lock:
            total_errors = len(self.error_records)
            unresolved_errors = sum(1 for r in self.error_records if not r.resolved)
            
            return {
                "total_errors": total_errors,
                "unresolved_errors": unresolved_errors,
                "resolution_rate": (total_errors - unresolved_errors) / total_errors if total_errors > 0 else 1.0,
                "category_breakdown": {cat.value: count for cat, count in self.error_counts.items()},
                "severity_breakdown": {sev.value: count for sev, count in self.severity_counts.items()},
                "recent_errors": [r.to_dict() for r in self.error_records[-10:]],
                "critical_unresolved": [
                    r.to_dict() for r in self.error_records 
                    if not r.resolved and r.severity == ErrorSeverity.CRITICAL
                ]
            }
    
    def export_error_report(self, filename: Optional[str] = None) -> str:
        """Export detailed error report to JSON file"""
        if filename is None:
            filename = self.log_dir / f"error_report_{int(time.time())}.json"
        else:
            filename = Path(filename)
        
        report = {
            "generated_at": time.time(),
            "summary": self.get_error_summary(),
            "all_errors": [r.to_dict() for r in self.error_records]
        }
        
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        self.logger.info(f"Error report exported to {filename}")
        return str(filename)
    
    # Default error handlers
    def _handle_connection_error(self, error_record: ErrorRecord):
        """Handle connection-related errors"""
        self.logger.debug(f"Connection error handler triggered for {error_record.error_id}")
        # Add connection-specific recovery logic here
    
    def _handle_timeout_error(self, error_record: ErrorRecord):
        """Handle timeout-related errors"""
        self.logger.debug(f"Timeout error handler triggered for {error_record.error_id}")
        # Add timeout-specific recovery logic here
    
    def _handle_process_error(self, error_record: ErrorRecord):
        """Handle process-related errors"""
        self.logger.debug(f"Process error handler triggered for {error_record.error_id}")
        # Add process-specific recovery logic here
    
    def _handle_auth_error(self, error_record: ErrorRecord):
        """Handle authentication-related errors"""
        self.logger.debug(f"Authentication error handler triggered for {error_record.error_id}")
        # Add auth-specific recovery logic here


# Global error handler instance
_global_error_handler: Optional[ErrorHandler] = None


def get_error_handler(log_dir: str = "./test_logs") -> ErrorHandler:
    """Get or create global error handler instance"""
    global _global_error_handler
    if _global_error_handler is None:
        _global_error_handler = ErrorHandler(log_dir)
    return _global_error_handler


def handle_error(message: str, 
                category: ErrorCategory = ErrorCategory.UNKNOWN,
                severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                exception: Optional[Exception] = None,
                context: Optional[ErrorContext] = None,
                **context_kwargs) -> str:
    """
    Convenience function for handling errors.
    
    Args:
        message: Error message
        category: Error category
        severity: Error severity
        exception: Exception object if available
        context: Error context
        **context_kwargs: Additional context as keyword arguments
    
    Returns:
        Error ID for tracking
    """
    if context is None and context_kwargs:
        context = ErrorContext(**context_kwargs)
    
    return get_error_handler().handle_error(
        message=message,
        category=category,
        severity=severity,
        exception=exception,
        context=context
    )


@contextmanager
def error_context(**kwargs):
    """Context manager for error handling with automatic context"""
    handler = get_error_handler()
    with handler.logger.context(**kwargs):
        yield


# Decorators for automatic error handling
def handle_exceptions(category: ErrorCategory = ErrorCategory.UNKNOWN,
                     severity: ErrorSeverity = ErrorSeverity.HIGH,
                     reraise: bool = True):
    """Decorator for automatic exception handling"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                handle_error(
                    message=f"Exception in {func.__name__}: {str(e)}",
                    category=category,
                    severity=severity,
                    exception=e,
                    context=ErrorContext(operation=func.__name__)
                )
                if reraise:
                    raise
                return None
        return wrapper
    return decorator


async def handle_exceptions_async(category: ErrorCategory = ErrorCategory.UNKNOWN,
                                 severity: ErrorSeverity = ErrorSeverity.HIGH,
                                 reraise: bool = True):
    """Async decorator for automatic exception handling"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                handle_error(
                    message=f"Exception in {func.__name__}: {str(e)}",
                    category=category,
                    severity=severity,
                    exception=e,
                    context=ErrorContext(operation=func.__name__)
                )
                if reraise:
                    raise
                return None
        return wrapper
    return decorator