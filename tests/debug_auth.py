#!/usr/bin/env python3

import asyncio
import websockets
import websockets.exceptions

async def test_auth():
    try:
        # This should fail with authentication error
        async with websockets.connect("ws://localhost:9999/nonexistent") as ws:
            pass
    except Exception as e:
        print(f"Exception type: {type(e)}")
        print(f"Exception: {e}")
        print(f"Exception dir: {[attr for attr in dir(e) if not attr.startswith('_')]}")
        
        # Check if it has status_code attribute
        if hasattr(e, 'status_code'):
            print(f"status_code: {e.status_code}")
        else:
            print("No status_code attribute")
            
        # Check if it's InvalidStatus
        if isinstance(e, websockets.exceptions.InvalidStatus):
            print("Is InvalidStatus exception")
            # Check what attributes InvalidStatus has
            invalid_status_attrs = [attr for attr in dir(e) if not attr.startswith('_')]
            print(f"InvalidStatus attributes: {invalid_status_attrs}")
            
        elif isinstance(e, websockets.exceptions.InvalidHandshake):
            print("Is InvalidHandshake exception")

if __name__ == "__main__":
    asyncio.run(test_auth())