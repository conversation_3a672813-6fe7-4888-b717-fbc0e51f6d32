#!/usr/bin/env python3

import asyncio
import websockets
import websockets.exceptions

async def test_auth_fix():
    """Test the authentication fix with proper headers"""
    
    # Test with correct credentials
    correct_auth_header = "Basic " + "dGVzdDp0ZXN0".encode().decode()  # test:test base64
    headers = {"Authorization": correct_auth_header}
    
    print("Testing authentication with headers...")
    print(f"Headers: {headers}")
    
    try:
        # This should fail with connection refused, but we want to see if the header format is correct
        async with websockets.connect("ws://localhost:9999/test", extra_headers=headers) as ws:
            print("Unexpected connection success!")
    except Exception as e:
        print(f"Exception type: {type(e)}")
        print(f"Exception: {e}")
        
        # Check if it's a websockets exception
        if hasattr(e, 'response') and hasattr(e.response, 'status_code'):
            print(f"Status code: {e.response.status_code}")
        elif hasattr(e, 'status_code'):
            print(f"Status code: {e.status_code}")

if __name__ == "__main__":
    asyncio.run(test_auth_fix())