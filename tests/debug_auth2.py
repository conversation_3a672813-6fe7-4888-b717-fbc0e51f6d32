#!/usr/bin/env python3

import asyncio
import websockets
import websockets.exceptions

async def test_auth():
    # Test with a server that exists but will return 401
    try:
        # Use a real server that might return 401
        async with websockets.connect("ws://httpbin.org/ws") as ws:
            pass
    except Exception as e:
        print(f"Exception type: {type(e)}")
        print(f"Exception: {e}")
        print(f"Exception dir: {[attr for attr in dir(e) if not attr.startswith('_')]}")
        
        # Check specific exception types
        if hasattr(e, 'status_code'):
            print(f"status_code: {e.status_code}")
        else:
            print("No status_code attribute")
            
        # Check for response attribute
        if hasattr(e, 'response'):
            print(f"response: {e.response}")
            if hasattr(e.response, 'status_code'):
                print(f"response.status_code: {e.response.status_code}")
        
        # Check specific websockets exceptions
        if isinstance(e, websockets.exceptions.InvalidStatus):
            print("Is InvalidStatus exception")
            invalid_status_attrs = [attr for attr in dir(e) if not attr.startswith('_')]
            print(f"InvalidStatus attributes: {invalid_status_attrs}")
            
        elif isinstance(e, websockets.exceptions.InvalidHandshake):
            print("Is InvalidHandshake exception")
            invalid_handshake_attrs = [attr for attr in dir(e) if not attr.startswith('_')]
            print(f"InvalidHandshake attributes: {invalid_handshake_attrs}")

if __name__ == "__main__":
    asyncio.run(test_auth())