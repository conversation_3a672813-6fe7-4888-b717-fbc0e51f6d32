@echo off
setlocal enabledelayedexpansion

:: WSSTun Comprehensive Test Runner (Windows)
:: Usage: run_tests.bat [options]

:: Default configuration
set "VERBOSE=false"
set "KEEP_LOGS=false"
set "BINARY_PATH=..\target\debug\wsstun.exe"
set "SERVER_PORT=8060"
set "TIMEOUT=30"
set "BUILD_FIRST=true"
set "CLEAN_FIRST=false"
set "RELEASE_MODE=false"
set "CLEANUP_ONLY=false"

:: Parse command line arguments
:parse_args
if "%~1"=="" goto :end_parse_args
if "%~1"=="-h" goto :show_help
if "%~1"=="--help" goto :show_help
if "%~1"=="-v" (
    set "VERBOSE=true"
    shift
    goto :parse_args
)
if "%~1"=="--verbose" (
    set "VERBOSE=true"
    shift
    goto :parse_args
)
if "%~1"=="-k" (
    set "KEEP_LOGS=true"
    shift
    goto :parse_args
)
if "%~1"=="--keep-logs" (
    set "KEEP_LOGS=true"
    shift
    goto :parse_args
)
if "%~1"=="-b" (
    set "BINARY_PATH=%~2"
    shift
    shift
    goto :parse_args
)
if "%~1"=="--binary" (
    set "BINARY_PATH=%~2"
    shift
    shift
    goto :parse_args
)
if "%~1"=="-p" (
    set "SERVER_PORT=%~2"
    shift
    shift
    goto :parse_args
)
if "%~1"=="--port" (
    set "SERVER_PORT=%~2"
    shift
    shift
    goto :parse_args
)
if "%~1"=="-t" (
    set "TIMEOUT=%~2"
    shift
    shift
    goto :parse_args
)
if "%~1"=="--timeout" (
    set "TIMEOUT=%~2"
    shift
    shift
    goto :parse_args
)
if "%~1"=="--no-build" (
    set "BUILD_FIRST=false"
    shift
    goto :parse_args
)
if "%~1"=="--clean" (
    set "CLEAN_FIRST=true"
    shift
    goto :parse_args
)
if "%~1"=="--release" (
    set "BINARY_PATH=..\target\release\wsstun.exe"
    set "RELEASE_MODE=true"
    shift
    goto :parse_args
)
if "%~1"=="--cleanup-only" (
    set "CLEANUP_ONLY=true"
    shift
    goto :parse_args
)
echo Unknown option: %~1
goto :show_help

:end_parse_args

:: Display configuration
echo === WSSTun Comprehensive Test Configuration ===
echo Binary file: %BINARY_PATH%
echo Server port: %SERVER_PORT%
echo Test timeout: %TIMEOUT% seconds
echo Verbose output: %VERBOSE%
echo Keep logs: %KEEP_LOGS%
echo Build project: %BUILD_FIRST%
echo.

:: Handle cleanup-only mode
if "%CLEANUP_ONLY%"=="true" (
    echo WSSTun Test Environment Cleanup
    echo.
    
    :: Run Python cleanup
    set "PYTHON_CMD=python test_wsstun_comprehensive.py --cleanup-only"
    if "%VERBOSE%"=="true" (
        set "PYTHON_CMD=!PYTHON_CMD! --verbose"
    )
    
    !PYTHON_CMD!
    set CLEANUP_EXIT_CODE=!errorlevel!
    
    echo.
    if !CLEANUP_EXIT_CODE!==0 (
        echo === Cleanup completed successfully! ===
    ) else (
        echo === Cleanup failed ===
    )
    
    exit /b !CLEANUP_EXIT_CODE!
)

:: Install Python dependencies
echo Installing Python dependencies...
pip install -r requirements.txt

echo ✓ Setup completed

:: Build project
if "%BUILD_FIRST%"=="false" (
    echo Skipping build step
    goto :check_binary
)

echo Building project...

:: Switch to project root directory for compilation
cd ..

:: Clean if needed
if "%CLEAN_FIRST%"=="true" (
    echo Cleaning project...
    cargo clean
)

:: Build
if "%RELEASE_MODE%"=="true" (
    echo Building in release mode...
    cargo build --release
) else (
    echo Building in debug mode...
    cargo build
)

:: Return to test directory
cd tests

if errorlevel 1 (
    echo ✗ Build failed
    exit /b 1
)

echo ✓ Build successful

:check_binary
:: Check binary file
echo Checking binary file...

if not exist "%BINARY_PATH%" (
    echo Error: Binary file not found: %BINARY_PATH%
    if "%BUILD_FIRST%"=="false" (
        echo Hint: Try running with build option
    )
    exit /b 1
)

echo ✓ Binary file check passed

:: Run tests
echo Running tests...
echo.

:: Build Python command
set "PYTHON_CMD=python test_wsstun_comprehensive.py"
set "PYTHON_CMD=%PYTHON_CMD% --binary %BINARY_PATH%"
set "PYTHON_CMD=%PYTHON_CMD% --server-port %SERVER_PORT%"
set "PYTHON_CMD=%PYTHON_CMD% --timeout %TIMEOUT%"

if "%VERBOSE%"=="true" (
    set "PYTHON_CMD=%PYTHON_CMD% --verbose"
)

if "%KEEP_LOGS%"=="true" (
    set "PYTHON_CMD=%PYTHON_CMD% --keep-logs"
)

:: Execute tests
%PYTHON_CMD%
set TEST_EXIT_CODE=%errorlevel%

echo.
if %TEST_EXIT_CODE%==0 (
    echo === All tests passed! ===
) else (
    echo === Tests failed ===
    echo Check log directory for details: .\test_logs\
)

exit /b %TEST_EXIT_CODE%

:show_help
echo WSSTun Comprehensive Test Runner (Windows)
echo.
echo Usage: %~nx0 [options]
echo.
echo Options:
echo   -h, --help          Show this help message
echo   -v, --verbose       Verbose output
echo   -k, --keep-logs     Keep test log files
echo   -b, --binary PATH   Specify wsstun binary path (default: ..\target\debug\wsstun.exe)
echo   -p, --port PORT     Specify server port (default: %SERVER_PORT%)
echo   -t, --timeout SEC   Specify test timeout (default: %TIMEOUT% seconds)
echo   --no-build          Skip build step
echo   --clean             Clean before build
echo   --release           Use release mode
echo   --cleanup-only      Only perform cleanup and exit
echo.
echo Examples:
echo   %~nx0                  # Run all tests
echo   %~nx0 -v -k            # Verbose output and keep logs
echo   %~nx0 --no-build       # Skip build, run tests only
echo   %~nx0 --release        # Use release mode
echo   %~nx0 --cleanup-only   # Clean up processes and logs
exit /b 0 