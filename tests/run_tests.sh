#!/bin/bash

# WSSTun Comprehensive Test Runner
# Usage: ./run_tests.sh [options]

set -e

# Color definitions
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Default configuration
VERBOSE=false
KEEP_LOGS=false
BINARY_PATH="../target/debug/wsstun"
SERVER_PORT=8060
TIMEOUT=30
BUILD_FIRST=true
CLEAN_FIRST=false
CLEANUP_ONLY=false

# Show help information
show_help() {
    echo "WSSTun Comprehensive Test Runner"
    echo ""
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  -h, --help          Show this help message"
    echo "  -v, --verbose       Verbose output"
    echo "  -k, --keep-logs     Keep test log files"
    echo "  -b, --binary PATH   Specify wsstun binary path (default: ../target/debug/wsstun)"
    echo "  -p, --port PORT     Specify server port (default: $SERVER_PORT)"
    echo "  -t, --timeout SEC   Specify test timeout (default: $TIMEOUT seconds)"
    echo "  --no-build          Skip build step"
    echo "  --clean             Clean before build"
    echo "  --release           Use release mode"
    echo "  --cleanup-only      Only perform cleanup and exit"
    echo ""
    echo "Examples:"
    echo "  $0                  # Run all tests"
    echo "  $0 -v -k            # Verbose output and keep logs"
    echo "  $0 --no-build       # Skip build, run tests only"
    echo "  $0 --release        # Use release mode"
    echo "  $0 --cleanup-only   # Clean up processes and logs"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -k|--keep-logs)
            KEEP_LOGS=true
            shift
            ;;
        -b|--binary)
            BINARY_PATH="$2"
            shift 2
            ;;
        -p|--port)
            SERVER_PORT="$2"
            shift 2
            ;;
        -t|--timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        --no-build)
            BUILD_FIRST=false
            shift
            ;;
        --clean)
            CLEAN_FIRST=true
            shift
            ;;
        --release)
            BINARY_PATH="../target/release/wsstun"
            RELEASE_MODE=true
            shift
            ;;
        --cleanup-only)
            CLEANUP_ONLY=true
            shift
            ;;
        *)
            echo -e "${RED}Unknown option: $1${NC}"
            show_help
            exit 1
            ;;
    esac
done

# Print configuration
echo -e "${BOLD}=== WSSTun Comprehensive Test Configuration ===${NC}"
echo -e "Binary file: ${BLUE}$BINARY_PATH${NC}"
echo -e "Server port: ${BLUE}$SERVER_PORT${NC}"
echo -e "Test timeout: ${BLUE}$TIMEOUT seconds${NC}"
echo -e "Verbose output: ${BLUE}$VERBOSE${NC}"
echo -e "Keep logs: ${BLUE}$KEEP_LOGS${NC}"
echo -e "Build project: ${BLUE}$BUILD_FIRST${NC}"
echo ""

# Install Python dependencies
echo -e "${YELLOW}Installing Python dependencies...${NC}"
pip3 install -r requirements.txt

echo -e "${GREEN}✓ Setup completed${NC}"

# Build project
build_project() {
    if [ "$BUILD_FIRST" = false ]; then
        echo -e "${YELLOW}Skipping build step${NC}"
        return
    fi
    
    echo -e "${YELLOW}Building project...${NC}"
    
    # Switch to project root directory for compilation
    cd ..
    
    # Clean if needed
    if [ "$CLEAN_FIRST" = true ]; then
        echo -e "${YELLOW}Cleaning project...${NC}"
        cargo clean
    fi
    
    # Build
    if [ "$RELEASE_MODE" = true ]; then
        echo -e "${YELLOW}Building in release mode...${NC}"
        cargo build --release
    else
        echo -e "${YELLOW}Building in debug mode...${NC}"
        cargo build
    fi
    
    # Return to test directory
    cd tests
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ Build successful${NC}"
    else
        echo -e "${RED}✗ Build failed${NC}"
        exit 1
    fi
}

# Check binary file
check_binary() {
    echo -e "${YELLOW}Checking binary file...${NC}"
    
    if [ ! -f "$BINARY_PATH" ]; then
        echo -e "${RED}Error: Binary file not found: $BINARY_PATH${NC}"
        if [ "$BUILD_FIRST" = false ]; then
            echo -e "${YELLOW}Hint: Try running with build option${NC}"
        fi
        exit 1
    fi
    
    if [ ! -x "$BINARY_PATH" ]; then
        echo -e "${RED}Error: Binary file not executable: $BINARY_PATH${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✓ Binary file check passed${NC}"
}

# Run tests
run_tests() {
    echo -e "${YELLOW}Running tests...${NC}"
    echo ""
    
    # Build Python command
    PYTHON_CMD="python3 test_wsstun_comprehensive.py"
    PYTHON_CMD="$PYTHON_CMD --binary $BINARY_PATH"
    PYTHON_CMD="$PYTHON_CMD --server-port $SERVER_PORT"
    PYTHON_CMD="$PYTHON_CMD --timeout $TIMEOUT"
    
    if [ "$VERBOSE" = true ]; then
        PYTHON_CMD="$PYTHON_CMD --verbose"
    fi
    
    if [ "$KEEP_LOGS" = true ]; then
        PYTHON_CMD="$PYTHON_CMD --keep-logs"
    fi
    
    # Execute tests
    eval $PYTHON_CMD
    TEST_EXIT_CODE=$?
    
    echo ""
    if [ $TEST_EXIT_CODE -eq 0 ]; then
        echo -e "${GREEN}${BOLD}=== All tests passed! ===${NC}"
    else
        echo -e "${RED}${BOLD}=== Tests failed ===${NC}"
        echo -e "${YELLOW}Check log directory for details: ./test_logs/${NC}"
    fi
    
    return $TEST_EXIT_CODE
}

# Cleanup function
cleanup() {
    pkill -f "test_wsstun_comprehensive" 2>/dev/null || true
}

# Set signal handlers
trap cleanup EXIT INT TERM

# Main execution flow
main() {
    # Handle cleanup-only mode
    if [ "$CLEANUP_ONLY" = true ]; then
        echo -e "${BOLD}WSSTun Test Environment Cleanup${NC}"
        echo ""
        
        # Run Python cleanup
        PYTHON_CMD="python3 test_wsstun_comprehensive.py --cleanup-only"
        if [ "$VERBOSE" = true ]; then
            PYTHON_CMD="$PYTHON_CMD --verbose"
        fi
        
        eval $PYTHON_CMD
        CLEANUP_EXIT_CODE=$?
        
        echo ""
        if [ $CLEANUP_EXIT_CODE -eq 0 ]; then
            echo -e "${GREEN}${BOLD}=== Cleanup completed successfully! ===${NC}"
        else
            echo -e "${RED}${BOLD}=== Cleanup failed ===${NC}"
        fi
        
        exit $CLEANUP_EXIT_CODE
    fi
    
    echo -e "${BOLD}Starting WSSTun Comprehensive Tests${NC}"
    echo ""
    
    build_project
    check_binary
    run_tests
    
    exit $?
}

# Run main function
main 