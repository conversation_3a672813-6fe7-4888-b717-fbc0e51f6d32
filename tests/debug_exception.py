#!/usr/bin/env python3

import asyncio
import websockets
import websockets.exceptions

async def test_exception():
    """Test what exception is actually thrown"""
    
    # Test with incorrect credentials
    incorrect_auth_header = "Basic " + "d3Jvbmc6d3Jvbmc=".encode().decode()  # wrong:wrong base64
    headers = {"Authorization": incorrect_auth_header}
    
    print("Testing with incorrect credentials...")
    print(f"Headers: {headers}")
    
    try:
        async with websockets.connect("ws://localhost:8070/hub", additional_headers=headers) as ws:
            print("Unexpected connection success!")
    except Exception as e:
        print(f"Exception type: {type(e)}")
        print(f"Exception: {e}")
        print(f"Exception attributes: {[attr for attr in dir(e) if not attr.startswith('_')]}")
        
        # Check specific websockets exceptions
        if isinstance(e, websockets.exceptions.InvalidStatus):
            print("=== InvalidStatus Exception Details ===")
            if hasattr(e, 'response'):
                print(f"Response: {e.response}")
                if hasattr(e.response, 'status_code'):
                    print(f"Status code: {e.response.status_code}")
                if hasattr(e.response, 'headers'):
                    print(f"Headers: {dict(e.response.headers)}")
                if hasattr(e.response, 'text'):
                    print(f"Response text: {e.response.text}")
        elif isinstance(e, websockets.exceptions.InvalidHandshake):
            print("=== InvalidHandshake Exception Details ===")
            if hasattr(e, 'status_code'):
                print(f"Status code: {e.status_code}")

if __name__ == "__main__":
    asyncio.run(test_exception())