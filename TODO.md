- [OK]对server进行重构，将bridge和forward分开，有共用的代码可以放到server_common中
- [OK]server端对长时间没有使用的缓存需要有清理机制（如consumer/provider已经down了但没来得及发通知，重构，老代码不再追查）
- 将一些debug的日志中冗余的DEBUG文字去除，还有一些为验证多加的一些debug日志，问一下是否需要删除?是否影响性能？
- [OK]某种情况下，service-id会连接不成功或运行一段时间成功会后又不成功，在win11-gpu连接时，尤其当gpu启动多个provider时，启动一个一般没问题（怀疑是同一个server下多个service-id有问题？已经重构不再追查）
- 将源代码中的注释、日志输出等都修改为英文
- [OK]保留对connect_id的使用，虽然当前使用websocket可能不用，但示来扩展到http必须使用这个逻辑
- 合理规范文件目录，如lib/mod/test等
- [OK]直接实现http代理的功能，proxy（可以socks5,也可以http),像ss-rust-local一样
- [OK]homepage独立出来外围，不要写在代码中，并且显示一下版本
- 将代码中一些配置使用配置文件，有一个命令(default-conf)可以导出默认的配置文件
- [✅]hub service需要实现application级的heartbeat，1.1版本已经实现，重构proxy丢失了
- [🔧]需要考虑：## 在ping/maianloop失败时，外围重试，其实不需要清理掉所有资源，只将与server的websocket老的清理掉，再新建一个即可  
启动连接/服务注册/websocket连接失败也不要退出程序，一直重试，间隔逐渐加长，最多5分钟，即指数退避算法  
- [✅]只有hub与server需要application级的ping/pong，其它链路暂时不需要
- 同时在server端的stats也要修改一下输出（provider->hub service），如果hub-service已经超时，就清理掉，现在好像保留10分钟？
- 检查readme的更新与否
- socks5的代理模式还不行
- refactor: 支持多个target转发，多端口
- refactor: 一个程序，可以综合了全部的sub command的能力
- refactor: client id使用u32，越界后重置，减少内存和带宽占用，也减少uuid生成时间
- refactor: 为写入操作添加超时（增加到15秒，适应SSH密钥交换）,在写入目标中，不应该有超时设置可以去除掉，hub_service.rs中，注意其它文件中一样处理。
- [✅]bug: 使用oracle-cloud <-> tencent-ide测试ssh时又发生找不到client id的问题，看opensshd的日志是它收到不正确的包  
直接关闭了连接。但pc2016本地测试ssh没有问题？(已经解决，是因为hubservice使用异步写入，有可能乱序，当前修改为同步写，后续再优化性能)
- tune: 进一步提升proxy/forward的性能（应该是像hub一样，建链路减少？）
- 实现 metrics 整合: 按照我之前提出的方案，开始修改代码以提供一个统一的 /metrics 和 /status 视图。
