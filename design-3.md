# WSSTun 重构设计方案 v4

## 1. 新文件结构
```
src/
├── main.rs (重新组织子命令)
├── common.rs (添加新消息类型)
├── server.rs (添加proxy路由支持)
├── client.rs (unified - 统一的客户端实现，支持forward和proxy)
├── hub_service.rs (new - hub-service子命令实现，替代bridge_provider)
├── proxy_server.rs (enhanced - 代理服务器端公共逻辑)
├── server_forward.rs (existing - 处理 /forward)
├── server_proxy.rs (new - 处理 /proxy，使用proxy_server.rs)
├── hub_service_forward.rs (new - 处理 /hub/forward)
├── hub_service_proxy.rs (new - 处理 /hub/proxy，使用proxy_server.rs)
└── 删除的文件：
    ├── bridge_consumer.rs (删除 - 已被hub_service替代)
    ├── bridge_provider.rs (删除 - 已被hub_service替代)
    ├── forward_client.rs (删除 - 合并到client.rs)
    └── proxy_client.rs (删除 - 合并到client.rs)
```

## 2. URL 端点设计
- **`/forward`** - 直接端口转发
- **`/proxy`** - 直接代理服务
- **`/hub/forward`** - Hub 端口转发
- **`/hub/proxy`** - Hub 代理服务

## 3. 子命令设计

### `server` 子命令（扩展现有）
```bash
# 启动服务器，支持所有端点
wsstun server --listen 0.0.0.0:8080
```
- 处理 `/forward`, `/proxy`, `/hub/forward`, `/hub/proxy` 所有端点

### `forward` 子命令（统一端口转发）
```bash
# 直接转发模式
wsstun forward --listen 127.0.0.1:8080 --server wss://server.com --target 192.168.1.100:80

# Hub 转发模式
wsstun forward --listen 127.0.0.1:8080 --server wss://server.com --target 192.168.1.100:80 --service-id my-service

# 带认证
wsstun forward --listen 127.0.0.1:8080 --server wss://server.com --target 192.168.1.100:80 --username user --password pass
```

### `proxy` 子命令（统一代理）
```bash
# 直接代理模式
wsstun proxy --listen 127.0.0.1:1080 --server wss://server.com

# Hub 代理模式
wsstun proxy --listen 127.0.0.1:1080 --server wss://server.com --service-id my-proxy

# 带认证
wsstun proxy --listen 127.0.0.1:1080 --server wss://server.com --username user --password pass
```

### `server` 子命令
```bash
# 基本服务器（支持所有端点：/forward, /proxy, /hub/forward, /hub/proxy）
wsstun server --listen 0.0.0.0:8080

# 高性能配置
wsstun server --listen 0.0.0.0:8080 \
    --max-connections 5000 \
    --keep-alive true \
    --dns-cache-size 2000 \
    --connection-timeout 15
```

### `hub-service` 子命令（统一服务提供者）
```bash
# 注册为服务提供者（支持动态转发和代理）
wsstun hub-service --server wss://server.com --service-id my-service
```

## 4. 架构流程

### 直接端口转发
```
客户端应用 → forward_client → /forward → server_forward → 指定目标服务
```

### 直接代理
```
客户端应用 → proxy_client → /proxy → server_proxy → 动态目标服务
```

### Hub 端口转发
```
客户端应用 → forward_client → /hub/forward → hub_service_forward → hub → hub_service → 指定目标服务
```

### Hub 代理
```
客户端应用 → proxy_client → /hub/proxy → hub_service_proxy → hub → hub_service → 动态目标服务
```

> 💡 **详细的数据流图和交互时序图请参考：[traffic-flow.md](./traffic-flow.md)**

## 5. URL 构建逻辑

### forward_client.rs 中的逻辑
```rust
fn build_websocket_url(base_url: &str, service_id: Option<&str>) -> String {
    match service_id {
        Some(_) => format!("{}/hub/forward", base_url),
        None => format!("{}/forward", base_url),
    }
}
```

### proxy_client.rs 中的逻辑
```rust
fn build_websocket_url(base_url: &str, service_id: Option<&str>) -> String {
    match service_id {
        Some(_) => format!("{}/hub/proxy", base_url),
        None => format!("{}/proxy", base_url),
    }
}
```

## 6. 参数设计

### server 子命令
```rust
Server {
    listen: String,                    // 监听地址
    max_connections: u32,              // 最大并发连接数（默认1000）
    keep_alive: bool,                  // 是否启用HTTP Keep-Alive（默认true）
    keep_alive_timeout: u64,           // Keep-Alive超时时间秒（默认30）
    dns_cache_size: u32,               // DNS缓存大小（默认1000）
    dns_cache_ttl: u64,                // DNS缓存TTL秒（默认300）
    connection_timeout: u64,           // 连接超时时间秒（默认10）
    buffer_size: usize,                // 数据缓冲区大小（默认32KB）
}
```

### forward 子命令
```rust
Forward {
    listen: String,                    // 本地监听地址
    server: String,                    // WebSocket 服务器 URL (不含路径)
    target: String,                    // 目标地址（必须）
    service_id: Option<String>,        // 服务ID（Hub模式）
    username: Option<String>,          // 认证用户名
    password: Option<String>,          // 认证密码
    reconnect_attempts: u32,           // 重连次数
    reconnect_delay: u64,              // 重连延迟
}
```

### proxy 子命令
```rust
Proxy {
    listen: String,                    // 本地监听地址
    server: String,                    // WebSocket 服务器 URL
    service_id: Option<String>,        // 服务ID（Hub模式）
    username: Option<String>,          // 认证用户名
    password: Option<String>,          // 认证密码
}
```

### hub-service 子命令
```rust
HubService {
    server: String,                    // WebSocket 服务器 URL (不含路径)
    service_id: String,                // 服务ID（必须）
}
```

## 7. 消息协议设计

### 新增消息类型
```rust
// Hub 转发请求（包含目标地址）
#[serde(rename = "hub_forward_request")]
HubForwardRequest {
    service_id: String,
    target_addr: String,               // 由客户端指定
},

// Hub 代理请求（不包含目标地址）
#[serde(rename = "hub_proxy_request")]  
HubProxyRequest {
    service_id: String,
    // target_addr 由代理协议解析后单独发送
},

// 服务端分发指令到 hub-service
#[serde(rename = "service_forward_instruction")]
ServiceForwardInstruction {
    connection_id: String,
    instruction_type: String,          // "target" 或 "proxy"
    target_addr: String,               // 目标地址
},

// hub-service 响应
#[serde(rename = "service_instruction_ack")]
ServiceInstructionAck {
    connection_id: String,
    success: bool,
    error_message: Option<String>,
},
```

## 8. 文件职责详解

### `forward_client.rs`
- 实现 forward 子命令
- 根据 service_id 参数构建正确的 WebSocket URL
- 处理直接转发和 Hub 转发两种模式
- 发送目标地址给服务端

### `proxy_client.rs`
- 实现 proxy 子命令
- 根据 service_id 参数构建正确的 WebSocket URL
- SOCKS5/HTTP 协议解析
- 处理直接代理和 Hub 代理两种模式

### `hub_service.rs`
- 实现 hub-service 子命令（替代 bridge_provider）
- 连接到 `/hub/forward` 或 `/hub/proxy` 端点注册服务
- 接收服务端分发的指令：
  - `target` 指令：连接到指定目标地址
  - `proxy` 指令：使用代理服务器逻辑处理
- 使用 `proxy_server.rs` 公共逻辑处理代理请求

### `client.rs`
- 统一实现 forward 和 proxy 子命令
- 根据子命令类型构建不同的WebSocket URL路径
- 共享连接管理、认证、重连等逻辑
- forward模式：直接TCP转发
- proxy模式：先解析代理协议再转发

### `proxy_server.rs`
- 代理服务器端公共逻辑
- SOCKS5/HTTP 协议处理和连接管理
- 可被 `server_proxy.rs` 和 `hub_service_proxy.rs` 复用

### `server_proxy.rs`
- 处理 `/proxy` WebSocket 连接
- 使用 `proxy_server.rs` 公共逻辑
- 直接连接动态目标地址

### `hub_service_forward.rs`
- 处理 `/hub/forward` WebSocket 连接
- 解析客户端发送的目标地址
- 分发 `target` 指令给对应的 hub-service

### `hub_service_proxy.rs`
- 处理 `/hub/proxy` WebSocket 连接
- 建立代理连接后等待代理协议解析
- 分发 `proxy` 指令给对应的 hub-service

## 9. 工作流程详解

### Hub 端口转发流程
1. 客户端：`forward --target 192.168.1.100:80 --service-id my-service`
2. 连接到：`wss://server.com/hub/forward`
3. 发送：`HubForwardRequest { service_id: "my-service", target_addr: "192.168.1.100:80" }`
4. 服务端路由到对应的 hub-service
5. 服务端发送：`ServiceForwardInstruction { instruction_type: "target", target_addr: "192.168.1.100:80" }`
6. hub-service 连接到 `192.168.1.100:80` 并开始转发

### Hub 代理流程
1. 客户端：`proxy --service-id my-proxy`
2. 连接到：`wss://server.com/hub/proxy`
3. 发送：`HubProxyRequest { service_id: "my-proxy" }`
4. 服务端建立与 hub-service 的连接
5. 客户端发送实际代理请求（SOCKS5/HTTP）
6. 服务端解析代理协议得到目标地址
7. 服务端发送：`ServiceForwardInstruction { instruction_type: "proxy", target_addr: "example.com:80" }`
8. hub-service 连接到目标地址并开始代理转发

### 直接代理流程
1. 客户端：`proxy` (无 service-id)
2. 连接到：`wss://server.com/proxy`
3. 客户端发送实际代理请求（SOCKS5/HTTP）
4. 服务端解析代理协议得到目标地址
5. 服务端直接连接目标地址并开始转发

### 直接转发流程
1. 客户端：`forward --target 192.168.1.100:80` (无 service-id)
2. 连接到：`wss://server.com/forward?target=192.168.1.100:80`
3. 服务端直接连接到指定目标地址并开始转发

### 统一代理流程
1. 服务器端：`server --listen 0.0.0.0:8080`
2. 客户端：`proxy --listen 127.0.0.1:1080 --server wss://server.com`
3. 连接到：`wss://server.com/proxy`
4. 客户端发送HTTP/SOCKS5代理请求到服务器
5. 服务器解析代理协议并连接目标服务器
6. 客户端和服务器端逻辑简化，代码复用率高

## 10. 使用场景示例

### 场景1：简单端口转发
```bash
# 服务器
wsstun server --listen 0.0.0.0:8080

# 客户端转发
wsstun forward --listen 127.0.0.1:8080 --server wss://server.com --target 192.168.1.100:80
```

### 场景2：简单代理
```bash
# 服务器
wsstun server --listen 0.0.0.0:8080

# 客户端代理
wsstun proxy --listen 127.0.0.1:1080 --server wss://server.com
```

### 场景2b：高性能代理
```bash
# 服务器（启用性能优化）
wsstun server --listen 0.0.0.0:8080 --max-connections 5000 --keep-alive true

# 客户端代理
wsstun proxy --listen 127.0.0.1:1080 --server wss://server.com
```

### 场景3：分布式转发网络
```bash
# 服务器
wsstun server --listen 0.0.0.0:8080

# 服务提供者
wsstun hub-service --server wss://server.com --service-id backend-service

# 客户端通过 hub 转发
wsstun forward --listen 127.0.0.1:8080 --server wss://server.com --target 192.168.1.100:3000 --service-id backend-service
```

### 场景4：分布式代理网络
```bash
# 中心服务器
wsstun server --listen 0.0.0.0:8080

# 代理服务提供者（美国节点）
wsstun hub-service --server wss://server.com --service-id us-proxy

# 客户端通过 hub 使用代理
wsstun proxy --listen 127.0.0.1:1080 --server wss://server.com --service-id us-proxy
```

## 11. 关键设计特点

1. **统一的参数逻辑**：所有客户端子命令都使用相同的 --server 参数（不含路径）
2. **智能路由**：根据 service_id 参数自动构建正确的 WebSocket URL
3. **动态目标地址**：target 地址由客户端指定，不是 provider 启动时固定
4. **指令分发**：服务端向 hub-service 分发 target 或 proxy 指令
5. **代码复用**：proxy_server.rs 提供公共代理逻辑
6. **协议分离**：代理协议解析与目标地址分发分离处理

## 12. 实现优先级

### 第一阶段：基础重构
1. 创建新的文件结构
2. 实现 `proxy_server.rs` 公共逻辑
3. 重构 `main.rs` 子命令结构
4. 扩展 `common.rs` 消息类型

### 第二阶段：直接模式
1. 实现 `forward_client.rs`
2. 实现 `proxy_client.rs` 
3. 实现 `server_proxy.rs`
4. 测试直接转发和代理功能

### 第三阶段：Hub 模式
1. 实现 `hub_service.rs`
2. 实现 `hub_service_forward.rs`
3. 实现 `hub_service_proxy.rs`
4. 测试分布式转发和代理功能

### 第四阶段：优化和测试
1. 性能优化
2. 错误处理完善
3. 全面测试
4. 文档更新 

## 13. 性能优化架构分析

### 当前架构性能问题

#### Direct Proxy模式性能瓶颈
```
客户端应用 → wsstun proxy client → WebSocket → wsstun server → 目标服务器
```

**主要问题**：
1. **协议解析开销**：每个连接都需要解析SOCKS5/HTTP协议（~200-500μs开销）
2. **连接建立延迟**：双重握手增加延迟
3. **内存拷贝开销**：频繁的小块数据拷贝和缓冲区操作
4. **锁竞争**：多个`Arc<Mutex<>>`导致的并发性能问题
5. **缺乏连接复用**：每次都重新建立到目标的连接

### 优化架构：客户端逻辑统一

#### 架构设计
```
客户端应用 → wsstun client (forward/proxy) → WebSocket → wsstun server → 目标服务器
```

#### 统一优势分析

**1. 客户端代码统一**
- forward和proxy客户端逻辑完全一致
- 只有WebSocket URL路径不同（/forward vs /proxy）
- 大幅减少代码重复和维护成本

**2. 服务器端协议处理**
- 服务器端根据不同端点处理不同协议
- /forward端点：直接TCP转发
- /proxy端点：先解析代理协议再转发

**3. 架构简化**
- 客户端不需要理解代理协议
- 减少客户端复杂度和资源占用
- 统一的连接管理和错误处理

**4. 部署简化** 
- 客户端配置更加简单
- 服务器端提供所有协议支持
- 减少用户学习成本

#### 实施方案

**统一客户端实现**：
```rust
// 统一的客户端配置，支持forward和proxy
ClientConfig {
    command_type: CommandType,         // Forward | Proxy
    listen: String,                    // 监听地址
    server: String,                    // WebSocket服务器URL
    target: Option<String>,            // 目标地址（仅forward需要）
    service_id: Option<String>,        // 服务ID（Hub模式）
    username: Option<String>,          // 认证用户名
    password: Option<String>,          // 认证密码
    reconnect_attempts: u32,           // 重连次数
    reconnect_delay: u64,              // 重连延迟
}

// URL构建逻辑统一
fn build_websocket_url(config: &ClientConfig) -> String {
    let base_path = match config.command_type {
        CommandType::Forward => if config.service_id.is_some() { "/hub/forward" } else { "/forward" },
        CommandType::Proxy => if config.service_id.is_some() { "/hub/proxy" } else { "/proxy" },
    };
    format!("{}{}", config.server, base_path)
}
```

**服务器端部署**：
```bash
# 启动WSSTun服务器（支持所有端点）
wsstun server --listen 0.0.0.0:8080
```

**客户端使用**：
```bash
# forward和proxy使用相同的客户端逻辑
wsstun forward --listen 127.0.0.1:8080 --server wss://server.com --target 192.168.1.100:80
wsstun proxy --listen 127.0.0.1:1080 --server wss://server.com
```

#### 性能对比预期

| 指标 | Direct Proxy | Forward + Local Proxy | 性能提升 |
|------|--------------|----------------------|----------|
| 新连接延迟 | ~100-200ms | ~20-50ms | **60-80%** |
| 并发连接数 | ~1000 | ~5000+ | **400%+** |
| CPU使用率 | 高（协议解析） | 低（纯转发） | **70%+** |
| 内存使用 | 高（多缓冲区） | 低（直接转发） | **50%+** |
| 连接复用率 | 0% | 60-80% | **显著提升** |

#### 迁移策略

**阶段1：并行部署**
- 保持现有proxy命令功能
- 新增forward + local proxy方案
- 性能测试和对比验证

**阶段2：逐步迁移**
- 文档更新，推荐使用新架构
- 提供迁移脚本和配置示例
- 收集用户反馈

**阶段3：优化清理**
- 根据使用情况考虑是否废弃直接代理模式
- 专注优化forward模式性能
- 简化代码库维护成本

### Hub模式的优化考虑

**Hub Forward + Local Proxy**：
```
客户端 → hub forward client → WebSocket → hub server → hub service → 本地代理 → 目标服务器
```

**优势**：
- 保持分布式架构的灵活性
- 结合连接复用的性能优势
- 支持多地域代理节点部署

**实施**：
```bash
# Hub Service端
wsstun hub-service --server wss://hub.com --service-id proxy-node-us

# 客户端
wsstun proxy --listen 127.0.0.1:1080 --server wss://hub.com --service-id proxy-node-us
```

## 14. 代码重构分析

### 当前代码冗余情况

**需要清理的文件**：
- `bridge_consumer.rs` - 旧的bridge模式，已被hub-service替代
- `bridge_provider.rs` - 旧的bridge模式，已被hub-service替代  
- `client.rs` - 旧的客户端实现，已被新的client模块替代

**需要简化的功能**：
- 移除proxy子命令的--local-mode参数（已取消）
- 简化server_proxy.rs，集成proxy_server.rs的优化功能
- 统一forward和proxy的WebSocket连接逻辑

**代码复用机会**：
- `proxy_server.rs`的协议解析逻辑可直接用于server内置代理
- `common.rs`的连接管理功能需要增强
- WebSocket连接和认证逻辑可进一步统一

## 15. 应用层心跳机制

WSSTun 的应用层心跳机制专门设计用于解决负载均衡器环境下的 WebSocket 连接保活问题。该机制仅在 Hub 模式下实现，提供双向心跳以确保连接稳定性。

**详细设计文档**：[heartbeat-mechanism-design.md](./heartbeat-mechanism-design.md)

**核心特性**：
- **双向心跳**：Hub Service ↔ Hub Server 30秒间隔
- **LB穿透**：JSON文本消息避免负载均衡器拦截
- **简化重连**：失败即重连，指数退避策略
- **三层保活**：TCP Keep-Alive + WebSocket Ping/Pong + Application Heartbeat

**实现状态**：✅ 已完成基础心跳机制，🔧 待完善自动重连集成

## 16. 代码复用分析

### proxy_server.rs 复用可行性

**当前 proxy_server.rs 已包含的功能**：
✅ `parse_proxy_request()` - SOCKS5/HTTP协议解析  
✅ `connect_to_target()` - TCP连接建立  
✅ `send_proxy_response()` - 代理响应发送  
✅ 完整的协议处理逻辑  

**需要增强的功能**：
🔧 连接池管理 - 复用到相同目标的连接  
🔧 DNS缓存 - 减少域名解析延迟  
🔧 HTTP Keep-Alive - 支持持久连接  
🔧 并发控制 - 限制最大连接数  
🔧 性能监控 - 连接统计和健康检查  

### 实现策略

**proxy_client.rs 中的模式选择**：
```rust
pub async fn run_proxy_client(config: ProxyClientConfig) -> Result<()> {
    if config.local_mode {
        // 本地模式：直接使用 proxy_server.rs 逻辑
        run_local_proxy_mode(config).await
    } else {
        // 远程模式：通过 WebSocket 隧道
        run_remote_proxy_mode(config).await
    }
}
```

**proxy_server.rs 的增强**：
```rust
// 新增结构体支持本地模式优化
pub struct LocalProxyServer {
    connection_pool: ConnectionPool,
    dns_cache: DnsCache,
    config: LocalProxyConfig,
}

// 扩展现有函数支持优化特性
pub async fn connect_to_target_optimized(
    target_host: &str, 
    target_port: u16,
    pool: &ConnectionPool
) -> Result<TcpStream> {
    // 先尝试从连接池获取
    // 失败则创建新连接
}
```

### 优势分析

1. **代码复用率高**：~80%的现有代码可直接复用
2. **维护成本低**：只需维护一套代理协议逻辑
3. **功能一致性**：本地模式和远程模式行为一致
4. **渐进式优化**：可逐步添加性能优化特性

## 17. 实现状态和扩展计划

### 17.1 已实现功能（Phase 6 完成）

#### ✅ 应用层心跳机制（已完成）

**详细设计和实现状态**：[heartbeat-mechanism-design.md](./heartbeat-mechanism-design.md)

**已完成**：双向心跳发送/接收、任务生命周期管理、简化重连机制
**待完善**：CLI参数集成、心跳超时检测、自动重连完整集成、监控端点

#### ✅ Provider到HubService命名统一（已完成）

**实现内容**：
- **消息类型重命名**：
  - `ProviderHeartbeat` → `HubServiceHeartbeat`
  - `ServiceProviderDisconnectedNotification` → `ServiceHubServiceDisconnectedNotification`
- **结构体重命名**：
  - `ProviderState` → `HubServiceState`
  - `total_providers` → `total_hub_services`
- **函数重命名**：
  - `register_provider()` → `register_hubservice()`
  - `handle_provider_disconnect()` → `handle_hubservice_disconnect()`
  - `forward_consumer_to_provider()` → `forward_consumer_to_hubservice()`
  - `forward_provider_to_consumer()` → `forward_hubservice_to_consumer()`
  - `get_provider_sink()` → `get_hubservice_sink()`
  - `handle_provider_messages()` → `handle_hubservice_messages()`
- **注释和日志统一**：所有相关注释、错误消息、日志输出都统一使用HubService术语

#### ✅ 完整重连机制架构（已完成）

**实现内容**：
- **ServiceManager结构**：完整的服务状态管理
- **ReconnectState管理**：重连状态跟踪
- **完整资源清理**：
  - 停止心跳任务和消息处理任务
  - 关闭WebSocket连接
  - 清理所有活跃TCP连接
  - 清理状态数据
- **指数退避算法**：1s→2s→4s→8s...→300s最大延迟
- **自动重新注册**：重连后完整重建连接、重启任务、重新注册服务
- **持续重连**：达到最大次数后使用长间隔持续尝试

**当前状态**：架构完整，但需要进一步集成到main函数中实现完全自动重连

#### ✅ Consumer到Client命名统一（已完成 - v1.3.0）

**实现内容**：
- **消息类型重命名**：
  - `NewConsumerNotification` → `NewClientNotification`
  - `ConsumerDisconnectedNotification` → `ClientDisconnectedNotification`
  - `ConsumerConnectionEndedByTarget` → `ClientConnectionEndedByTarget`
- **字段重命名**：
  - `consumer_connection_id` → `client_connection_id`
  - `consumers` → `clients`
  - `consumer_to_service` → `client_to_service`
  - `total_consumers` → `total_clients`
- **函数重命名**：
  - `handle_consumer_disconnect()` → `handle_client_disconnect()`
  - `register_consumer()` → `register_client()`
  - `forward_consumer_to_hubservice()` → `forward_client_to_hubservice()`
  - `forward_hubservice_to_consumer()` → `forward_hubservice_to_client()`
  - `handle_consumer_messages()` → `handle_client_messages()`
- **类型重命名**：
  - `HubConsumerType` → `HubClientType`
- **注释和日志统一**：所有相关注释、错误消息、日志输出都统一使用Client术语

**技术特点**：
- 与provider→hubservice的命名统一保持一致
- 形成了完整的命名规范：HubService（服务提供者）↔ Client（服务消费者）
- 所有相关模块都同步更新：common.rs、server_hub.rs、hub_service.rs、hub_service_forward.rs、hub_service_proxy.rs
- 版本升级至v1.3.0

#### ✅ Bridge命名统一到Hub（已完成 - v1.3.0）

**实现内容**：
- **消息类型重命名**：
  - `BridgeMessage` → `HubMessage`
  - `serialize_bridge_message()` → `serialize_hub_message()`
  - `send_bridge_message_with_context()` → `send_hub_message_with_context()`
- **消息模式更新**：
  - 注释从"服务中心桥接模式"改为"服务中心Hub模式"
  - 相关函数文档和注释全面更新为Hub术语
- **代码一致性**：
  - 所有相关模块统一使用Hub命名：common.rs、server_hub.rs、hub_service.rs、hub_service_forward.rs、hub_service_proxy.rs
  - 保持与之前的provider→hubservice、consumer→client命名统一相一致
- **文档同步更新**：design-3.md中的所有相关示例代码和设计说明

**技术特点**：
- 完全向后兼容，仅是命名层面的统一
- 形成了完整的命名规范体系：Hub（架构）+ HubService（服务提供者）+ Client（服务消费者）
- 消除了Bridge这一容易混淆的术语，统一使用Hub概念
- 所有相关的函数、枚举、结构体、消息类型都保持命名一致性

### 17.2 待实现扩展功能

#### 🔧 Phase 7：完整自动重连集成

**目标**：将ServiceManager完全集成到应用生命周期中

**任务清单**：
1. **重构main函数**：
   - 集成ServiceManager到应用启动流程
   - 支持优雅的信号处理（SIGTERM/SIGINT）
   - 实现完全的自动重连，无需外部重启

2. **连接健康监控**：
   - 实现基于时间戳的心跳超时检测
   - 添加连接质量评估机制
   - 自动触发重连的多种条件

3. **状态持久化**：
   - 重连状态的持久化存储
   - 重启后恢复重连计数和状态
   - 避免重连风暴的全局协调

#### 🔧 Phase 8：性能优化增强

**目标**：提升整体性能和资源利用效率

**任务清单**：
1. **连接池管理**：
   - 实现到相同目标的TCP连接复用
   - 智能连接池大小调整
   - 连接健康检查和自动清理

2. **DNS缓存优化**：
   - 实现智能DNS缓存机制
   - 可配置的TTL和缓存大小
   - 减少域名解析延迟

3. **内存和CPU优化**：
   - 优化消息处理的内存拷贝
   - 更大的缓冲区减少系统调用
   - 异步处理的并发度优化

#### 🔧 Phase 9：监控和可观测性

**目标**：增强系统监控和故障诊断能力

**任务清单**：
1. **指标收集**：
   - 心跳成功/失败统计
   - 连接建立/断开统计
   - 数据传输量和延迟统计
   - 重连次数和成功率统计

2. **HTTP监控端点**：
   - `/metrics` - Prometheus格式指标输出
   - `/health` - 健康检查端点
   - `/status` - 详细状态信息
   - `/connections` - 活跃连接列表

3. **结构化日志**：
   - JSON格式日志输出选项
   - 统一的错误码和分类
   - 可配置的日志级别和过滤

#### 🔧 Phase 10：安全性增强

**目标**：提升系统安全性和稳定性

**任务清单**：
1. **认证增强**：
   - JWT token支持
   - API密钥管理
   - 客户端证书认证

2. **限流和防护**：
   - 连接速率限制
   - 数据传输速率限制
   - DDoS防护机制

3. **配置验证**：
   - 配置文件格式验证
   - 运行时参数校验
   - 安全配置最佳实践检查

### 17.3 已废弃功能

#### ❌ 复杂多级心跳失败处理（已简化）

**原设计**：3级失败处理机制（Warning → Error → Critical）
**当前实现**：简化为直接重连，失败则进程退出
**原因**：复杂度过高，简单重连更可靠

#### ❌ Bridge模式命名（已统一为Hub）

**原设计**：BridgeMessage、bridge_provider、bridge_consumer等命名
**当前状态**：已全部重命名为Hub相关命名
**原因**：统一术语，提升代码可读性和一致性

#### ❌ 本地代理模式（已移除）

**原设计**：proxy命令的--local-mode参数
**当前状态**：已从设计中移除
**原因**：架构简化，统一使用服务器端代理处理

## 18. 分阶段重构实施计划

### 阶段一：代码清理和客户端统一（✅ 已完成）

**目标**：删除冗余文件，统一客户端实现

**任务清单**：
1. 删除冗余文件
   - 删除 `bridge_consumer.rs` - 已被hub_service替代
   - 删除 `bridge_provider.rs` - 已被hub_service替代  
   - 删除 `forward_client.rs` - 合并到统一client.rs
   - 删除 `proxy_client.rs` - 合并到统一client.rs
   - 更新 `main.rs` 移除相关import

2. 创建统一的client.rs
   - 实现统一的`ClientConfig`结构
   - 实现`CommandType`枚举（Forward/Proxy）
   - 统一WebSocket连接逻辑
   - 统一URL构建逻辑（/forward, /proxy, /hub/forward, /hub/proxy）
   - 统一认证和重连处理

3. 更新main.rs集成统一客户端
   - forward子命令调用统一client逻辑
   - proxy子命令调用统一client逻辑
   - 传递对应的CommandType参数

4. 同步更新文档
   - 更新 `README.md` - 移除旧的bridge命令示例，说明统一架构
   - 更新 `--help` 信息 - 确保forward和proxy命令帮助正确
   - 更新 examples/ 目录 - 简化客户端使用示例
   - 更新架构文档 - 说明客户端统一设计

**验收标准**：
- [ ] 编译通过，无warning
- [ ] forward和proxy子命令功能正常
- [ ] help信息正确显示
- [ ] README和examples同步更新
- [ ] 代码大幅简化，重复率降低

### 阶段二：服务器端优化完善（✅ 已完成）

**目标**：完善服务器端实现，确保稳定性

**任务清单**：
1. 完善server_proxy.rs实现
   - 确保与proxy_server.rs良好集成
   - 集成连接池、DNS缓存等优化功能
   - 支持server命令的性能配置参数
   - 优化错误处理和连接管理
   - 添加详细日志和监控

2. 统一hub_service相关模块
   - 确保hub_service_forward.rs和hub_service_proxy.rs使用统一逻辑
   - 优化hub模式的消息处理
   - 简化分布式架构的实现

3. 完善server.rs路由处理
   - 确保所有端点正常工作
   - 优化WebSocket升级处理
   - 添加健康检查和状态监控

4. 同步更新文档
   - 更新 `README.md` - 完善服务器配置说明
   - 更新 `--help` 信息 - 确保所有参数说明正确
   - 更新 examples/ 目录 - 添加服务器部署示例
   - 添加故障排除指南

**验收标准**：
- [ ] 所有服务器端点稳定工作
- [ ] Hub模式功能正常
- [ ] 错误处理和日志完善
- [ ] 文档完整准确

### 阶段三：性能优化和增强（🔧 部分完成）

**目标**：提升整体性能，增加高级功能

**任务清单**：
1. 优化client.rs性能
   - 增大缓冲区大小，减少系统调用
   - 优化内存使用和数据拷贝
   - 改进并发处理和连接复用

2. 优化server端性能
   - 在proxy_server.rs中添加连接池支持
   - 实现DNS缓存减少解析延迟
   - 优化proxy协议解析性能

3. 增强稳定性功能
   - 改进错误恢复机制
   - 添加连接健康检查
   - 实现自适应重连策略

4. 同步更新文档
   - 更新 `README.md` - 添加性能优化说明
   - 更新 `--help` 信息 - 确保准确性
   - 更新 examples/ 目录 - 性能调优示例
   - 添加性能基准测试结果

**验收标准**：
- [ ] 性能有明显提升
- [ ] 连接稳定性改善
- [ ] 功能完整可靠
- [ ] 文档详细准确

### 阶段四：最终测试和发布准备（🔧 待完成）

**目标**：全面测试验证，准备发布

**任务清单**：
1. 全面功能测试
   - 所有子命令的端到端测试
   - 各种网络环境下的稳定性测试
   - 错误场景和恢复测试
   - 多平台兼容性验证

2. 性能基准测试
   - 与重构前版本的性能对比
   - 并发连接数和延迟测试
   - 内存使用和CPU性能测试
   - 长时间运行稳定性测试

3. 代码质量保证
   - 完整的代码审查
   - 清理unused代码和依赖
   - 统一代码风格和注释
   - 安全性检查

4. 最终文档完善
   - 完整更新 `README.md` - 新架构说明和快速开始
   - 完善 `--help` 信息 - 确保所有命令帮助准确
   - 完整的 examples/ 目录 - 覆盖所有使用场景
   - 编写架构设计文档和最佳实践指南
   - 编写从旧版本的迁移指南

**验收标准**：
- [ ] 所有功能测试通过
- [ ] 性能达到或超过预期
- [ ] 代码质量达标
- [ ] 文档完整详实
- [ ] 可以正式发布

### 阶段五：应用层心跳机制实现（✅ 已完成）

**详细设计和实现状态**：[heartbeat-mechanism-design.md](./heartbeat-mechanism-design.md)

**目标**：实现Hub和Server之间的双向应用层心跳机制，解决负载均衡器环境下的连接保活问题

**✅ 已完成**：
- 双向心跳发送/接收 (30秒间隔)
- 任务生命周期管理
- 简化重连机制 (失败即进程退出)
- 完整的ServiceManager和ReconnectState架构

**🔧 待完善**：
- CLI参数集成 (`--heartbeat-interval`, `--heartbeat-timeout`)
- 心跳超时检测和自动重连完整集成
- HTTP监控端点 (`/metrics/heartbeat`)

**技术特点**：
- JSON文本消息穿透负载均衡器
- 三层保活机制 (TCP + WebSocket + Application)
- 指数退避重连策略

**监控和日志**：
```rust
// 心跳相关日志级别设计
debug!("Sent/Received heartbeat"); // 正常心跳
info!("Heartbeat mechanism started/stopped"); // 状态变化  
warn!("Heartbeat timeout detected"); // 超时警告
error!("Heartbeat send failed"); // 发送失败
```

**验收标准**：
- [ ] Hub Service能定时发送心跳给Server（30秒间隔）
- [ ] Hub Server能发送反向心跳给Hub Service
- [ ] 双向心跳超时检测正常工作（90秒）
- [ ] 心跳中断时连接能正确清理
- [ ] 负载均衡器环境下心跳正常穿透
- [ ] 心跳相关配置参数正常工作
- [ ] 相关日志和监控功能完善
- [ ] 文档完整更新

**成功指标**：
- 连接稳定性提升：在LB环境下连接保持时间延长200%+
- 故障检测时间：从原来的90秒降低到45-60秒
- 假故障率降低：减少因LB拦截ping/pong导致的误判
- 可观测性增强：心跳日志便于运维监控和问题诊断

## 18. 重构优势总结

### 架构简化带来的收益

**代码层面**：
- 删除4个冗余文件（bridge_*.rs, *_client.rs）
- 统一客户端逻辑，减少重复代码50%+
- 简化URL构建逻辑，只需要路径区分
- 统一连接管理和错误处理

**用户体验**：
- 命令行接口保持不变，无学习成本
- 消除复杂的mode参数选择
- 配置更加简单直观
- 部署和维护更加容易

**维护成本**：
- 大幅减少代码重复和分支逻辑
- 统一的测试和调试流程
- 更少的bug和兼容性问题
- 更简单的功能扩展

**性能优化**：
- 客户端逻辑简化，资源占用更少
- 服务器端集中处理，优化空间更大  
- 服务器端可配置连接池和DNS缓存
- 统一的性能优化策略
- 更好的可观测性和监控

这种重构真正体现了"简单就是美"的设计哲学，在保持功能完整性的同时，大幅提升了代码质量和用户体验。 