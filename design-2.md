## **wsstun 隧道程序扩展设计方案：服务中心桥接 (Service Hub Bridging)**

### **1\. 项目目标扩展**

*   在原有客户端-服务器端口转发功能的基础上，增加一种新的操作模式：“服务中心桥接模式” (Service Hub Mode)。
*   在该模式下，一个特定的客户端（称为“服务提供者”或 Client B）可以向隧道服务端注册一个服务。
*   其他多个客户端（称为“服务消费者”或 Client A）可以连接到隧道服务端，并请求访问由 Client B 提供的该服务。
*   服务端将为每个成功连接的 Client A 与 Client B 之间建立一个逻辑通道，通过 WebSocket Secure (WSS) 中继两者的数据。

数据流向 (服务中心桥接模式，服务端中继)：
`[本地应用 A_n] <-> [Client A_n 本地接口] <-> [wsstun Client A_n] <-> WSS <-> [wsstun 服务端] <-> WSS <-> [wsstun Client B] <-> [Client B 本地服务]`

*注：此设计假定数据通过服务端中继。*

### **2\. 核心组件变更与新增**

此模式引入两种新的操作模式/角色，对应新的顶级子命令：
*   **服务消费者 (bridge-consumer)**
*   **服务提供者 (bridge-provider)**

#### **2.1. 服务消费者 (bridge-consumer / Client A)**

*   **功能:**
    1.  **桥接消费者模式启动**: 通过 `wsstun bridge-consumer` 子命令启动。
    2.  **服务请求与重试**: 连接到指定的隧道服务端 WebSocket URL，并请求连接到一个由 `service_id` 标识的特定服务。
        *   如果 Client B (服务提供者) 尚未注册或服务不可用，Client A 将持续重试连接到服务端并请求服务。重试次数默认为无限，重试间隔可配置 (默认3秒)。
    3.  **本地数据接口**: 监听一个本地TCP端口 (例如 `127.0.0.1:local_port`)。此端口会立即开始监听。
        *   在成功连接到 Client B 的服务之前，任何连接到此本地端口的 TCP 连接将被立即断开/拒绝。
        *   一旦与 Client B 的服务连接成功，后续的本地 TCP 连接将通过已建立的 WSS 通道转发给对应的 Client B (通过服务端中继)。
    4.  **数据接收**: 从 Client B (通过服务端中继) 接收到的数据，将同样转发给连接到此本地TCP端口的本地应用。
    5.  **服务端指令处理**: 理解和响应来自服务端的、特定于此模式的控制消息 (例如，连接成功/失败、服务提供者断开等)。
    6.  **错误处理**: 处理连接服务失败、服务提供者断开、本地TCP连接错误等。

*   **命令行接口示例 (Client A - Consumer):**
    `wsstun bridge-consumer --listen 127.0.0.1:8080 --server wss://your.server.com/hub --service-id <UNIQUE_SERVICE_ID> --retry-interval 3000`
    *   `bridge-consumer`: 表明以服务消费者模式启动。
    *   `--listen <ADDR>`: Client A 本地监听的TCP地址和端口。
    *   `--server <URL>`: 隧道服务端的 WebSocket URL (应指向服务端为此模式设定的特定路径，例如 `/hub`)。
    *   `--service-id <ID>`: 希望连接的、由 Client B 提供的服务的唯一标识符。
    *   `--retry-interval <MS>` (可选): 连接服务端/服务失败后的重试间隔时间（毫秒）。默认为3000ms。重试次数默认为无限。

#### **2.2. 服务提供者 (bridge-provider / Client B)**

*   **功能:**
    1.  **桥接提供者模式启动**: 通过 `wsstun bridge-provider` 子命令启动。
    2.  **服务注册**: 连接到指定的隧道服务端 WebSocket URL，并注册一个由 `service_id` 标识的服务。
    3.  **本地服务对接**: 将通过 WSS 从 Client A (经由服务端) 收到的数据，转发到其本地运行的一个目标 TCP 服务 (例如 `127.0.0.1:target_port`)。
    4.  **多消费者处理**: 能够同时处理来自多个不同 Client A 的连接和数据流。对于每一个连接的 Client A，Client B 会与其本地目标服务建立一个独立的TCP连接进行数据交换。
    5.  **数据发送**: 从本地目标服务读取的数据，需要能够被正确地路由回对应的 Client A (通过服务端中继)。
    6.  **服务端指令处理**: 理解和响应来自服务端的控制消息 (例如，新的消费者连接、消费者断开等)。
    7.  **错误处理**: 处理服务注册失败、本地目标服务连接错误、与服务端的WSS连接错误等。

*   **命令行接口示例 (Client B - Provider):**
    `wsstun bridge-provider --target 127.0.0.1:3000 --server wss://your.server.com/hub --service-id <UNIQUE_SERVICE_ID>`
    *   `bridge-provider`: 表明以服务提供者模式启动。
    *   `--target <ADDR>`: Client B 本地运行的目标TCP服务的地址和端口。
    *   `--server <URL>`: 隧道服务端的 WebSocket URL (同 Client A，应指向 `/hub` 路径)。
    *   `--service-id <ID>`: Client B 用来注册其服务的唯一标识符。Client A 将使用此 ID 来连接。

#### **2.3. 隧道服务端 (Tunnel Server) - 服务中心角色**

*   **新增功能:** (服务端逻辑基本不变，仅强调其角色)
    1.  **服务中心路径**: 在 WebSocket 服务上提供一个新的路径 (例如 `/hub`) 专门用于处理此桥接模式的请求。
    2.  **服务提供者 (Client B) 注册**: (同前)
    3.  **服务消费者 (Client A) 连接请求处理**: (同前)
    4.  **数据中继与路由**: (同前)
    5.  **状态管理与清理**: (同前)
    6.  **命令行接口**: 服务端启动命令保持不变，例如 `wsstun server --listen 0.0.0.0:8001`。新功能通过客户端 (`bridge-consumer`, `bridge-provider`) 连接到特定路径 (`/hub`) 来激活。

*   **WebSocket 控制消息协议 (基于 JSON 的 TextMessage, 示例)**: (保持不变，见上一版本)

    *   **Client B (Provider) -> 服务端**: `RegisterService`
    *   **服务端 -> Client B (Provider)**: `ServiceRegisteredAck`, `NewConsumerNotification`, `ConsumerDisconnectedNotification`
    *   **Client A (Consumer) -> 服务端**: `ConnectToServiceRequest`
    *   **服务端 -> Client A (Consumer)**: `ServiceConnectionAck`, `ServiceUnavailableNotification`, `ServiceProviderDisconnectedNotification`, `ErrorNotification`

### **3\. 设计细节 (服务中心桥接模式)**

#### **3.1. 项目结构调整 (建议)**

```
wsstun/
├── Cargo.toml
└── src/
    ├── main.rs                     // 程序入口，参数解析，模式选择 (client, server, bridge-consumer, bridge-provider)
    ├── client.rs                   // 原有的客户端端口转发逻辑模块 (wsstun client ...)
    ├── server.rs                   // 服务端逻辑模块 (wsstun server ...)
    ├── bridge_consumer.rs          // (新增) 服务消费者 (Client A) 逻辑模块 (wsstun bridge-consumer ...)
    ├── bridge_provider.rs          // (新增) 服务提供者 (Client B) 逻辑模块 (wsstun bridge-provider ...)
    └── common.rs                   // 共享工具、错误类型、控制消息类型定义等
```
*   `main.rs` 将根据顶级子命令 (`client`, `server`, `bridge-consumer`, `bridge-provider`) 决定启动相应逻辑。
*   `server.rs` 中的 Axum 路由处理器 `/hub` 保持不变，用于服务中心桥接模式。

#### **3.2. 服务消费者逻辑 (`bridge_consumer.rs` - Client A)**

1.  **配置**: `server_url`, `service_id`, `local_listen_addr`, `retry_interval`。
2.  **共享状态**: `Arc<Mutex<Option<ConsumerConnectionId>>>` 用于表示与 Client B 服务的连接状态 (None 表示未连接或连接断开，Some包含连接ID表示已连接)。
3.  **主函数 `run_bridge_consumer(...)`**:
    *   立即启动 `tokio::net::TcpListener` 绑定到 `local_listen_addr`。
    *   **WSS 连接与服务请求循环 (后台任务/独立 Tokio 任务)**:
        a.  循环 (无限重试，间隔 `retry_interval`)：
            i.  尝试连接到 WSS 服务端 `server_url` (应为 `/hub` 路径)。
            ii. 如果连接成功，发送 `ConnectToServiceRequest` 消息 (含 `service_id`)。
            iii. 等待服务端响应：
                *   `ServiceConnectionAck`: 成功！获取 `consumer_connection_id`。更新共享状态为 `Some(consumer_connection_id)`。跳出重试循环，进入数据转发的准备阶段 (见下方)。
                *   `ServiceUnavailableNotification`/`ErrorNotification`: 服务暂时不可用或发生错误。记录日志，关闭当前WSS连接（如果已建立），等待 `retry_interval` 后继续下一次重试。
                *   WSS 连接失败：记录日志，等待 `retry_interval` 后继续下一次重试。
    *   **本地 TCP 连接处理循环 (主任务或与WSS管理并行的任务)**:
        *   异步接受本地 `listener.accept().await` -> `local_tcp_stream`。
        *   检查共享连接状态：
            *   如果共享状态为 `None` (即尚未连接到 Client B 服务): 立即关闭此 `local_tcp_stream`。
            *   如果共享状态为 `Some(consumer_connection_id)`: 派生一个新的 Tokio 任务，负责此 `local_tcp_stream`与已建立的 WSS 连接 (通过 `consumer_connection_id` 关联) 之间的数据转发。
                *   **数据转发阶段**:
                    *   分割 `local_tcp_stream` (reader/writer) 和 `ws_stream` (sink/stream) (注意: `ws_stream` 是指与服务端成功建立并完成服务请求的那个连接)。
                    *   **本地TCP -> WSS**: 读取 `local_reader` 数据，封装为 `BinaryMessage`，通过 `ws_sink` 发送给服务端。
                    *   **WSS -> 本地TCP**: 读取 `ws_stream_reader` 的 `BinaryMessage`，写入 `local_writer`。
                    *   处理 `ServiceProviderDisconnectedNotification` 或其他控制消息：优雅关闭此 `local_tcp_stream`，如果 WSS 连接因服务提供者断开而关闭，需要将共享状态重置为 `None` 并重新触发WSS连接与服务请求循环。
                    *   使用 `tokio::select!` 管理双向复制和控制消息的监听。
    *   **WSS 连接的维护**: 一旦 `ServiceConnectionAck` 收到，对应的 `ws_stream` 需要持续监听服务端的控制消息 (如 `ServiceProviderDisconnectedNotification`)。如果此 WSS 连接断开，需要将共享状态设为 `None`，并重新开始WSS连接与服务请求的重试循环。

#### **3.3. 服务提供者逻辑 (`bridge_provider.rs` - Client B)**

*   (逻辑基本保持不变，主要是命令行调用方式改变，以及确保其健壮性。)

#### **3.4. 服务端逻辑 (`server.rs` - 服务中心部分)**

*   (逻辑基本保持不变，主要是 `main.rs` 的分发逻辑调整。)

#### **3.5. 消息定义 (`common.rs`)**

*   (保持不变。)

### **4\. 安全考虑**

*   **`service_id` 的管理**: `service_id` 的唯一性和保密性（如果需要）由用户协调。可以考虑服务端对 `service_id` 的格式或命名空间进行管理。
*   **认证与授权**: 服务端 `/hub` 路径应考虑与 `/tunnel` 路径类似的认证机制 (如HTTP头token)。Client B 注册服务、Client A 连接服务前都应通过认证。
*   **服务端资源**: 防止恶意创建大量服务或连接请求。可考虑速率限制、连接数限制。
*   **数据隔离**: 服务端必须严格确保不同 `service_id` 之间以及同一 `service_id` 下不同 `consumer_connection_id` 之间的数据隔离和正确路由。

### **5\. 未来可能的增强功能**

*   **支持一次启动多个隧道**: Client B可支持，多个target地址，Client A也可以指定连接哪个Client B的target地址
*   **服务端列出可用服务**: Client A 可以查询当前已注册的服务列表 (需考虑安全和隐私)。
*   **服务发现机制**: 更动态的服务注册和发现机制。
*   **Client B 的负载均衡/冗余**: 允许多个 Client B 实例为同一个 `service_id` 提供服务。
*   **端到端加密 (E2EE)**: 如果需要，可以在 Client A 和 Client B 的应用层之间实现，加密数据作为 `BinaryMessage` 通过服务端中继。
*   **流控**: 实现有效的流量控制，防止因某个 Client A 或本地服务问题导致资源耗尽。
*   **provider重试**: 当server下线后，provider要进行重连接，期间要做保活检查。

