# WSSTun 连接断开传播机制设计方案

## 🔗 链路架构分析

当前的WSSTun架构有以下链路：

```
Client ↔ Server ↔ Hub Service ↔ Target
```

**现有问题**: 任何一个环节的断开都可能导致其他环节无法及时感知，形成"僵尸连接"或数据发送到已断开的连接。

## 🔍 断开传播问题分析

### 正向数据流：Client → Server → Hub Service → Target

### 1. Target -> Hub Service 方向（主要问题）

**当前状态**: ❌ 断开传播缺失
- `forward_target_to_websocket`函数检测到EOF后，仅清理本地状态
- **没有向Server发送`ClientConnectionEndedByTarget`通知**
- 导致Server和Client继续发送数据到已断开的目标

**代码位置**: `src/hub_service.rs:624-698`

### 2. Hub Service -> Server 方向

**当前状态**: ✅ 断开传播正常
- Server的`handle_hubservice_messages`函数监听WebSocket关闭
- 调用`handle_hubservice_disconnect`通知所有客户端
- 发送`ServiceHubServiceDisconnectedNotification`给客户端

**代码位置**: `src/server_hub.rs:751-794`

### 3. Server -> Client 方向

**当前状态**: ✅ 断开传播正常
- Server处理客户端断开后调用`handle_client_disconnect`
- 发送`ClientDisconnectedNotification`给Hub Service
- Hub Service清理对应的目标连接

**代码位置**: `src/server_hub.rs:82-134`

### 4. Client -> Server 方向

**当前状态**: ✅ 断开传播正常
- Client的`forward_bidirectional_optimized`函数检测本地TCP EOF
- WebSocket自动关闭，Server的`handle_client_messages`检测到并清理
- 各个handle_client_messages函数监听WebSocket关闭

**代码位置**: `src/client.rs:490-500`, `src/server_hub.rs:820-949`

### 反向数据流：Target ← Hub Service ← Server ← Client

### 5. Client断开 -> Server处理

**当前状态**: ✅ 断开传播正常
- Client的本地TCP连接断开时，`forward_bidirectional_optimized`中的t1任务检测到EOF
- WebSocket连接自动关闭，触发Server端的`handle_client_messages`
- Server调用`handle_client_disconnect`，发送`ClientDisconnectedNotification`

**代码位置**: `src/client.rs:490-500`, `src/server_hub.rs:82-134`

### 6. Server断开 -> Hub Service处理

**当前状态**: ✅ 断开传播正常
- Server检测到Client断开后，发送`ClientDisconnectedNotification`给Hub Service
- Hub Service在`handle_control_message`中处理，清理对应的Target连接

**代码位置**: `src/hub_service.rs:408-427`

### 7. Hub Service -> Target 写入失败（新发现的问题）

**当前状态**: ⚠️ 部分缺失
- `handle_binary_message`函数中，向Target写入失败时**只记录错误日志**
- **没有清理连接状态或通知Server**
- 可能导致后续数据继续发送到已断开的Target连接

**代码位置**: `src/hub_service.rs:470-490`

**问题代码**:
```rust
Ok(Err(e)) => {
    error!("向目标写入数据失败，客户端 {}: {}", client_id_for_log, e);
    // ❌ 缺少：清理连接状态和通知Server
}
Err(_) => {
    error!("向目标写入数据超时（15秒），客户端 {}, 数据长度: {}", 
           client_id_for_log, payload_len);
    // ❌ 缺少：清理连接状态和通知Server
}
```

### 8. Server -> Hub Service 写入失败（新发现的问题）

**当前状态**: ⚠️ 部分缺失
- `handle_client_messages`函数中，调用`forward_client_to_hubservice`失败时**只记录错误**
- **没有主动断开客户端连接**
- 只有在特定错误（client未注册）时才会断开，其他WebSocket错误继续运行

**代码位置**: `src/server_hub.rs:860-880`

**问题代码**:
```rust
if let Err(e) = service_hub.read().await.forward_client_to_hubservice(&client_id, data.to_vec()).await {
    error!("Failed to forward data to hub service: {}", e);
    
    // ❌ 只处理特定错误类型，WebSocket错误没有断开连接
    if let TunnelError::Other(msg) = &e {
        if msg.contains("Client") && msg.contains("is not registered") {
            break;
        }
    }
    // ❌ 其他错误（如WebSocket发送失败）继续运行
}
```

## 🛠️ 完整断开传播设计方案

### 方案1: Target -> Hub Service -> Server -> Client

#### 1.1 Target断开时的处理流程

```rust
// 在 forward_target_to_websocket 函数中
Ok(0) => {
    debug!("目标连接关闭 (EOF) - 连接ID: {}", connection_id);
    
    // 🔧 新增：发送断开通知给Server
    let disconnect_msg = HubMessage::ClientConnectionEndedByTarget {
        client_connection_id: connection_id.clone(),
    };
    
    if let Ok(json) = serde_json::to_string(&disconnect_msg) {
        let mut sink = ws_sink.lock().await;
        if let Err(e) = sink.send(Message::Text(json.into())).await {
            error!("发送目标断开通知失败: {}", e);
        } else {
            debug!("已发送目标断开通知 - 连接ID: {}", connection_id);
        }
    }
    
    break;  // 退出读取循环
}
```

#### 1.3 Hub Service写入Target失败时的处理

```rust
// 在 handle_binary_message 函数中
tokio::spawn(async move {
    let mut should_disconnect = false;
    let result = tokio::time::timeout(Duration::from_secs(15), async {
        let mut writer = target_writer.lock().await;
        writer.write_all(&payload_owned).await
    }).await;
    
    match result {
        Ok(Ok(())) => {
            debug!("已转发 {} 字节到目标连接，客户端: {}", payload_len, client_id_for_log);
        }
        Ok(Err(e)) => {
            error!("向目标写入数据失败，客户端 {}: {}", client_id_for_log, e);
            should_disconnect = true;
        }
        Err(_) => {
            error!("向目标写入数据超时（15秒），客户端 {}, 数据长度: {}", 
                   client_id_for_log, payload_len);
            should_disconnect = true;
        }
    }
    
    // 🔧 新增：如果写入失败，清理连接并通知Server
    if should_disconnect {
        // 清理连接状态
        {
            let mut state = service_state.lock().await;
            state.active_connections.remove(&client_id_for_log);
            state.pending_data.remove(&client_id_for_log);
        }
        
        // 发送断开通知给Server
        let disconnect_msg = HubMessage::ClientConnectionEndedByTarget {
            client_connection_id: client_id_for_log.clone(),
        };
        
        if let Ok(json) = serde_json::to_string(&disconnect_msg) {
            let mut sink = ws_sink.lock().await;
            if let Err(e) = sink.send(Message::Text(json.into())).await {
                error!("发送目标断开通知失败: {}", e);
            } else {
                debug!("已发送目标写入失败断开通知 - 连接ID: {}", client_id_for_log);
            }
        }
    }
});
```

#### 1.2 Server接收到目标断开通知的处理

```rust
// 在 handle_hubservice_messages 的 Message::Text 分支中
HubMessage::ClientConnectionEndedByTarget { client_connection_id } => {
    info!("收到目标连接断开通知 - 客户端: {}", client_connection_id);
    
    // 🔧 新增：主动关闭对应的客户端连接
    let client_sink = {
        let hub = service_hub.read().await;
        hub.hub_services.get(&service_id)
            .and_then(|hs| hs.clients.get(&client_connection_id))
            .map(|sink| Arc::clone(sink))
    };
    
    if let Some(sink) = client_sink {
        let mut client_sink = sink.lock().await;
        // 发送关闭帧给客户端
        let _ = client_sink.close().await;
        info!("已主动关闭客户端连接 - 连接ID: {}", client_connection_id);
    }
    
    // 清理连接状态
    let mut hub = service_hub.write().await;
    hub.handle_client_disconnect(&client_connection_id).await;
}
```

### 方案2: Client -> Server -> Hub Service -> Target

#### 2.1 客户端断开的优化处理

**当前状态**: ✅ 已经正常工作
- Client WebSocket关闭触发Server的`handle_client_disconnect`
- Server发送`ClientDisconnectedNotification`给Hub Service
- Hub Service清理目标连接

#### 2.2 增强清理的完整性

```rust
// 在 hub_service.rs 的 handle_control_message 中
HubMessage::ClientDisconnectedNotification { client_connection_id } => {
    info!("客户端断开连接: {}", client_connection_id);

    // 🔧 增强：更完整的清理逻辑
    let mut state = service_state.lock().await;
    if let Some(connection_state) = state.active_connections.remove(&client_connection_id) {
        match connection_state {
            ConnectionState::Connected { reader, writer } => {
                // TCP连接会在Drop时自动关闭，但可以显式关闭以确保及时释放
                drop(reader);
                drop(writer);
                debug!("已清理连接ID {} 的TCP连接", client_connection_id);
            }
            _ => {}
        }
    }
    
    // 清理缓存数据
    if let Some(pending_data) = state.pending_data.remove(&client_connection_id) {
        debug!("清理了连接ID {} 的 {} 个缓存数据包", 
               client_connection_id, pending_data.len());
    }
}
```

### 方案3: Hub Service -> Server 双向断开

#### 3.1 Hub Service意外断开的处理

**当前状态**: ✅ 已经正常工作
- Server检测到WebSocket关闭
- 调用`handle_hubservice_disconnect`
- 通知所有连接的客户端并清理状态

#### 3.2 增强：Server -> Hub Service断开通知

```rust
// 在 server 侧，当检测到Hub Service不健康时
let disconnect_msg = HubMessage::ServerHeartbeat {
    server_id: "server_shutdown".to_string(),
    timestamp: current_timestamp,
    active_services: 0,  // 0表示服务器即将关闭
};

// 发送给Hub Service，让其主动断开所有连接
```

## 📊 断开传播状态矩阵

| 断开方向 | 数据流方向 | 当前状态 | 需要修复 | 优先级 |
|----------|------------|----------|----------|---------|
| Target → Hub Service | 正向 | ❌ 缺失 | ✅ 是 | 🔥 高 |
| Hub Service → Server | 正向 | ✅ 正常 | ❌ 否 | - |
| Server → Client | 正向 | ✅ 正常 | ❌ 否 | - |
| Client → Server | 反向 | ✅ 正常 | ❌ 否 | - |
| Server → Hub Service | 反向 | ✅ 正常 | ❌ 否 | - |
| Hub Service → Target写入失败 | 反向 | ⚠️ 部分缺失 | ✅ 是 | 🔥 高 |
| Server → Hub Service写入失败 | 反向 | ⚠️ 部分缺失 | ✅ 是 | 🔶 中 |
| Hub Service WebSocket断开 | 双向 | ✅ 正常 | ❌ 否 | - |

## 🔄 完整的断开传播流程

### 流程1: Target端断开

```
Target关闭 
  ↓
Hub Service检测到EOF
  ↓
发送ClientConnectionEndedByTarget给Server
  ↓
Server收到通知，主动关闭Client WebSocket
  ↓
Client检测到WebSocket关闭，清理本地TCP连接
```

### 流程2: Client端断开

```
Client关闭本地TCP
  ↓
Client WebSocket任务结束
  ↓
Server检测到Client WebSocket关闭
  ↓
发送ClientDisconnectedNotification给Hub Service
  ↓
Hub Service清理对应的Target TCP连接
```

### 流程3: Hub Service断开

```
Hub Service异常/重启
  ↓
Server检测到Hub Service WebSocket关闭
  ↓
向所有Client发送ServiceHubServiceDisconnectedNotification
  ↓
各Client收到通知，关闭本地TCP连接
```

### 流程4: Server断开

```
Server异常/重启
  ↓
所有WebSocket连接断开
  ↓
Client和Hub Service检测到连接关闭
  ↓
各自清理本地资源和TCP连接
```

## 🎯 实现优先级

### 立即修复（高优先级）
1. **Target → Hub Service → Server 的断开通知**
   - 修复`forward_target_to_websocket`函数
   - 添加`ClientConnectionEndedByTarget`消息发送
   - 让Server主动关闭对应的Client连接

2. **Hub Service → Target 写入失败的处理**
   - 修复`handle_binary_message`函数中的错误处理
   - 写入失败或超时时清理连接状态
   - 发送`ClientConnectionEndedByTarget`通知给Server

### 后续增强（中优先级）
3. **Server → Hub Service 写入失败的处理**
   - 修复`handle_client_messages`函数中的错误处理
   - 所有转发错误都应该断开客户端连接
   - 不只是处理特定的错误类型

4. **增强清理逻辑的完整性**
   - 添加更详细的连接状态跟踪
   - 确保所有资源都被正确释放
   - 添加断开通知的确认机制

### 可选优化（低优先级）
5. **添加超时和重试机制**
   - 断开通知的超时处理
   - 连接健康检查
   - 自动重连逻辑

## 🔍 验证方案

### 验证1: Target端断开测试
1. 建立完整连接：Client → Server → Hub Service → Target
2. 主动关闭Target端TCP连接
3. 观察Client是否及时收到断开通知
4. 确认所有中间环节都正确清理了状态

### 验证2: 各环节断开测试
1. 分别测试Client、Server、Hub Service的主动断开
2. 验证断开通知是否按预期传播
3. 确认没有遗留的"僵尸连接"

### 验证3: 网络异常测试
1. 模拟网络中断场景
2. 验证超时机制是否正常工作
3. 确认重连后状态一致性

## 📝 实现注意事项

1. **消息发送要处理失败情况**: 断开通知发送失败时的降级策略
2. **避免循环断开**: 防止断开通知引起的连锁反应
3. **状态一致性**: 确保所有环节的状态保持同步
4. **性能考虑**: 断开处理不应阻塞正常的数据转发
5. **日志记录**: 详细记录断开事件，便于调试和监控

## 🚨 关键修复点

**最重要的修复**就是在`src/hub_service.rs`的`forward_target_to_websocket`函数中，当检测到`Ok(0)`（EOF）时，**必须发送`ClientConnectionEndedByTarget`消息**给Server，这是解决当前问题的关键。

目前这个消息类型在`common.rs`中已经定义，但在Hub Service中没有使用，这就是导致SSH连接阻塞问题的根本原因。 