<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>wsstun Server</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background-color: #f4f4f4; color: #333; }
        .container { background-color: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
        h1 { color: #0056b3; }
        p { line-height: 1.6; }
        code { background-color: #eee; padding: 2px 5px; border-radius: 4px; }
        ul { list-style-type: none; padding: 0; }
        li { margin-bottom: 10px; }
        .endpoint { font-weight: bold; color: #007bff; }
        .version-info { margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-left: 4px solid #007bff; }
        .version-info h3 { margin: 0 0 10px 0; color: #495057; }
        .version-info .version { font-family: 'Courier New', monospace; color: #28a745; font-weight: bold; }
        .build-time { color: #6c757d; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <h1>wsstun Server is Running</h1>
        <p>This is the main page for the wsstun server. Below are the available WebSocket endpoints:</p>
        <ul>
            <li><span class="endpoint">/forward</span>: Endpoint for port forwarding. Clients connect here to tunnel TCP traffic. Requires a <code>target</code> query parameter (e.g., <code>/forward?target=192.168.1.100:80</code>).</li>
            <li><span class="endpoint">/hub</span>: Endpoint for the Service Hub. Bridge providers and consumers connect here to register and access services.</li>
        </ul>
        <p>For more information on how to use wsstun, please refer to the command-line help (<code>wsstun --help</code>) or the project documentation.</p>
        
        <div class="version-info">
            <h3>Version Information</h3>
            <div class="version">Version: {{VERSION}}</div>
            <div class="build-time">Build Time: {{BUILD_TIME}}</div>
        </div>
    </div>
</body>
</html> 