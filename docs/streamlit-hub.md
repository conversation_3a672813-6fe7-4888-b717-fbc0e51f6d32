# **设计文档: 利用 Streamlit Cloud 构建企业级实时数据通道**

## 1. 摘要与架构愿景

### 1.1. 目标
本文档旨在设计一个**安全、可靠、可扩展**的实时双向通讯架构。该架构利用 **Streamlit Cloud** 作为认证网关和通讯中继，以连接一个**独立的 Rust 客户端**和一个**后端 Python 业务应用**。

最终目标是为 Rust 客户端提供一个逻辑上透明的数据通道，使其能够安全地与隔离在私有网络中的后端应用进行交互，而无需将后端应用直接暴露在公网上。

### 1.2. 核心原则
- **职责分离 (Separation of Concerns)**:
    - **Rust 客户端**: 纯粹的命令发起者和数据消费者。
    - **Streamlit Cloud 应用**: **仅作为认证代理和无状态的消息路由器 (Stateless Message Router)**。它不包含任何业务逻辑，以实现最小攻击面和最大稳定性。
    - **后端 Python 应用**: 唯一的业务逻辑和数据处理中心。

- **Streamlit 即协议层 (Streamlit as a Protocol Layer)**: 我们将把 Streamlit 的原生通讯机制（`ForwardMsg`/`BackMsg`）和组件（`st.chat_input`/`st.write_stream`）视为一个可靠的、经过验证的**应用层通讯传输协议**，而非一个 UI 框架。

### 1.3. 宏观架构图
```
                                        [ Streamlit Cloud Platform ]
                                        +------------------------------------------------------+
                                        |                                                      |
                                        |  +---------------------------+                       |
+-----------------+      (WSS/HTTPS)    |  |  Streamlit App (Bridge)   |      (HTTP/WS/gRPC)     +----------------------+
|                 | <-----------------> |  |                           | <-------------------> |                      |
|   Rust Client   |                     |  |  - Authentication Proxy   |                       |  Backend Python App  |
|                 |                     |  |  - Message Router         |                       | (Private Network)    |
+-----------------+                     |  |  - Stateless              |                       |                      |
                                        |  +---------------------------+                       |
                                        |                                                      |
                                        +------------------------------------------------------+
                                             |           ^
                                             |           |
                                          [Protobuf over WebSocket]
```

## 2. 认证与会话建立 (Authentication & Session Establishment)

这是整个架构的**安全基石**。Rust 客户端必须模拟一个合法的用户浏览器，以通过 Streamlit Cloud 的认证机制。

1.  **Phase 1: HTTP(S) Handshake & Cookie Acquisition**
    - Rust 客户端使用一个支持 Cookie Jar 的 HTTP 客户端（如 `reqwest`）向 Streamlit 应用的公共 URL (e.g., `https://yourapp.streamlit.app`) 发起 `GET` 请求。
    - Streamlit Cloud 会进行响应，并设置必要的会话和 CSRF (Cross-Site Request Forgery) Cookies。客户端必须正确存储这些 Cookies。

2.  **Phase 2: Configuration Extraction**
    - 客户端解析 `GET` 请求返回的 HTML 响应体。
    - 在 HTML 的 `<script>` 标签中，寻找一个全局 JavaScript 对象，通常是 `window.streamlitConfig` 或类似的名称。
    - 从此对象中提取关键信息：
        - `appId`: 应用的唯一标识。
        - `streamUrl`: WebSocket 连接的目标 URL（通常是 `/stream`）。
        - 其他会话元数据。

3.  **Phase 3: Secure WebSocket (WSS) Connection**
    - Rust 客户端使用一个 WebSocket 库（如 `tokio-tungstenite`）发起一个到 `wss://yourapp.streamlit.app/stream` 的 WSS 连接请求。
    - **关键**: 在此 WSS 连接的 HTTP 升级请求头 (Upgrade Request Headers) 中，**必须附上 Phase 1 中获取的所有 Cookies**。
    - Streamlit Cloud 的服务器会验证这些 Cookies。只有验证通过，WebSocket 连接才会被成功建立。没有合法的 Cookies，连接将被拒绝。

## 3. 通讯协议设计

我们将设计一个两层协议：底层是 Streamlit 的原生 Protobuf 协议，上层是我们定义的基于 JSON 的应用层协议。

### 3.1. 底层传输: 聊天组件作为数据帧 (Chat as a Data Frame)
- **客户端 → 服务端 (Client to Server)**:
    - Rust 客户端将应用层 JSON 消息序列化为字符串。
    - 它构造一个 Streamlit `BackMsg` Protobuf 消息，模拟**用户在 `st.chat_input` 中输入了该字符串并提交**。
- **服务端 → 客户端 (Server to Client)**:
    - Streamlit 桥梁应用从后端接收数据。
    - 它通过 `st.write_stream` 将数据**逐块 `yield`** 出去。
    - Streamlit SDK 会将每个 `yield` 的块打包成一个 `ForwardMsg` Protobuf 消息（具体是 `Delta` 类型，包含一个 `Text` 或 `Json` 元素），并通过 WebSocket 发送给客户端。

### 3.2. 应用层协议: JSON 消息规范
所有通过聊天组件传输的字符串都必须是此格式的 JSON，以确保机器可读性。

#### 客户端请求 (Client Request)
```json
{
  "header": {
    "request_id": "uuid-v4-string",
    "client_version": "1.0.0",
    "timestamp_utc": "iso-8601-string"
  },
  "type": "ONE_TIME_QUERY" | "STREAM_SUBSCRIBE" | "EXECUTE_COMMAND",
  "payload": {
    "endpoint": "/api/v1/users",
    "params": { "id": 123 },
    "body": { /* for POST/PUT */ }
  }
}
```
- `request_id`: 用于关联请求和响应，在异步世界中至关重要。
- `type`: 定义了请求的性质。
- `payload`: 完全模仿了一个标准的 HTTP 请求（端点、参数、请求体），使得 Streamlit 桥梁可以轻松地将其转换为对后端服务的真实网络调用。

#### 服务端响应 (Server Response)
```json
{
  "header": {
    "request_id": "uuid-v4-string", // Correlates to the client request
    "source_node": "backend-service-1",
    "timestamp_utc": "iso-8601-string"
  },
  "status": "STREAMING_CHUNK" | "COMPLETED" | "ERROR",
  "payload": {
    "data": { /* ... actual data ... */ },
    "error": {
      "code": "INTERNAL_ERROR",
      "message": "Database connection failed."
    }
  }
}
```
- `status`:
    - `STREAMING_CHUNK`: 表示这是一个流式响应中的一个数据块。
    - `COMPLETED`: 表示这是一次性请求的最终且完整响应，或一个流的结束信号。
    - `ERROR`: 表示请求处理失败。

## 4. 核心工作流详解

### 4.1. 一次性数据查询 (Request/Response Workflow)
1.  **Rust Client**: 构造一个 `type: "ONE_TIME_QUERY"` 的 JSON 请求，并通过 `st.chat_input` 机制发送。
2.  **Streamlit Bridge**:
    - 从 `st.chat_input` 捕获到 JSON 字符串并反序列化。
    - 使用 `requests` 库，根据 `payload` 中的 `endpoint` 和 `params`，向后端应用 (e.g., `http://backend-service/api/v1/users?id=123`) 发起一个 HTTP GET 请求。
    - 等待后端返回完整的 HTTP 响应。
3.  **Backend App**: 处理 HTTP 请求，从数据库查询数据，返回一个包含用户信息的 JSON 响应。
4.  **Streamlit Bridge**:
    - 接收到后端的响应。
    - 构造一个 `status: "COMPLETED"` 的应用层 JSON 响应。
    - 通过 `st.write_stream` 一次性 `yield` 这个完整的响应字符串。
5.  **Rust Client**: 接收到单个 `ForwardMsg`，解析出响应 JSON，通过 `request_id` 匹配请求，任务完成。

### 4.2. 实时流订阅 (Streaming Workflow)
1.  **Rust Client**: 构造一个 `type: "STREAM_SUBSCRIBE"` 的 JSON 请求，`payload` 中包含 `{"topic": "live_market_data"}`。
2.  **Streamlit Bridge**:
    - 接收并解析请求。
    - 使用 `websockets` 或 `aiohttp` 库，与后端应用的 WebSocket 端点 (e.g., `ws://backend-service/ws/market-data`) 建立一个持久连接，并发送订阅 `topic` 的消息。
3.  **Backend App**: 接收到订阅请求，开始向这个 WebSocket 连接持续推送实时市场数据（每条都是一个小的 JSON 对象）。
4.  **Streamlit Bridge**:
    - 进入一个 `async for` 循环，监听来自后端 WebSocket 的消息。
    - **每收到一条后端消息**，就立即构造一个 `status: "STREAMING_CHUNK"` 的应用层 JSON 响应，并通过 `st.write_stream` `yield` 出去。
5.  **Rust Client**: 持续接收到一系列 `ForwardMsg`，解析出每一个数据块，并实时处理（如更新 UI、写入数据库等）。
6.  **流结束**: 当 Rust 客户端发送取消订阅的命令，或后端连接关闭时，桥梁应用可以 `yield` 一个 `status: "COMPLETED"` 的消息作为流的结束信号。

## 5. 安全、可靠性与错误处理

- **安全**:
    - **输入验证**: Streamlit 桥梁应用必须对从客户端收到的 JSON `payload` 进行严格的白名单验证。绝不能允许客户端指定任意的 `endpoint` 或 `params`，只能允许预定义的操作。
    - **无凭证传递**: 后端应用的访问凭证（API Keys, DB Passwords）绝不能存储在 Streamlit 应用的代码或 `st.secrets` 中。桥梁应用应被部署在可以安全访问后端的网络环境中（如 VPC），通过网络策略进行访问控制。
- **可靠性**:
    - **心跳机制**: 对于长时间的流式连接，桥梁应用可以设计一个定时任务，定期向客户端 `yield` 心跳消息，以确认通道存活。
    - **重连逻辑**: Rust 客户端必须实现健壮的 WebSocket 重连逻辑。如果连接断开，它应该自动重复 `Section 2` 的整个认证和会话建立过程。
- **错误处理**:
    - 后端应用的任何错误（如数据库超时、HTTP 500）都必须被桥梁应用捕获。
    - 桥梁应用应将这些错误包装成一个标准化的 `status: "ERROR"` 应用层 JSON 响应，并回传给 Rust 客户端，以便客户端能够优雅地处理失败。

## 6. 局限性与权衡
- **延迟**: 此架构引入了额外的网络中继，必然会增加端到端的延迟。它不适用于需要微秒级响应的高频交易等场景。
- **成本**: Streamlit Cloud 的资源是计费的。持续的 WebSocket 连接和数据流会消耗计算资源，需要进行成本评估。
- **复杂性**: 这是一个多组件系统，调试和日志记录需要跨越三个不同的系统，对 DevOps 提出了更高的要求。
- **吞吐量限制**: Streamlit Cloud 平台本身可能对单个应用的带宽和计算能力有限制，这可能会成为系统吞吐量的瓶颈。 