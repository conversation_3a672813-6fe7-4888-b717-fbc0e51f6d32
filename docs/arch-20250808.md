## 项目概述

- 名称：wsstun
- 版本：1.5.3（见 Cargo.toml）
- 描述：通过 WebSocket/WebSocket Secure (WSS) 连接进行流量转发的隧道应用，支持直连转发、代理、服务中心（Hub）模式、Mux 复用模式以及“动态隧道”模式
- 语言与运行时：Rust + Tokio 异步运行时

## 架构总览

- 二元角色：Client（发起本地监听、与 Server 建立 WS 隧道）与 Server（Axum Web 服务器，暴露 WS/HTTP 端点）
- 多种工作模式：
  - Forward：指定目标地址的端口转发
  - Proxy：SOCKS5/HTTP 代理（动态解析目标）
  - Hub 模式：Server 充当“服务中心”，Hub Service 注册服务，客户端经 Server 与 Hub Service 建立转发通路（两种实现：传统 MUX/统一消息；动态隧道）
  - Mux：在单条 WS 上复用多路流（Client/Server 两端都有对应实现）
  - 动态隧道：将控制通道与数据通道分离，按需为每个客户端连接创建数据隧道

示意（模块关系，仅基于代码）：

```mermaid
graph TD
  subgraph Client
    A1[client.rs]
    A2[client_mux.rs]
  end

  subgraph Server(Axum)
    B1[/forward]\nserver_forward.rs
    B2[/proxy]\nserver_proxy.rs
    B3[/mux]\nserver_mux.rs
    B4[/hub]\nserver_hub.rs
    B5[/hub/forward]\nhub_service_forward.rs
    B6[/hub/proxy]\nhub_service_proxy.rs
    B7[/hub/dynamic]\nserver_hub_dynamic.rs
    B8[/hub/data]\nserver_hub_dynamic.rs
  end

  subgraph Hub Service(SP侧)
    C1[hub_service.rs\n(传统/统一消息)]
    C2[hub_service_dynamic.rs\n(动态隧道)]
  end

  A1 -- ws --> B1 & B2
  A2 -- ws(/mux) --> B3
  C1 -- ws(/hub) <--> B4
  C2 -- ws(/hub/dynamic) <--> B7
  C2 -- ws(/hub/data) <--> B8
```

补充公共模块：
- common.rs：错误类型、连接ID编码/前缀、Origin 生成、WS 重试连接、消息协议定义（HubMessage、ClientMuxMessage、ControlMessage、DataTunnelMessage）、工具函数
- proxy_server.rs：代理协议解析、DNS 缓存、连接池与连接建立
- heartbeat.rs：应用层心跳（JSON 文本，避免被 LB 对 ping/pong 干扰）
- reconnect.rs：重连状态与信号枚举
- streamlit_client.rs：Streamlit WS 客户端示例（使用 prost/reqwest）

## 核心模块与职责

- src/main.rs
  - 命令行入口（clap）：Forward/Proxy/StreamlitClient/HubService/Server 子命令
  - 全局参数：--log-level、--use-mux（决定 Client/HubService 的实现分支）
  - 根据命令调度到 client.rs、client_mux.rs、hub_service.rs/hub_service_dynamic.rs、server.rs

- src/common.rs
  - 基础类型与错误：Result、TunnelError
  - 连接与隧道 ID：ConnectionId/TunnelId 生成器（原子计数）、4字节大端编码/解码、数据帧前缀
  - URL 与 WS：trim_server_url_slashes、prepare_ws_url、generate_origin_header、connect_websocket_with_retry
  - 消息协议：
    - HubMessage（服务注册、客户端连接通知/确认、错误/心跳、转发指令等，JSON 文本）
    - ClientMuxMessage（复用模式控制与数据相关控制消息，JSON 文本）
    - ControlMessage/DataTunnelMessage（动态隧道控制与数据通道注册，JSON 文本）
  - 序列化/发送工具：serialize_*、send_hub_message_with_context
  - 单元测试覆盖：连接ID、前缀/提取、URL 处理、Origin 头生成等

- src/client.rs（非 Mux 客户端）
  - 本地监听（TCP），接入后为每条连接建立到 Server 的 WS 隧道
  - Forward 模式：server /forward，target 作为查询参数
  - Proxy 模式：server /proxy，无 target，数据帧即代理协议
  - 基于连接池（Semaphore）控制并发与统计（ConnectionStats）
  - 重连与超时逻辑（connect_websocket_with_retry + timeout + backoff）
  - 双向转发：TCP<->WS，按配置 buffer_size 读写

- src/client_mux.rs（Mux 客户端）
  - 在 /mux 上建立多个 WS 连接，形成“写入通道池”（UnboundedSender<Message>）
  - 会话握手：InitializeSession/JoinSession，服务端返回 session_id
  - 新连接请求/响应：RequestNewStream/NewStreamResponse
  - 上行数据：对每个 TCP 流加 4 字节 connection_id 前缀后经所选 WS 发送
  - 下行数据：从服务器收到带前缀的数据，路由到对应的本地 TCP 连接
  - 维护映射：connection_id -> ws_index、active_streams、pending_responses

- src/server.rs（服务端主入口）
  - Axum 路由：/forward、/proxy、/mux、/hub、/hub/forward、/hub/proxy、/hub/dynamic、/hub/data、以及 /、/health、/status、/metrics
  - 共享状态：ServiceHub（传统 Hub）、DynamicHubState（动态隧道）、AppState（Mux 会话）
  - 统一装配 ServerConfig，并在端点处理时通过 Extension 传递

- src/server_forward.rs
  - /forward：从查询中读取 target，建立到目标的 TCP 连接，双向转发（WS<->TCP）

- src/server_proxy.rs
  - /proxy：两种路径
    - 直接模式：查询中带 target，直接连目标
    - 动态模式：首帧二进制为代理协议（SOCKS5/HTTP），解析后建立到目标连接
  - 使用 OnceCell<ConnectionManager> 复用 DNS 缓存/连接池配置

- src/proxy_server.rs
  - 代理协议解析：SOCKS5（CONNECT，无认证）、HTTP（CONNECT/GET/POST 基本解析）
  - DNS 缓存（HashMap + TTL + 容量控制）、连接池（按目标复用、闲置清理）
  - 连接超时、解析耗时记录与日志

- src/server_mux.rs（Mux 服务端）
  - /mux：管理 ClientSession（session_id 唯一），维护 ws_senders、connections、pending_data、路由表
  - 新流建立：RequestNewStream -> 解析/连目标 -> 返回 NewStreamResponse；其后缓存回放 pending_data
  - 数据转发：
    - 上行（WS->TCP）：收到携带 connection_id 前缀的数据 -> 写入相应 TCP
    - 下行（TCP->WS）：读取后加前缀 -> 按路由（connection_id->ws_index）发回对应 WS

- src/server_hub.rs（传统 Hub 服务中心）
  - /hub：
    - Hub Service 侧：RegisterService -> ServiceRegisteredAck；保留其 sink
    - 客户端侧：ConnectToServiceRequest -> ServiceConnectionAck（分配 client_connection_id）
  - 将客户端与服务提供者 sink 注册到 ServiceHub，负责 client<->hubservice 的数据中转与断连清理
  - 通过 HubMessage 传递控制（如 ClientConnectionEndedByTarget、ServiceHubServiceDisconnectedNotification）

- src/hub_service_forward.rs & src/hub_service_proxy.rs（面向 /hub/* 的路由桥接）
  - /hub/forward：校验 service_id 可用 -> 向 Hub Service 发送 ServiceForwardInstruction（instruction_type: "target"）
  - /hub/proxy：首帧解析代理协议 -> 指令下发（instruction_type: "proxy"），并注册客户端连接
  - 将客户端数据转发到 Hub Service，或经 DynamicHubState 路由（forward 里有对动态模式的兼容）

- src/hub_service.rs（Hub Service，传统/统一消息实现）
  - 作为“服务提供者（SP）”主动连到 server /hub，注册服务
  - 处理 ServiceForwardInstruction：连接目标，维护 active_connections，缓存/回放 pending_data
  - 二进制数据路径：客户端数据经 Server 前缀化后到 Hub Service -> 写入目标；目标数据回写到 Server 并带前缀
  - 心跳与重连：heartbeat.rs + reconnect.rs + watch::Sender/Receiver 协调

- src/server_hub_dynamic.rs（Server 侧动态隧道）
  - /hub/dynamic：控制通道（Hub -> Server，注册控制连接）
  - /hub/data：数据隧道（Hub <-> Server，按 client_id 注册与双向转发）
  - DynamicHubState：管理控制通道、待建立的数据隧道、活跃隧道、client<->service 映射、客户端连接 sink
  - 传统 forward 的兼容：legacy_{connection_id} 作为 client_id 贯穿

- src/hub_service_dynamic.rs（Hub Service 侧动态隧道）
  - 连接 /hub/dynamic（控制）、按需连接 /hub/data（数据）
  - DynamicTunnelManager：active_tunnels、metrics、tunnel_id 生成器；为 legacy forward 创建隧道任务
  - 处理 ControlMessage 与传统 HubMessage（ServiceForwardInstruction）
  - 重连/心跳机制与传统实现一致
  - 注：spawn_new_tunnel_task 为简化占位实现；spawn_legacy_tunnel_task 完整实现了目标连接与 /hub/data 数据通道的双向转发

- 其他
  - heartbeat.rs：定期发送 HubServiceHeartbeat(JSON)，失败时发出 HeartbeatFailed 重连信号
  - reconnect.rs：ReconnectState/Signal
  - streamlit_client.rs：基于 prost 生成的协议访问 Streamlit WS（示例性质）

## 消息协议与数据封装

- 连接/流标识：
  - ConnectionId（u32，自增），以 4 字节大端前缀加在二进制数据前，用于在 WS 中区分多路流
  - TunnelId（动态隧道内部使用）

- HubMessage（JSON 文本，见 src/common.rs）：
  - RegisterService/ServiceRegisteredAck
  - ConnectToServiceRequest/ServiceConnectionAck
  - NewClientNotification/ClientDisconnectedNotification
  - ClientConnectionEndedByTarget
  - HubServiceHeartbeat
  - ServiceForwardInstruction（connection_id + instruction_type + target_addr）
  - ErrorNotification/ServiceUnavailableNotification 等

- ClientMuxMessage（JSON 文本）：
  - InitializeSession/SessionEstablished、JoinSession
  - RequestNewStream/NewStreamResponse
  - Heartbeat、CloseStream
  - RegisterHubService/HubServiceRegistered（保留字段，服务端未使用）

- ControlMessage（动态隧道，JSON 文本）：RegisterHub、PrepareNewTunnel、TunnelReady、TunnelCreationFailed
- DataTunnelMessage（动态隧道，JSON 文本）：RegisterDataTunnel

## 关键技术栈与依赖

- Tokio（1.45，full）、futures-util、tokio-tungstenite（rustls-tls-webpki-roots）
- Axum（0.8，ws 特性）
- 序列化：serde/serde_json
- CLI：clap（derive）
- 日志：log + env_logger
- 错误：thiserror、anyhow（示例/工具）
- URL/HTTP：url、reqwest（Streamlit 示例）
- TLS：rustls
- 其他：bytes、base64、chrono、prost/prost-build（Streamlit 示例）、mimalloc（全局分配器）

## 主要功能流程

- Forward（直连）：
  1) client.rs 本地监听 -> 接入后构造 ws://server/forward?target=host:port，连接成功后
  2) 双向转发：TCP->WS 二进制、WS->TCP 写入；关闭/错误时双向清理

- Proxy（直连）：
  1) client.rs 连接 ws://server/proxy
  2) 首帧由客户端发送 SOCKS5/HTTP 代理请求；server_proxy.rs 解析后连目标
  3) 后续数据双向转发

- Hub Forward/Proxy（传统统一消息）：
  1) Hub Service（hub_service.rs）连 server /hub 并注册服务
  2) 客户端连接 /hub/forward 或 /hub/proxy；Server 生成 connection_id 并向 Hub Service 下发 ServiceForwardInstruction
  3) Hub Service 侧连接目标、缓存/回放待发数据；目标数据加前缀回送 Server，再转发给客户端

- Mux：
  1) client_mux.rs 与 /mux 建立会话（InitializeSession/SessionEstablished）
  2) 新流：RequestNewStream(target) -> Server 解析/连目标 -> NewStreamResponse
  3) 数据：加 4 字节 connection_id 前缀双向传输；Server 侧维护 connection_id 到具体 WS 的路由

- 动态隧道：
  1) Hub Service（hub_service_dynamic.rs）连接 /hub/dynamic 注册，保活心跳
  2) 当收到传统 ServiceForwardInstruction（target）时，为 legacy_{connection_id} 创建隧道任务：
     - 连接目标 TCP
     - 连接 /hub/data 并发送 RegisterDataTunnel(client_id)
     - 双向转发（目标<->数据隧道）
  3) Server 侧 server_hub_dynamic.rs 维护 client_id 到数据隧道/客户端连接的映射，完成响应数据回路

## 设计亮点与模式

- 统一的错误模型与结果类型（thiserror + alias Result）
- 多协议/模式的统一抽象：通过 JSON 文本消息（tagged enum + serde）
- 连接多路复用：固定 4 字节前缀区分流；Mux 会话握手与路由表
- 心跳与重连：应用层心跳（避免 ping/pong 被中间层处理），watch 通道驱动重连状态机
- 代理优化：DNS 缓存 + 连接池 + 超时管理
- 数据安全性：多数写操作使用超时包装；断连/错误场景下进行状态清理与关闭通知

## 现状与边界（以代码为准）

- server /metrics 中部分指标为占位（未实现统计来源）
- hub_service_dynamic.rs 中 spawn_new_tunnel_task 为简化占位实现；spawn_legacy_tunnel_task 为完整实现
- server_hub_dynamic.rs::create_tunnel_task 使用硬编码 server_url 示例（注释说明应来自配置）；生产化需统一由配置传入
- 日志/注释多为英文，个别中文仍存在（以 docs/code-tidy-01 为准还在逐步英文化）

