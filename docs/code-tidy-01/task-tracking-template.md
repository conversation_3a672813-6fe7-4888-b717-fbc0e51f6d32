# WSS Tunnel 代码整理任务跟踪模板

## 任务基本信息
- **任务编号**: [例如: 1.1.1]
- **任务名称**: [例如: 检查 `src/common.rs` 中的未使用导入]
- **目标**: [任务目标描述]
- **风险等级**: [低/中/高]
- **预计时间**: [例如: 30分钟]
- **实际时间**: [完成后填写]

## 任务状态
- [ ] 未开始
- [ ] 进行中
- [ ] 已完成
- [ ] 已测试
- [ ] 已提交
- [ ] 已审查

## 具体步骤
### 步骤1: [步骤描述]
- [ ] 完成
- [ ] 验证通过
- **备注**: [如有问题或特殊情况]

### 步骤2: [步骤描述]
- [ ] 完成
- [ ] 验证通过
- **备注**: [如有问题或特殊情况]

### 步骤3: [步骤描述]
- [ ] 完成
- [ ] 验证通过
- **备注**: [如有问题或特殊情况]

## 验证结果
### 编译验证
- [ ] `cargo check` 通过
- [ ] `cargo build` 通过
- [ ] `cargo clippy` 通过

### 测试验证
- [ ] `cargo test` 通过
- [ ] 相关功能测试通过
- [ ] 性能测试通过（如适用）

### 代码质量验证
- [ ] 代码格式正确 (`cargo fmt --check`)
- [ ] 没有新的警告
- [ ] 文档更新（如适用）

## 遇到的问题
### 问题1: [问题描述]
- **影响**: [对任务的影响程度]
- **解决方案**: [采取的解决方案]
- **结果**: [解决结果]

### 问题2: [问题描述]
- **影响**: [对任务的影响程度]
- **解决方案**: [采取的解决方案]
- **结果**: [解决结果]

## 修改内容
### 文件修改
- **文件**: [文件路径]
- **修改类型**: [新增/修改/删除]
- **修改内容**: [简要描述修改内容]

### 代码变更
```rust
// 修改前的代码
// 修改后的代码
```

## 测试结果
### 单元测试
- [ ] 所有相关单元测试通过
- [ ] 新增测试用例（如适用）
- [ ] 测试覆盖率检查

### 集成测试
- [ ] 相关集成测试通过
- [ ] 端到端测试通过（如适用）

### 性能测试
- [ ] 性能没有下降
- [ ] 内存使用正常
- [ ] CPU使用正常

## 代码审查
### 审查人员
- **审查人**: [姓名]
- **审查日期**: [日期]
- **审查结果**: [通过/需要修改]

### 审查意见
- [ ] 代码逻辑正确
- [ ] 错误处理完善
- [ ] 注释清晰准确
- [ ] 测试覆盖充分
- [ ] 性能影响评估
- [ ] 向后兼容性

### 审查反馈
- **正面反馈**: [审查人员的正面评价]
- **改进建议**: [审查人员的改进建议]
- **需要修改的地方**: [具体需要修改的地方]

## 提交信息
### Git提交
```bash
git add [修改的文件]
git commit -m "[任务编号] [任务名称]

- 修改内容1
- 修改内容2
- 验证结果"
```

### 分支信息
- **分支名**: [例如: feature/task-1.1.1]
- **目标分支**: [例如: main]
- **PR链接**: [如适用]

## 后续任务
### 依赖任务
- [ ] 任务 [任务编号] 需要先完成
- [ ] 任务 [任务编号] 需要先完成

### 后续影响
- [ ] 影响任务 [任务编号]
- [ ] 影响任务 [任务编号]

## 经验总结
### 成功经验
- [经验1]
- [经验2]

### 改进建议
- [建议1]
- [建议2]

### 注意事项
- [注意事项1]
- [注意事项2]

## 完成确认
- [ ] 任务目标已达成
- [ ] 所有验证通过
- [ ] 代码已提交
- [ ] 文档已更新
- [ ] 经验已总结

**完成日期**: [日期]
**完成人**: [姓名]

---

## 使用说明

### 如何填写模板
1. 复制此模板到新的文件
2. 根据具体任务修改任务基本信息
3. 按照步骤执行任务
4. 在每个步骤完成后勾选相应选项
5. 记录遇到的问题和解决方案
6. 填写验证结果
7. 提交代码审查
8. 总结经验和教训

### 模板使用建议
1. **详细记录**: 每个步骤都要详细记录，便于后续参考
2. **及时更新**: 任务状态要及时更新，保持跟踪的准确性
3. **问题记录**: 遇到的问题要详细记录，便于后续避免
4. **经验总结**: 每个任务完成后要总结经验，提高效率
5. **质量保证**: 严格按照验证清单进行检查，确保质量

### 模板维护
- 根据实际使用情况调整模板内容
- 定期更新验证清单
- 收集使用反馈，持续改进 