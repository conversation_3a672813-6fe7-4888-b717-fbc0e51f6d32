# Hub-Service 重构设计：动态隧道模式

## 1. 概述与目标

本文档旨在规划 `hub-service` 的重构方案，以支持一种全新的、高并发的动态隧道模式。此举的目标是解决现有 `mux`（多路复用）实现中的性能与可伸缩性瓶颈，特别是“队头阻塞”（Head-of-Line Blocking）问题。

核心目标如下：
1.  **引入全新的、高性能的“动态隧道”模式**，并将其作为默认行为。
2.  **保留现有的 `mux` 实现**，通过命令行参数 `--use-mux` 进行切换，以保证向后兼容性与性能对比。
3.  **简化整体架构**，通过按需创建连接取代维护静态连接池。
4.  **确保健壮性**，通过严格分离I/O读写流来防止死锁。
5.  **为未来监控奠定基础**，预先设计好指标收集系统。

## 2. 命令行接口 (CLI) 变更

为了让用户能在两种模式间自由选择，我们将为 `hub-service` 子命令添加一个新的参数。

### `src/main.rs`

`HubService` 参数结构体将进行修改，增加 `--use-mux` 参数，其默认值为 `false`。

```rust
// 位于 src/main.rs

#[derive(Args, Debug)]
pub struct HubService {
    #[clap(long, short, default_value = "0.0.0.0:8000")]
    hub_ws_addr: String,

    #[clap(long, short, default_value = "0.0.0.0:8501")]
    hub_tcp_addr: String,

    // 用于选择实现模式的新参数
    #[clap(long, default_value_t = false)]
    use_mux: bool,
}
```

`main` 函数将依据此参数的值，条件性地调用现有的 `hub_service::run_hub_service` 或新的 `hub_service_dynamic::run_hub_service_dynamic` 函数。

```rust
// 位于 src/main.rs 的 main 函数中

// ...
Cli::HubService(args) => {
    if args.use_mux {
        // 运行原有的 mux 实现
        info!("Running Hub-Service in MUX mode.");
        hub_service::run_hub_service(args).await?;
    } else {
        // 运行新的动态隧道实现
        info!("Running Hub-Service in Dynamic Tunnel mode.");
        hub_service_dynamic::run_hub_service_dynamic(args).await?;
    }
}
// ...
```

## 3. 架构设计 (`--use-mux=false`)

新的动态模式将控制流与数据流彻底分离。

### 3.1. 核心概念

*   **控制通道 (Control Channel)**: 一个从 `hub-service` 到 `server` 的、单一的、持久化的 WebSocket 连接。它专用于收发指令消息（例如：“请准备一个新的隧道”）。
*   **动态数据隧道 (Dynamic Data Tunnels)**: `hub-service` 不再维护一个预热的连接池，而是为每一个客户端请求按需创建一个全新的到 `server` 的 WebSocket 连接。每个数据隧道都完全隔离。

### 3.2. 新增组件

为了最大程度减少对现有代码的干扰，所有新逻辑将存放在一个新文件中：

*   **`src/hub_service_dynamic.rs`**: 该文件将包含新模式的全部逻辑，包括：
    *   `run_hub_service_dynamic()`: 新模式的主入口函数。
    *   一个用于管理控制通道连接并监听指令的函数。
    *   一个为每个客户端请求创建 `tokio::task` 的函数，用于建立和管理数据隧道。

### 3.3. 详细工作流

以下序列图清晰地展示了从 `hub-service` 启动到客户端数据传输的完整过程。

```mermaid
sequenceDiagram
    participant Client as 客户端进程
    participant Server as 服务端进程
    participant Hub as Hub服务进程
    participant LocalSvc as 本地服务

    note over Server, Hub: 1. Hub 启动 (动态模式)
    Hub->>+Server: 建立唯一的 WebSocket 作为控制通道
    Server-->>-Hub: 连接确认
    Hub->>Server: 通过控制通道发送 `RegisterHub` 消息

    note over Client, Server: 2. 客户端发起转发请求
    Client->>+Server: 建立 WebSocket, 请求通过 Hub 转发

    note over Server, Hub: 3. 服务端协调隧道创建
    Server->>Hub: 通过控制通道发送 `PrepareNewTunnel { client_id }`

    note over Hub: 4. Hub 按需创建隧道
    Hub->>Hub: 为此客户端派生一个新的异步任务
    par
        Hub->>+Server: 新任务建立一个新的 WebSocket (数据隧道)
    and
        Hub->>+LocalSvc: 新任务建立一个新的 TCP 连接
    end
    
    note over Hub, Server: 5. 数据隧道进行身份认领
    Hub->>Server: (通过新的数据隧道) 发送 `RegisterDataTunnel { client_id }`

    note over Server: 6. 服务端关联客户端与隧道
    Server->>Server: 将等待中的客户端连接与新注册的数据隧道进行绑定

    note over Client, LocalSvc: 7. 隔离的数据流
    loop 双向数据泵
        Client<->>Server<->>Hub<->>LocalSvc: 数据通过专用的隧道流动
    end
```

### 3.4. 协议定义

我们需要定义一些清晰的通信消息。它们可以存放在 `src/common.rs` 中，或是一个新的协议专用文件。

**控制通道消息 (`server` <-> `hub_service`):**
```rust
pub enum ControlMessage {
    RegisterHub { hub_id: String },
    // Server -> Hub: 指示 Hub 为新客户端做准备
    PrepareNewTunnel { client_id: String }, 
    // Hub -> Server: 确认准备就绪或报告错误
    TunnelReady { client_id: String, tunnel_id: String },
    TunnelCreationFailed { client_id: String, reason: String },
}
```

**数据隧道消息 (`hub_service` -> `server`):**
```rust
pub enum DataTunnelMessage {
    // 在新数据隧道上发送的第一个消息，用于表明其身份
    RegisterDataTunnel { client_id: String },
}
```

## 4. 实现细节与最佳实践

### 4.1. 防止死锁：分离 I/O 流

这是至关重要的一点。每一个用于双向转发的 I/O 资源（`WebSocketStream`, `TcpStream`）都**必须**被拆分为其对应的读半部分和写半部分。这使得数据可以在独立的异步任务中被并发地读取和写入，避免一个操作阻塞另一个。

**`spawn_new_tunnel_task` 实现草图示例:**

```rust
async fn spawn_new_tunnel_task(client_id: String, server_addr: String, local_target_addr: String) {
    // 1. 建立连接
    let ws_stream = tokio_tungstenite::connect_async(&server_addr).await.unwrap().0;
    let tcp_stream = tokio::net::TcpStream::connect(&local_target_addr).await.unwrap();

    // 2. 关键步骤：拆分两个流
    let (mut ws_write, mut ws_read) = ws_stream.split();
    let (mut tcp_read, mut tcp_write) = tcp_stream.into_split();

    // 3. 注册隧道
    let register_msg = DataTunnelMessage::RegisterDataTunnel { client_id };
    // ws_write.send(Message::from(serde_json::to_string(&register_msg).unwrap())).await;

    // 4. 派生两个任务，在两个方向上泵送数据
    let upload_task = tokio::spawn(async move {
        tokio::io::copy(&mut tcp_read, &mut ws_write).await;
    });

    let download_task = tokio::spawn(async move {
        tokio::io::copy(&mut ws_read, &mut tcp_write).await;
    });
    
    // TODO: 优雅停机逻辑
    tokio::try_join!(upload_task, download_task).ok();
}
```

## 5. 指标与监控 (TODO)

虽然完整的指标端点实现被推迟，但我们将设计好数据结构，并在代码中放置 `TODO` 标记，以便未来实现。

### 5.1. 拟议的指标

一个中心的 `Metrics` 结构体将通过 `Arc<Mutex<...>>` 在任务间共享。

```rust
// 将定义在 hub_service_dynamic.rs
#[derive(Default, Debug)]
pub struct HubMetrics {
    pub active_tunnels: u64,
    pub total_tunnels_created: u64,
    pub total_tunnels_failed: u64,
    pub bytes_uplinked: u64,
    pub bytes_downlinked: u64,
    pub control_channel_status: bool,
}
```

### 5.2. 实现草图 (`TODO`)

*   **`run_hub_service_dynamic`**: 将创建 `Arc<Mutex<HubMetrics>>`。
*   **控制通道任务**: 将更新 `control_channel_status`。
*   **`spawn_new_tunnel_task`**:
    *   进入时: `metrics.lock().unwrap().active_tunnels += 1;`
    *   成功时: `metrics.lock().unwrap().total_tunnels_created += 1;`
    *   失败时: `metrics.lock().unwrap().total_tunnels_failed += 1;`
    *   `copy` 函数返回传输的字节数，可用于更新 `bytes_uplinked` 和 `bytes_downlinked`。
    *   退出时: `metrics.lock().unwrap().active_tunnels -= 1;`

可以派生一个独立的任务来周期性地记录指标或通过 HTTP 端点暴露它们。一个 `TODO` 注释将被放置在相应位置。
```rust
// TODO: 派生一个指标任务，用于周期性地记录或暴露 HubMetrics 数据。
// tokio::spawn(monitor_metrics(metrics.clone()));
```

这种结构化的方法确保我们的重构是健壮的、可维护的，并为未来的功能增强做好了准备。 