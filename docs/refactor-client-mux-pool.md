# 客户端 Mux 连接池改造方案

## 1. 背景 (Background)

当前的 `client-mux` 架构在客户端和服务器之间仅使用一个单一的 WebSocket 连接来承载所有并发的TCP流。经过分析，该架构已被证实存在严重的性能瓶颈。

**核心问题**: **队头阻塞 (Head-of-Line Blocking)**。

1.  **TCP层**: 单一的WebSocket连接底层依赖于单一的TCP连接。任何一个数据包的网络延迟或丢失，都会导致整条TCP链路的暂停，从而阻塞了所有正在该链路上复用的、本应无关的TCP流。
2.  **应用层**: `client-mux` 和 `server-mux` 的消息处理循环是串行的。一个请求的处理（无论是上行发送还是下行写入）必须等待前一个请求的`await`操作完成，这在应用层进一步放大了延迟，并人为地将并发请求串行化。

这些问题导致在高并发或网络不稳定的情况下，`client-mux`模式性能急剧下降，并出现偶发性的连接阻塞。

## 2. 目标 (Goals)

本次改造的核心目标是在保留Mux架构优势的前提下，解决其性能瓶颈。

- **提升性能**: 显著提高 `client-mux` 模式下的并发处理能力和数据吞吐量。
- **缓解阻塞**: 通过并行化数据传输，极大降低队头阻塞发生的概率。
- **保证数据完整性**: 必须确保任意单个TCP流（例如一个文件下载或SSH会话）内部的数据包严格按序传输。
- **可控的资源使用**: 避免为每个业务请求都创建全新的WebSocket连接，将总连接数维持在一个可控的范围内。

## 3. 方案设计 (Proposed Design)

### 3.1. 核心思想

引入一个**WebSocket连接池**。客户端 (`client-mux`) 在启动时，不再与服务端 (`server-mux`) 建立单个连接，而是建立并维护一个由 **N** 个WebSocket连接组成的池。新的业务流（本地TCP连接）将被分发到池中的不同连接上进行传输。

### 3.2. 技术选型

为避免重复造轮子，我们将使用 `crates.io` 上成熟的异步连接池库 **`deadpool`**。

- **理由**:
    - **异步原生**: 为`async/await`和`tokio`设计，与项目技术栈高度契合。
    - **健壮性**: 提供了强大的连接生命周期管理、健康检查和自动回收/重建机制。
    - **灵活性**: 其API可以适配我们“获取并长期绑定”的特殊用例。

### 3.3. 详细设计

#### 3.3.1. 客户端改造 (`client_mux.rs`)

**1. 新增配置项**

在 `ClientMuxConfig` 结构体中增加新字段，以控制连接池的大小。

```rust
pub struct ClientMuxConfig {
    // ... aother fields
    pub mux_pool_size: usize, // e.g., default = 1, recommended = 4 or 8
}
```

**2. `deadpool` 集成**

- **依赖**: 在 `Cargo.toml` 中添加 `deadpool`。为支持超时等关键功能，必须启用`rt_tokio_1`特性。
    ```toml
    deadpool = { version = "0.10", features = ["managed", "rt_tokio_1"] }
    ```
- **`Manager` 实现**: 创建一个 `struct WebSocketManager` 并为其实现 `deadpool::managed::Manager` trait。
    - `create()`: 实现创建单个WebSocket连接的逻辑。
    - `recycle()`: 实现连接的健康检查。一个简单有效的方式是带超时地发送WebSocket `Ping` 帧并等待`Pong`帧返回。

**3. `ClientMuxManager` 结构改造**

- 移除原有的单个 `ws_sender`。
- 新增以下字段：
    ```rust
    struct ClientMuxManager {
        // ...
        session_id: Arc<RwLock<Option<u32>>>, // 存储由服务端分配的会话ID
        pool: deadpool::managed::Pool<WebSocketManager>,
        live_connections: Arc<RwLock<Vec<deadpool::managed::Object<WebSocketManager>>>>,
        round_robin_counter: Arc<std::sync::atomic::AtomicUsize>,
        // 用于下行数据路由
        stream_to_ws_map: Arc<Mutex<HashMap<ConnectionId, usize>>>, 
    }
    ```

**4. 初始化与连接池预热**

- 在 `ClientMuxManager` 初始化时，创建 `deadpool::Pool`。
- **预热**: 在主程序启动后，立即从 `pool` 中获取 `mux_pool_size` 个连接，并触发握手协议，将它们存储在 `live_connections` 这个 `Vec` 中，作为我们进行轮询的稳定集合。

**5. 连接分发与绑定 (Round-Robin)**

- 当 `handle_local_connection` 被调用以处理一个新的本地TCP流时：
    1.  移除向全局`broadcast` channel发送数据的逻辑。
    2.  使用 `round_robin_counter` 原子地获取下一个索引: `index = counter.fetch_add(1) % pool_size`。
    3.  从 `live_connections` 读锁中获取索引为 `index` 的WebSocket连接的克隆。
    4.  **绑定**: 将这个 `connection_id` 和 `index` 的映射关系存入 `stream_to_ws_map`。
    5.  `spawn` 一个新的转发任务，这个任务将持有**特定的**WebSocket连接，并负责此TCP流后续所有的数据收发。

#### 3.3.2. 服务端改造 (`server_mux.rs`)

**1. 引入全局会话管理**

- 服务端需要一个全局共享的、线程安全的状态来管理所有活跃的Mux会话。
    ```rust
    // e.g. in server.rs
    let server_state = Arc::new(RwLock::new(HashMap<u32, Arc<ClientSession>>));
    ```
- 这个`server_state`将通过Axum的`Extension`注入到WebSocket处理器中。

**2. 握手协议 (基于WebSocket消息)**

此协议取代原先基于HTTP Header的方案，更健壮、更高效。

- **首次连接**:
    1.  客户端池中的第一个连接建立后，立即发送一个控制消息: `ClientMuxMessage::InitializeSession`。
    2.  服务端收到此消息后，理解为这是一个新会话的开始。
    3.  服务端生成一个全局唯一的 `SessionId` (类型为 **`u32`**，通过原子计数器等方式生成)。
    4.  服务端创建一个新的 `ClientSession` 实例，并将其 `Arc` 存入全局的会话管理器中，以 `SessionId` 为键。
    5.  服务端通过WebSocket将 `SessionEstablished { session_id: u32 }` 消息发回给客户端。客户端收到后保存此 `SessionId`。

- **后续连接**:
    1.  客户端池中的第2, 3...N个连接建立后，它们已经知道了 `SessionId`。
    2.  它们会立即发送一个控制消息: `ClientMuxMessage::JoinSession { session_id: u32 }`。
    3.  服务端收到此消息后，从全局会话管理器中查找 `session_id`。
    4.  如果找到，就将这个新的WebSocket连接加入到已存在的 `ClientSession` 的连接池中。
    5.  如果未找到，则返回错误并关闭连接。

**3. `ClientSession` 结构改造**

- 移除原有的单个 `sender`。
- 新增一个 `senders` 池: `senders: Vec<Arc<Mutex<SplitSink<...>>>>`。
- 新增一个映射，用于下行路由: `stream_to_ws_map: HashMap<ConnectionId, usize>`。

**4. 上下行数据路由**

- **上行**: 当 `handle_control_message` 收到 `RequestNewStream` 请求时，它需要知道这个请求是从哪个WebSocket连接(用 `ws_index` 标识)上发来的。这个 `ws_index` 必须与 `connection_id` 一起存入 `stream_to_ws_map`。
- **下行**: 当 `handle_binary_frame` 需要将数据发回给某个 `connection_id` 时：
    1.  从 `stream_to_ws_map` 查出此流绑定的 `ws_index`。
    2.  从 `senders` 池中获取索引为 `ws_index` 的发送端。
    3.  通过此发送端发送数据。

这个机制保证了数据“从哪条路来，就从哪条路回去”，维护了单个TCP流内部的顺序性。

## 4. 潜在风险与对策

- **风险**: **数据倾斜 (Load Skew)**
    - **描述**: 简单的轮询分配可能导致负载不均，例如一个大流量下载占满一条链路，而其他链路相对空闲。
    - **对策**: 作为第一版实现(MVP)，**Round-Robin** 策略能以最小复杂度解决核心的队头阻塞问题。在方案上线后，如果观察到明显的倾斜问题，可作为二期优化，实现更智能的负载均衡算法，例如 **“最少连接数 (Least Connections)”** 算法。

- **风险**: **实现复杂度**
    - **描述**: 该方案是对现有架构的一次重大重构。
    - **对策**: 遵循本文档的设计，分步实施，先客户端后服务端，并为每个阶段编写充分的测试。

---

## 5. 重构实施步骤 (Implementation Steps)

本章节将作为本次重构任务的清单，用于追踪开发进度。

### 阶段一：通用定义与配置 (Phase 1: Common Definitions & Configuration)

- [ ] **任务 1.1**: 在 `Cargo.toml` 中添加 `deadpool` 依赖，并启用 `rt_tokio_1` 特性。
- [ ] **任务 1.2**: 在 `common.rs` 的 `ClientMuxMessage` 枚举中，添加用于新握手协议的变体：
    - `InitializeSession` (client to server)
    - `JoinSession { session_id: u32 }` (client to server)
    - `SessionEstablished { session_id: u32 }` (server to client)
- [ ] **任务 1.3**: 在 `client_mux.rs` 的 `ClientMuxConfig` 结构体中，增加 `mux_pool_size: usize` 配置项，并提供默认值 `1`。

### 阶段二：客户端改造 (`client_mux.rs`)

- [ ] **任务 2.1**: 实现 `deadpool` 的连接管理器 `struct WebSocketManager`，并为其实现 `deadpool::managed::Manager` trait。
- [ ] **任务 2.2**: 彻底重构 `ClientMuxManager` 结构体，引入 `deadpool::Pool`、`live_connections`、`session_id`、`round_robin_counter` 和 `stream_to_ws_map` 等新状态字段。
- [ ] **任务 2.3**: 实现客户端的握手逻辑。
    - 对于池中的第一个连接，发送 `InitializeSession`。
    - 监听并处理来自服务端的 `SessionEstablished` 消息，正确保存 `session_id`。
    - 对于池中后续的连接，发送 `JoinSession { session_id }`。
- [ ] **任务 2.4**: 实现连接池的初始化和预热逻辑，确保在程序启动后，`live_connections` 中填充了 `mux_pool_size` 个已完成握手的可用连接。
- [ ] **任务 2.5**: 移除旧的、基于全局`broadcast` channel的单一转发任务 (`tcp_to_ws_task`)。
- **[ ] 任务 2.6**: 在 `handle_local_connection` 中实现基于`round_robin_counter`的轮询分发逻辑。**（进行中）**
- [ ] **任务 2.7**: 为每个本地TCP连接创建独立的、与特定WebSocket连接绑定的新转发任务。

### 阶段三：服务端改造 (`server_mux.rs` & `server.rs`)

- [ ] **任务 3.1**: 在 `server.rs` 中，创建并初始化用于管理所有Mux会话的全局状态 `Arc<RwLock<HashMap<u32, Arc<ClientSession>>>>`。
- [ ] **任务 3.2**: 将上述全局状态通过Axum的`Extension`机制注入到WebSocket路由处理器中。
- [ ] **任务 3.3**: 重构 `server_mux.rs` 的入口函数 `handle_mux_connection`，使其能够处理新的握手协议，并从全局状态中查找或创建会话。
- [ ] **任务 3.4**: 重构 `ClientSession` 结构体，使其能够管理一个WebSocket发送端池 (`senders`) 和用于路由的 `stream_to_ws_map`。
- [ ] **任务 3.5**: 实现服务端的双向数据路由逻辑，确保数据能够根据 `ws_index` 从正确的WebSocket连接上发送回去。

---
`文档创建完毕`

## 6. 附录 (Appendix)

### 6.1. `SessionId` 与 `ConnectionId` 的区别与必要性

在连接池方案中，同时使用 `SessionId` 和 `ConnectionId` 至关重要。它们管理着完全不同层面的“身份”，解决不同范围的问题。

- **`SessionId`**: 管理 **“这是哪一个逻辑客户端的总会话？”**
- **`ConnectionId`**: 管理 **“这是哪一个具体的业务数据流？”**

#### 6.1.1. 一个物流公司的类比

将 `wsstun` 服务端想象成一个大型物流公司，以便理解这两个ID的角色：

- **`SessionId` (VIP大客户账号)**:
    - 由物流公司（服务端）分配，全局唯一，代表了您这个客户实体。无论您今天派出了多少辆货车来送货，它们都属于这同一个客户账号。

- **WebSocket 连接 (货车)**:
    - 为了提高效率，您决定同时派出4辆货车（连接池大小为4）来送货。每一辆货车到达物流公司时，都必须报上您的VIP客户账号 (`SessionId`)，这样公司才知道这些货车都属于您。

- **`ConnectionId` (货物运单号)**:
    - 您的每辆货车上可能装载了发往不同目的地的货物（例如一个SSH连接的数据流，一个文件下载的数据流）。每件货物上都贴着独一无二的运单号 (`ConnectionId`)。
    - 物流公司从货车上卸货时，不仅需要知道这车货属于哪个VIP客户 (`SessionId`)，还必须查看每件货上的运单号 (`ConnectionId`)，才能知道这件货具体应该送往哪里。

**结论**: `SessionId` 用来“认领车队”，`ConnectionId` 用来“分拣货物”。两者缺一不可。

#### 6.1.2. 为何必须有 `SessionId`?

如果没有 `SessionId`，连接池方案将彻底失败。服务端无法知道多个独立的WebSocket连接其实来自同一个客户端。它会为每一条物理连接都创建一个独立的、互不相干的 `ClientSession`。结果是，一个业务流（如 `connection_id=1`）的数据被分配到 `WebSocket-1`后，就无法被路由到 `WebSocket-2`，因为管理 `WebSocket-2` 的那个会话完全不认识 `connection_id=1`。数据流会被割裂在不同的会话中，导致连接池形同虚设。

`SessionId` 的作用就是让服务端能够将多个物理连接“粘合”成一个统一的逻辑会话，使得所有连接池内的链路可以为同一个会话中的任何`ConnectionId`服务。

#### 6.1.3. 总结

| 特性       | `