# 客户端-服务器连接复用重构计划

本文档旨在阐述将 `wsstun client` 和 `wsstun server` 之间的通信层重构为支持在单个 WebSocket 通道上进行连接复用的设计方案。

## 1. 目标

当前实现为每个本地应用发起的TCP流都创建一个新的WebSocket连接。该模型虽然简单，但效率低下，导致：

-   **高延迟**: 每个新流都承担了TCP、TLS和WebSocket握手的开销。
-   **资源密集**: 在客户端和服务器上消耗大量文件描述符、内存和CPU资源，尤其是在高负载下。
-   **扩展性差**: 随着并发连接数的增加，性能会迅速下降。

本重构的目标是改造此通信模式，在客户端和服务器之间使用**单一的、持久化的WebSocket连接**，并通过此单一通道复用所有数据流。这与 `hub-service` 使用的模型一致。

## 2. Mux架构设计

我们将引入一个新的通信模型，"Mux模式"，它将镜像现有的 `hub-service` 到 `server` 的架构。

### 2.1. 核心概念

-   **单一WebSocket连接**: `wsstun client` 将与 `wsstun server` 上的新 `/mux` 端点建立一个单一的、长寿命的WebSocket连接。此连接作为所有控制消息和数据载荷的传输通道。
-   **连接ID (ConnectionId)**: 每个独立的数据流（例如，一个本地TCP连接）将被分配一个唯一的 `ConnectionId`（一个 `u32` 整数，与 `hub_service.rs` 保持一致）。此ID用于将数据包与其对应的流关联起来。
-   **控制消息 (JSON)**: 控制信令，如创建新流、关闭流和心跳，将作为JSON编码的文本消息通过WebSocket发送。**选择JSON格式是为了与 `hub_service` 的控制协议 (`HubMessage`) 保持完全一致，后者同样使用 `serde_json` 进行序列化并通过 `Text` 消息传输。**
-   **数据帧 (Binary)**: 实际的应用数据将作为二进制WebSocket消息发送。每条消息都将以其 `ConnectionId` 为前缀，以便接收端进行解复用。

### 2.2. 通信协议

#### 控制消息

将定义一个新的 `ClientMuxMessage` 枚举用于控制平面的通信，并序列化为JSON。

```rust
// 概念定义
use serde::{Serialize, Deserialize};

// 与hub_service.rs保持一致
type ConnectionId = u32;

#[derive(Serialize, Deserialize, Debug)]
#[serde(tag = "type", content = "payload")]
enum ClientMuxMessage {
    /// Client -> Server: 请求建立一个新流
    RequestNewStream {
        connection_id: ConnectionId,
        // 对于 'forward' 模式, 目标地址需要预先指定.
        // 对于 'proxy' 模式, 此项为 None.
        target_addr: Option<String>,
    },
    /// Server -> Client: 对 RequestNewStream 的响应
    NewStreamResponse {
        connection_id: ConnectionId,
        success: bool,
        error_message: Option<String>,
    },
    /// Client -> Server: 用于维持连接的心跳
    Heartbeat {
        timestamp: u64,
    },
    /// 双向: 通知一个流已被关闭
    CloseStream {
        connection_id: ConnectionId,
    },
}
```

#### 数据帧格式

所有应用数据将使用与 `hub-service` 数据交换相同的二进制帧结构进行传输：

`[4字节ConnectionId (u32, 大端序)][原始TCP载荷]`

为了保持一致性，将复用 `common.rs` 中的 `add_connection_id_prefix` 和 `extract_connection_id_and_payload` 辅助函数。

### 2.3. 实现变更

#### `client.rs` 重构

1.  **启动**: 启动时，立即连接到服务器的 `/mux` 端点，建立持久的WebSocket连接。
2.  **心跳**: 派生一个后台任务，按可配置的间隔（例如每30秒）向服务器发送 `ClientMuxMessage::Heartbeat`。这能确保服务器知晓客户端存活，并防止中间代理关闭空闲连接。
3.  **连接管理**:
    -   维护一个共享状态 `Arc<Mutex<HashMap<ConnectionId, ...>>>`，用于映射 `ConnectionId` 到其对应的本地TCP流写入端。
    -   当接受一个新的本地TCP连接时：
        -   生成一个新的 `ConnectionId`。
        -   通过Mux通道发送 `ClientMuxMessage::RequestNewStream`。
        -   将TCP写入器存储在映射中。
        -   派生一个任务，从该TCP套接字读取数据，封装成二进制数据帧格式，并通过Mux通道发送。
4.  **消息处理**: 一个中心任务将从Mux WebSocket读取消息：
    -   收到 `NewStreamResponse`: 确认远端已准备就绪。
    -   收到 `Data` 帧: 提取 `ConnectionId`，在映射中查找TCP写入器，并将载荷写入本地TCP套接字。
    -   收到 `CloseStream`: 从映射中移除 `ConnectionId` 并关闭本地TCP套接字。

#### 2.3.1. 客户端重连机制
为了解决Mux模式下单点故障的问题，客户端必须实现一个健壮的自动重连机制，其逻辑将严格参考 `hub_service.rs` 中的实现。

1.  **连接状态管理**: 客户端管理器将维护一个连接状态机（如 `enum State { Connecting, Connected, Reconnecting }`）。
2.  **触发重连**: 任何导致WebSocket连接中断的事件（如读/写错误、服务器关闭连接、心跳失败）都将触发状态机进入 `Reconnecting` 状态。
3.  **指数退避算法**: 重连过程将采用与 `hub_service` 类似的多阶段指数退避策略：
    *   **快速重连阶段**: 在初始几次尝试中，采用较短的、指数增长的延迟（例如 1s, 2s, 4s, ...），以期快速恢复连接。
    *   **慢速重连阶段**: 若快速重连失败，则切换到较长的、固定的延迟（例如 30s）进行数次尝试。
    *   **持续重连阶段**: 若仍然失败，则以一个更长的最大延迟（例如 5分钟）持续尝试，直到连接恢复。
4.  **连接恢复**: 一旦重连成功，客户端将重新进入 `Connected` 状态。**注意：Mux连接的重连会中断所有正在进行的TCP流，这一点需要明确。** 客户端本地监听的TCP连接需要被关闭，等待应用层重新发起连接。

#### `server.rs` 重构

1.  **新端点**: 为路径 `/mux` 创建一个新的 `axum` 处理器，用于升级WebSocket连接。
2.  **会话管理**:
    -   为每个连接的客户端创建一个 `ClientSession` 结构体。
    -   `ClientSession` 将维护其自己的 `HashMap`，用于映射 `ConnectionId` 到对应的目标TCP流写入端（例如，到 `192.168.1.100:80` 或某个代理目的地）。
    -   它还将跟踪最后一次心跳时间以检测死客户端。
3.  **消息处理**: 会话处理器将循环从客户端的Mux WebSocket读取消息：
    -   收到 `RequestNewStream`: 连接到指定的 `target_addr`（对于forward模式）或准备进行代理。成功后，将目标TCP写入器存储在会话映射中，并发送 `NewStreamResponse`。
    -   收到 `Data` 帧: 提取 `ConnectionId`，查找目标TCP写入器，并转发载荷。
    -   收到 `Heartbeat`: 更新会话的 `last_heartbeat` 时间戳。
    -   收到 `CloseStream`: 从会话映射中移除连接并关闭目标TCP套接字。

### 2.4. 交互流程图

```mermaid
sequenceDiagram
    participant App as 本地应用
    participant Client as wsstun 客户端
    participant Server as WSSTun 服务器
    participant Target as 目标服务

    Client->>Server: WebSocket 握手 (/mux)
    Server-->>Client: 握手成功
    Client->>Server: (周期性) ClientMuxMessage::Heartbeat

    App->>Client: TCP 连接 (localhost:8080)
    Client->>Client: 生成 ConnectionId (例如 123)
    Client->>Server: ClientMuxMessage::RequestNewStream(id=123, target=...)

    Server->>Target: TCP 连接到目标
    Target-->>Server: TCP 连接成功
    Server->>Server: 映射 id=123 -> 目标TCP流
    Server-->>Client: ClientMuxMessage::NewStreamResponse(id=123, success=true)

    loop 数据传输
        App->>Client: TCP 数据
        Client->>Server: [二进制帧: id=123 | 载荷]
        Server->>Target: TCP 数据

        Target->>Server: TCP 数据
        Server->>Client: [二进制帧: id=123 | 载荷]
        Client->>App: TCP 数据
    end

    App->>Client: 关闭TCP连接
    Client->>Server: ClientMuxMessage::CloseStream(id=123)
    Server->>Target: 关闭TCP连接
    Server->>Server: 清理 id=123 的映射
```

## 3. Mux架构的缺点与权衡

虽然Mux模型提供了显著的性能优势，但承认其权衡之处也很重要：

1.  **队头阻塞 (Head-of-Line Blocking)**: 单一的WebSocket连接作为多个独立流的传输通道。如果这个单一连接的TCP缓冲区被填满（由于网络缓慢或一端消费慢），它可能会阻塞**所有**被复用的流，即使其他流本身是健康的。需要仔细管理背压和缓冲区大小来缓解此问题。
2.  **增加复杂性 (Increased Complexity)**: 管理流状态、ID和映射（每个数据包都需要`HashMap`查找）的逻辑，在本质上比之前的"每流一连接"模型更复杂。这增加了与状态管理和竞态条件相关的错误几率。
3.  **单点故障 (Single Point of Failure)**: 所有流都依赖于一个WebSocket连接。如果此连接断开，**所有**活跃的应用流都将突然终止。为Mux通道建立一个健壮的自动重连机制对于服务稳定性至关重要，**根据设计，客户端将实现一个与 `hub_service.rs` 类似的多阶段指数退避重连策略来应对此问题**。
4.  **缓冲膨胀风险 (Bufferbloat Potential)**: 为了处理那些其流尚未完全建立就已到达的数据（例如，客户端数据在服务器连接到目标之前就已到达），需要进行缓冲。这可能导致服务器/客户端内存使用量增加，并需要仔细管理以防止在负载下内存耗尽。

通过实施此项更改，我们是用简单性换取了性能和可伸缩性。

## 4. 分阶段执行计划

为了确保平稳过渡和风险可控，建议将重构过程分为以下几个阶段：

### 阶段一：协议定义与基础准备 (Protocol Definition & Scaffolding)
1.  **定义 `ClientMuxMessage`**: 在 `common.rs` 中，添加 `ClientMuxMessage` 枚举的定义，并引入 `serde` 依赖。
2.  **确认辅助函数**: 确保 `common.rs` 中的 `add_connection_id_prefix` 和 `extract_connection_id_and_payload` 函数是通用的，并可被客户端和服务器复用。
3.  **调整 `Cargo.toml`**: 为 `wsstun` crate 添加 `serde` 和 `serde_json` 依赖。

* **产出**: 定义了通信协议，但未改变现有任何业务逻辑。这是最安全的一步。

### 阶段二：服务器端 Mux 端点实现 (Server-Side Implementation)
1.  **新增 `/mux` 路由**: 在 `server.rs` 中，使用 `axum` 添加一个新的路由 `/mux`。
2.  **实现 Mux 处理器**: 编写一个新的处理器函数 `handle_mux_connection`，用于处理 `/mux` 上的 WebSocket 升级请求。
3.  **实现 `ClientSession`**: 创建 `ClientSession` 结构体来管理每个客户端的连接状态，包括其内部的 `ConnectionId` 到目标TCP流的映射。
4.  **实现消息循环**: 在 `ClientSession` 中，实现对 `ClientMuxMessage` 和二进制数据帧的完整处理逻辑（包括连接目标、转发数据、处理心跳和关闭流）。

* **产出**: 服务器具备了处理新 Mux 模式连接的能力。此时，旧的 `/forward` 和 `/proxy` 端点仍然保持不变并正常工作，不影响现有用户。

### 阶段三：客户端 Mux 模式实现 (Client-Side Implementation)
1.  **修改客户端启动逻辑**: 在 `client.rs` 中，修改启动逻辑。当以 `forward` 或 `proxy` 模式启动时，不再是单纯监听，而是首先与服务器的 `/mux` 端点建立持久的WebSocket连接。
2.  **实现 Mux 管理器**: 创建一个客户端的 Mux 管理器，负责维护与服务器的单一连接、发送心跳、以及处理重连逻辑。
3.  **重构本地连接处理**: 修改本地 `TcpListener` 的循环。当接受新连接时，通过 Mux 通道发送 `RequestNewStream` 消息，而不是创建新的 WebSocket 连接。
4.  **实现双向数据流**: 实现将本地TCP数据封装为二进制帧发送到服务器，以及将从服务器收到的二进制帧解包并写入本地TCP流的逻辑。

* **产出**: 客户端现在默认使用 Mux 模式工作。这是最大的一块改动。

### 阶段四：测试、清理与发布 (Testing, Cleanup & Release)
1.  **端到端测试**: 对 `forward` 和 `proxy` 两种模式在 Mux 架构下进行全面的端到端测试。特别关注高并发、连接中断、重连等场景。
2.  **性能基准测试**: （可选）对新旧两种模式进行性能对比测试，验证重构带来的性能提升。
3.  **移除旧代码**: 在确认新模式稳定可靠后，可以移除 `server.rs` 中的 `/forward` 和 `/proxy` 路由及其处理器，并删除 `client.rs` 中创建多条 WebSocket 连接的旧逻辑。
4.  **更新文档**: 更新 `README.md` 和其他相关用户文档，说明新的工作模式。

* **产出**: 一个更高效、更健壮、代码更整洁的 `wsstun` 版本。 