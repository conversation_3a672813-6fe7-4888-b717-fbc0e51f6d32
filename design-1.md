## **Rust 隧道程序设计方案：通过 WSS 将客户端端口转发至服务器 TCP 端口**

### **1\. 项目目标**

开发一个基于 Rust 的隧道应用程序，包含客户端和服务端两部分。

* **客户端 (Client)**：监听本地一个 TCP 端口。当有新连接时，通过 WebSocket Secure (wss://) 连接到远端 WebSocket 服务。然后，在本地 TCP 连接和 WebSocket 连接之间双向转发数据。  
* **服务端 (Server)**：运行一个 WebSocket 服务。当接收到来自客户端的 WebSocket 连接时，它会建立一个到预定义目标 TCP 服务 (如 target\_ip:target\_port) 的连接。然后，在 WebSocket 连接和目标 TCP 连接之间双向转发数据。

数据流向：  
\[本地应用\] \<-\> \[客户端TCP监听端口\] \<-\> \[隧道客户端\] \<-\> WSS \<-\> \[隧道服务端 (WebSocket服务器)\] \<-\> \[目标TCP服务端口\]

### **2\. 核心组件**

#### **2.1. 隧道客户端 (Tunnel Client)**

* **功能：**  
  1. 监听指定的本地主机和端口 (例如 127.0.0.1:local\_port)。  
  2. 接受本地 TCP 连接。  
  3. 对于每一个接受的本地 TCP 连接，建立一个到指定 WebSocket 服务器 (wss://server\_url/path) 的 WebSocket 连接。  
  4. 在本地 TCP 流和 WebSocket 流之间双向中继数据。  
  5. 处理并发的本地连接。  
  6. 处理连接错误和断开。  
  7. 支持wss://和ws://协议。  
  8. 支持将在client端参数中指定转发到服务上的目标地址ip:port  
  9. 支持通过命令行参数或配置文件进行配置。  
* **主要 Rust 库：**  
  * tokio: 异步运行时，用于网络 I/O 和并发。  
  * tokio::net::{TcpListener, TcpStream}: TCP 网络操作。  
  * tokio-tungstenite: WebSocket 客户端实现。需要启用 TLS 功能 (如 rustls-connector-webpki-roots 或 native-tls) 以支持 wss.  
  * url: 解析 WebSocket URL。  
  * futures-util: 用于流 (Stream) 和接收器 (Sink) 的辅助工具。

#### **2.2. 隧道服务端 (Tunnel Server)**

* **功能：**  
  1. 监听指定的地址和端口以接受 WebSocket 连接 (例如 0.0.0.0:wss\_port)。  
  2. 处理 ws:// 访问(反向代理如 Nginx/Caddy 已经处理了需要的wss/tls)。  
  3. 接受传入的 WebSocket 连接。  
  4. 对于每一个接受的 WebSocket 连接，建立一个到预定义目标 TCP 服务 (例如 target\_server\_ip:target\_server\_port) 的 TCP 连接。  
  5. 在 WebSocket 流和目标 TCP 流之间双向中继数据。  
  6. 处理并发的 WebSocket 连接。  
  7. 处理连接错误和断开。  
  8. 支持ipv4和ipv6。  
  9. 同一个server要支持多个client连接，建立多个到target的forward，不要串。  
  10. 支持通过命令行参数或配置文件进行配置。  
* **主要 Rust 库：**  
  * tokio: 异步运行时。  
  * tokio::net::TcpStream: TCP 网络操作（连接到目标服务）。  
  * axum (推荐) ，用于构建 WebSocket 服务器。axum 提供了良好的 WebSocket 支持和 tokio 集成。  
    * 如果使用 axum, 可以使用 axum::extract::ws::{WebSocket, WebSocketUpgrade}。  
  * futures-util: 用于流 (Stream) 和接收器 (Sink) 的辅助工具。

### **3\. 设计细节**

#### **3.1. 项目结构 (示例)**

rust-tunnel/  
├── Cargo.toml  
└── src/  
    ├── main.rs         // 程序入口，参数解析，模式选择 (client/server)  
    ├── client.rs       // 客户端逻辑模块  
    ├── server.rs       // 服务端逻辑模块  
    └── common.rs       // (可选) 共享工具、错误类型等

#### **3.2. 客户端逻辑 (client.rs)**

1. **配置加载**：  
   * 本地监听地址 (local\_addr:local\_port)。  
   * 远程 WebSocket 服务器 URL (wss\_url)。  
2. **主函数 run\_client(local\_addr, wss\_url)**：  
   * 创建 TcpListener 绑定到 local\_addr。  
   * 进入循环，异步接受 listener.accept().await 的连接。  
   * 对每个接受的 local\_tcp\_stream:  
     * 派生一个新的 tokio 任务来处理此连接。  
     * **任务内部：**  
       * 使用 tokio-tungstenite::connect\_async(wss\_url) 连接到 WebSocket 服务器。获取 ws\_stream。  
       * 错误处理：如果连接失败，记录日志并关闭 local\_tcp\_stream。  
       * 将 local\_tcp\_stream 分割为 local\_reader 和 local\_writer。  
       * 将 ws\_stream (通常是 WebSocketStream\<MaybeTlsStream\<TcpStream\>\>) 分割为 ws\_sink (发送端) 和 ws\_stream\_reader (接收端) 使用 split() 方法 (来自 futures\_util::{SinkExt, StreamExt})。  
       * 启动两个子任务进行双向数据复制：  
         1. **本地到远程**：从 local\_reader 读取数据，封装为 tungstenite::Message::Binary，通过 ws\_sink.send() 发送。  
         2. **远程到本地**：从 ws\_stream\_reader.next().await 读取 Message。如果是 Binary 或 Text (根据需要处理)，将其数据写入 local\_writer。处理 Close 消息以优雅关闭。处理 Ping 消息并回复 Pong。  
       * 使用 tokio::select\! 同时运行这两个复制任务。任何一个任务结束（由于 EOF、错误或关闭信号），都应触发另一个任务和相关连接的关闭。  
       * 确保所有资源（套接字）被正确关闭。

#### **3.3. 服务端逻辑 (server.rs)**

1. **配置加载**：  
   * WebSocket 监听地址 (bind\_addr:wss\_port)。  
2. **主函数 run\_server(bind\_addr, target\_addr, tls\_config)**：  
   * **设置 WebSocket 服务器** (以 axum 为例)：  
     * 创建一个 axum::Router 并定义一个处理 WebSocket 升级请求的路由 (例如 /tunnel)。  
     * 路由处理器会接收 WebSocketUpgrade 提取器，并调用 on\_upgrade 将连接升级为 WebSocket。  
     * 使用 axum\_server::bind(bind\_addr).serve(...) 监听 HTTP。  
     * 从header或body中读取目标 TCP 服务地址 (target\_addr:target\_port)。  
   * **WebSocket 连接处理函数 handle\_socket(mut ws: WebSocket, target\_addr)**：  
     * 当一个新的 WebSocket 连接被接受后，此函数（或闭包）被调用。  
     * 使用 TcpStream::connect(target\_addr).await 连接到目标 TCP 服务。获取 target\_tcp\_stream。  
     * 错误处理：如果连接失败，记录日志并尝试向 ws 发送一个 Close 消息。  
     * 将 target\_tcp\_stream 分割为 target\_reader 和 target\_writer。  
     * 启动两个子任务进行双向数据复制：  
       1. **WebSocket 到目标 TCP**：从 ws.recv().await 接收消息。如果是 Binary 或 Text，将数据写入 target\_writer。处理 Close 消息。  
       2. **目标 TCP 到 WebSocket**：从 target\_reader 读取数据，封装为 axum::extract::ws::Message::Binary，通过 ws.send() 发送。  
     * 使用 tokio::select\! 同时运行这两个复制任务。任何一个任务结束，都应触发另一个任务和相关连接的关闭。  
     * 确保所有资源被正确关闭。

#### **3.4. 数据转发逻辑**

* **核心**：在两个异步流之间高效、可靠地复制字节。  
* **WebSocket 消息类型**：使用 Binary 消息类型传输原始字节流。  
* **缓冲**：从一个流读取数据到缓冲区，然后写入另一个流。缓冲区大小 (例如 4KB 或 8KB) 会影响性能。  
  // 伪代码: 从 reader 读取并写入 writer  
  // async fn proxy(mut reader: R, mut writer: W) \-\> Result\<(), Error\>  
  // where R: AsyncRead \+ Unpin, W: AsyncWrite \+ Unpin {  
  //     let mut buffer \= vec\!\[0; BUFFER\_SIZE\];  
  //     loop {  
  //         let bytes\_read \= reader.read(\&mut buffer).await?;  
  //         if bytes\_read \== 0 { // EOF  
  //             break;  
  //         }  
  //         writer.write\_all(\&buffer\[..bytes\_read\]).await?;  
  //     }  
  //     Ok(())  
  // }

  对于 WebSocket，读取和写入的是 Message 对象，而不是直接的字节流。需要从 Message 中提取数据或将数据封装进 Message。  
  futures\_util::io::copy\_bidirectional 可以用于两个 AsyncRead \+ AsyncWrite 的流，但 WebSocket 流需要特殊处理消息帧。

#### **3.5. 错误处理与关闭**

* 所有 I/O 操作都可能失败，应使用 Result 和 ? 运算符进行健壮的错误处理。  
* 自定义错误类型 (使用 thiserror crate) 可以使错误处理更清晰。  
* 当隧道的一端关闭或发生错误时，应优雅地关闭另一端。  
  * 例如，如果客户端的本地 TCP 连接关闭，应向 WebSocket 服务器发送 Close 帧。  
  * 如果 WebSocket 连接断开，应关闭相应的 TCP 连接。  
* 使用 tokio::signal::ctrl\_c() 捕获 Ctrl+C信号，以实现服务器和客户端的平滑关闭。

#### **3.6. 认证与授权**

* 考虑添加认证层，请求wss://使用基本的用户名密码认证（在websokect的server上开启）  
* 在client端命令行中可以通过参数传递用户名密码

### **3.7. 增强功能**

* **心跳/保活**：使用 WebSocket Ping/Pong 帧来维持连接活跃，检测僵死连接。使用axum 支持的配置方式实现。  
* **配置文件支持**：除了命令行参数外，还支持从 TOML 加载配置。  
* **客户端自动重连**：如果 WebSocket 连接意外断开，客户端可以尝试自动重新连接。  
* 实现高性能  
* **详细的日志（分级别）和指标**：用于监控和调试。
* **所有权转移增强**: 简单的双向转发场景中，tokio::io::copy_bidirectional (如果流兼容) 或直接在 select! 中移动所有权可能更简洁，比使用Arc<Mutex<...>>更简单些。

#### **3.8. 命令行接口 (CLI)**

* 使用 clap crate 解析命令行参数，具体参数根据功能要求来支持。  
* **客户端示例命令（长命令）：**  
  rust-tunnel client \--listen 127.0.0.1:8080 \--connect wss://your.server.com/tunnel\_path \--target 127.0.0.1:80  
* **服务端示例命令（长命令）：**  
  rust-tunnel server \--listen 127.0.0.1:8001

### 