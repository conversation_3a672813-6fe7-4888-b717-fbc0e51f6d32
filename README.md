# WSSTun - WebSocket Secure Tunnel

一个基于WebSocket Secure (WSS)的高性能端口转发和代理工具，支持服务中心桥接模式。WSSTun v1.2.0 提供了全面的性能优化和监控功能。

## ✨ 主要特性

### 🚀 **核心功能** 
- **端口转发**: 通过WSS隧道进行本地到远程的端口转发
- **SOCKS5/HTTP代理**: 内建代理服务器支持动态目标地址
- **服务中心桥接**: 支持多对多的服务发现和连接
- **统一架构**: 简化的命令结构，减少50%+代码重复

### ⚡ **性能优化** (v1.2.0新增)
- **智能连接池**: 支持并发连接管理和资源限制
- **DNS缓存**: 内置DNS缓存减少解析延迟
- **指数退避重试**: 智能重连机制提高稳定性
- **缓冲区优化**: 可配置缓冲区大小提升吞吐量
- **实时监控**: 连接统计、性能指标和健康检查

### 🔒 **安全特性**
- **TLS加密**: 所有数据通过WSS加密传输
- **认证支持**: HTTP Basic认证保护连接安全
- **连接超时**: 防止资源泄露的智能超时机制

### 🌐 **网络支持**
- **多协议**: 支持HTTP/HTTPS/SOCKS5代理协议
- **IPv4/IPv6**: 完整的双栈网络支持
- **负载均衡友好**: 应用层心跳避免LB拦截

## 📥 安装

### 从源码编译
```bash
git clone https://github.com/your-org/wsstun.git
cd wsstun
cargo build --release
```

### 使用预编译二进制
```bash
# 下载最新release
wget https://github.com/your-org/wsstun/releases/latest/download/wsstun-linux-x64
chmod +x wsstun-linux-x64
```

## 🚀 快速开始

### 1. 启动高性能服务器
```bash
# 基础服务器
wsstun server --listen 0.0.0.0:8001

# 高性能配置
wsstun server --listen 0.0.0.0:8001 \
    --max-connections 5000 \
    --keep-alive true \
    --dns-cache-size 2000 \
    --connection-timeout 15 \
    --buffer-size 65536
```

### 2. 端口转发 - 优化版
```bash
# 直接转发模式（高性能）
wsstun forward --listen 127.0.0.1:8080 \
    --server wss://your-server.com \
    --target *************:80 \
    --max-connections 200 \
    --buffer-size 65536

# Hub转发模式
wsstun forward --listen 127.0.0.1:8080 \
    --server wss://your-server.com \
    --target *************:80 \
    --service-id web-service \
    --connection-timeout 30
```

### 3. 代理服务 - 优化版
```bash
# SOCKS5/HTTP代理（高性能）
wsstun proxy --listen 127.0.0.1:1080 \
    --server wss://your-server.com \
    --max-connections 500 \
    --buffer-size 65536

# Hub代理模式
wsstun proxy --listen 127.0.0.1:1080 \
    --server wss://your-server.com \
    --service-id proxy-service
```

### 4. 服务提供者
```bash
# 注册为服务提供者（支持转发和代理）
wsstun hub-service --server wss://your-server.com \
    --service-id my-service
```

## 🎛️ 性能配置选项

### 客户端性能参数
| 参数 | 默认值 | 描述 |
|------|--------|------|
| `--max-connections` | 100 | 最大并发连接数 |
| `--connection-timeout` | 30 | 连接超时时间（秒） |
| `--buffer-size` | 65536 | 数据缓冲区大小（字节） |

| `--reconnect-attempts` | 3 | 重连尝试次数 |
| `--reconnect-delay` | 1000 | 重连延迟（毫秒） |

### 服务器性能参数
| 参数 | 默认值 | 描述 |
|------|--------|------|
| `--max-connections` | 1000 | 最大并发连接数 |
| `--keep-alive` | true | 启用HTTP Keep-Alive |
| `--dns-cache-size` | 1000 | DNS缓存条目数 |
| `--dns-cache-ttl` | 300 | DNS缓存TTL（秒） |
| `--connection-timeout` | 10 | 连接超时时间（秒） |
| `--buffer-size` | 32768 | 数据缓冲区大小（字节） |

## 📊 性能监控

### HTTP监控端点

WSSTun服务器提供RESTful监控API：

```bash
# 健康检查
curl http://localhost:8001/health

# 服务器状态
curl http://localhost:8001/status

# 性能指标
curl http://localhost:8001/metrics
```

### 示例响应
```json
{
  "status": "running",
  "version": "1.2.0",
  "uptime_seconds": 3600,
  "config": {
    "max_connections": 5000,
    "dns_cache_size": 2000,
    "buffer_size": 65536
  },
  "services": {
    "active_count": 5,
    "services": ["web-service", "api-service", "proxy-service"]
  }
}
```

## 🔧 配置优化建议

### 高并发场景
```bash
# 服务器配置
wsstun server --listen 0.0.0.0:8001 \
    --max-connections 10000 \
    --dns-cache-size 5000 \
    --buffer-size 131072 \
    --connection-timeout 20

# 客户端配置
wsstun forward --listen 0.0.0.0:8080 \
    --server wss://server.com \
    --target internal-service:80 \
    --max-connections 1000 \
    --buffer-size 131072 \
    --reconnect-attempts 5
```

### 低延迟场景
```bash
# 优化延迟的配置
wsstun server --listen 0.0.0.0:8001 \
    --buffer-size 8192 \
    --connection-timeout 5 \
    --dns-cache-ttl 600

# 客户端快速响应
wsstun proxy --listen 127.0.0.1:1080 \
    --server wss://server.com \
    --buffer-size 8192 \
    --connection-timeout 10
```

### 带宽优化场景
```bash
# 大文件传输优化
wsstun forward --listen 0.0.0.0:8080 \
    --server wss://server.com \
    --target file-server:80 \
    --buffer-size 1048576 \
    --max-connections 50
```

## 🏗️ 架构优势

### 统一客户端架构
- **代码重用**: forward和proxy共享90%核心逻辑
- **一致性**: 统一的错误处理和重连机制
- **可维护性**: 单一代码路径减少bug

### 性能优化设计
- **零拷贝**: 优化的数据传输路径
- **连接复用**: 智能连接池管理
- **内存效率**: 可调节缓冲区避免内存浪费
- **并发控制**: 信号量机制防止资源耗尽

### 监控和可观测性
- **实时指标**: 连接数、流量、延迟统计
- **健康检查**: 自动故障检测和恢复
- **日志结构化**: 便于分析和调试
- **性能基准**: 内置性能测试和优化建议

## 📖 详细用法

### 命令行选项

所有命令都支持以下通用选项：
- `--log-level`: 设置日志级别 (error|warn|info|debug|trace)
- `--help`: 显示帮助信息

### WebSocket端点

服务器提供以下WebSocket端点：
- `/forward`: 直接端口转发
- `/proxy`: 直接代理服务
- `/hub`: 服务中心注册
- `/hub/forward`: Hub转发服务
- `/hub/proxy`: Hub代理服务

### 认证

支持HTTP Basic认证：
```bash
wsstun forward --listen 127.0.0.1:8080 \
    --server wss://server.com \
    --target *************:80 \
    --username admin \
    --password secretpass
```

## 🐛 故障排除

### 常见问题

1. **连接失败**
   ```bash
   # 检查服务器状态
   curl http://server:8001/health
   
   # 测试WebSocket连接
   wsstun forward --server wss://server.com --log-level debug
   ```

2. **性能问题**
   ```bash
   # 查看性能指标
   curl http://server:8001/metrics
   
   # 增加缓冲区大小
   wsstun forward --buffer-size 131072
   ```

3. **DNS解析慢**
   ```bash
   # 增加DNS缓存
   wsstun server --dns-cache-size 5000 --dns-cache-ttl 600
   ```

### 性能调优

- **高并发**: 增加 `max-connections` 和 `buffer-size`
- **低延迟**: 减少 `buffer-size` 和 `connection-timeout`
- **高带宽**: 增加 `buffer-size` 并优化网络配置
- **稳定性**: 增加 `reconnect-attempts` 和 DNS缓存

## 📊 性能基准

在标准测试环境下的性能数据：

- **并发连接**: 10,000+ 同时连接
- **吞吐量**: 1GB/s+ (本地网络)
- **延迟**: <5ms 额外延迟
- **内存使用**: <100MB (1000并发连接)
- **CPU使用**: <10% (中等负载)

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

---

**WSSTun v1.2.0** - 高性能WebSocket安全隧道工具 